import BaseGridList from "../../models/BaseGridList";
import {
    IGridColumn,
    IGridSettings,
} from "../../interfaces/general.interfaces";
import { mapOtherList } from "../common";
import { ImmobiliActions } from "../../interfaces/immobili.interface";
import { actionImmobiliButtons } from "../helperComponents";

export const getImmobiliGrids = async (t: any, actions: ImmobiliActions) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Descrizione"),
                t("Valore"),
                t("Catasto"),
                t("Classe"),
                t("Diritto"),
                t("Quota"),
                "",
            ],
            column_keys: [
                "descrizione",
                "valore",
                "tipocatasto",
                "classe",
                "diritto",
                "quota",
                "Azioni",
            ],
            column_widths: ["20%", "10%", "15%", "20%", "15%", "10%", "10%"],
            sortable: [false, true, true, true, true, true, false],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapImmobiliColumnNames(response, actions);
};

export const mapImmobiliColumnNames = (
    response: any,
    actions: ImmobiliActions
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "Azioni") {
                returnColumn.renderCell = (row: any) =>
                    actionImmobiliButtons(row, actions);
            }
            if (["Quota", "Valore"].includes(column_names[index])) {
                returnColumn.renderCell = (params: any) => {
                    const value = params.row[returnColumn.field];
                    if (typeof value === "object" && value !== null) {
                        return value.label || "";
                    }
                    return value ?? "";
                };
            }

            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};
