import {
    FormLabel,
    FormGroup,
    FormControlLabel,
    Checkbox,
    Stack,
    Radio,
    Box,
    RadioGroup,
    FormControl,
    Input,
    Tooltip,
} from "@vapor/react-material";
import { Typography } from "@vapor/react-extended";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCircleInfo } from "@fortawesome/free-solid-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { SettingsProps } from "../Settings";
import { useConfigs } from "../../../../store/ConfigStore";

export const Agenda = ({
    previousValues,
    userHasAccess,
    handleSettingsChange,
}: {
    previousValues: SettingsProps;
    userHasAccess: boolean;
    handleSettingsChange: (fieldName: keyof SettingsProps, value: any) => void;
}) => {
    const { t } = useTranslation();

    const { configs }: any = useConfigs();

    return (
        <Stack direction="column" gap={5}>
            <FormControl>
                <FormLabel component={Typography}>
                    {t(
                        "Giorni di preavviso per impegni e udienze importate dal Polisweb"
                    )}
                </FormLabel>

                <Stack direction="row" gap={1} alignItems="center">
                    <Typography variant="body2">
                        {t("Per impegni avvisa")}
                    </Typography>
                    <FormGroup>
                        <Input
                            name="deadlinesWarningDays"
                            value={previousValues.deadlinesWarningDays}
                            onChange={(e: { target: { value: any } }) =>
                                handleSettingsChange(
                                    "deadlinesWarningDays",
                                    e.target.value
                                )
                            }
                            sx={{ maxWidth: "50px" }}
                            size="small"
                        ></Input>
                    </FormGroup>
                    <Typography variant="body2">{t("Giorni Prima")}</Typography>
                </Stack>

                <Stack direction="row" gap={1} alignItems="center">
                    <Typography variant="body2">
                        {t("Per udienze avvisa")}
                    </Typography>
                    <FormGroup>
                        <Input
                            name="hearingsWarningDays"
                            value={previousValues.hearingsWarningDays}
                            onChange={(e: { target: { value: any } }) =>
                                handleSettingsChange(
                                    "hearingsWarningDays",
                                    e.target.value
                                )
                            }
                            sx={{ maxWidth: "50px" }}
                            size="small"
                        ></Input>
                    </FormGroup>
                    <Typography variant="body2">{t("Giorni Prima")}</Typography>
                </Stack>
            </FormControl>

            <FormControl>
                <FormLabel component={Typography}>
                    {t("Referente in impegni non evasi")}
                </FormLabel>
                <FormGroup>
                    <FormControlLabel
                        checked={previousValues.old_deadlines_referent === "1"}
                        control={
                            <Checkbox
                                name="old_deadlines_referent"
                                checked={
                                    previousValues.old_deadlines_referent ===
                                    "1"
                                }
                                onChange={(e: { target: { checked: any } }) =>
                                    handleSettingsChange(
                                        "old_deadlines_referent",
                                        e.target.checked ? "1" : "0"
                                    )
                                }
                            />
                        }
                        label={t("Includi referente negli impegni non evasi ")}
                    />
                </FormGroup>
            </FormControl>
            {configs.config("app.show_deadlines_conflicts_bool") && (
                <FormControl>
                    <FormLabel component={Typography}>
                        {t("Controlla conflitti impegni")}
                    </FormLabel>
                    <FormGroup>
                        <FormControlLabel
                            checked={
                                previousValues.show_deadlines_conflicts === "1"
                            }
                            control={
                                <Checkbox
                                    name="show_deadlines_conflicts"
                                    checked={
                                        previousValues.show_deadlines_conflicts ===
                                        "1"
                                    }
                                    onChange={(e: {
                                        target: { checked: any };
                                    }) =>
                                        handleSettingsChange(
                                            "show_deadlines_conflicts",
                                            e.target.checked ? "1" : "0"
                                        )
                                    }
                                />
                            }
                            label={t(
                                "Mostra i conflitti quando si crea un nuovo impegno "
                            )}
                        />
                    </FormGroup>
                </FormControl>
            )}
            <FormControl>
                <FormLabel component={Typography}>
                    {t(
                        "Referente per impegni e udienze importate dal Polisweb"
                    )}
                </FormLabel>
                <RadioGroup
                    name="polisweb_data_owner"
                    value={previousValues.polisweb_data_owner}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange(
                            "polisweb_data_owner",
                            e.target.value
                        )
                    }
                >
                    <FormControlLabel
                        value="1"
                        control={<Radio />}
                        label={t("Avvocato della pratica")}
                    />
                    <FormControlLabel
                        value="2"
                        control={<Radio />}
                        label={t("Responsabile della pratica")}
                    />
                    <FormControlLabel
                        value="3"
                        control={<Radio />}
                        label={t("Entrambi")}
                    />
                </RadioGroup>
            </FormControl>
            <FormControl>
                <Stack direction="row" gap={1} alignItems="center">
                    <FormLabel component={Typography}>
                        {t(
                            "Riassegnare impegni e udienze importate da Polisweb al responsabile"
                        )}
                    </FormLabel>
                    <Tooltip
                        title={t(
                            "Dopo il cambio responsabile, se abilitato, consente di riassegnare gli impegni e le udienze importate da Polisweb al nuovo responsabile"
                        )}
                        placement="top"
                        arrow
                    >
                        <FontAwesomeIcon
                            icon={faCircleInfo}
                            color="#0090D1"
                        ></FontAwesomeIcon>
                    </Tooltip>
                </Stack>
                <RadioGroup
                    name="move_calendar_referent"
                    value={previousValues.move_calendar_referent}
                    onChange={(e: { target: { value: any } }) =>
                        handleSettingsChange(
                            "move_calendar_referent",
                            e.target.value
                        )
                    }
                >
                    <FormControlLabel
                        value="1"
                        control={<Radio />}
                        label={t("Si")}
                    />
                    <FormControlLabel
                        value="0"
                        control={<Radio />}
                        label={t("No")}
                    />
                </RadioGroup>
            </FormControl>

            <FormControl>
                <FormLabel>
                    {t("Inizio contatore impegni passati non evasi")}
                </FormLabel>
                <Box maxWidth={200}>
                    {previousValues.startDateSearch && (
                        <DatePicker
                            label="Dal"
                            value={previousValues.startDateSearch}
                            onChange={(value: Date | null) =>
                                handleSettingsChange(
                                    "startDateSearch",
                                    value
                                )
                            }
                        />
                    )}
                </Box>
            </FormControl>

            {userHasAccess && (
                <FormControl>
                    <FormLabel component={Typography}>
                        {t(
                            "Modalità per la notifica di impegni e udienze via email"
                        )}
                    </FormLabel>

                    <FormGroup>
                        <FormControlLabel
                            checked={previousValues.deadlinesToOwners === "1"}
                            control={
                                <Checkbox
                                    name="deadlinesToOwners"
                                    checked={
                                        previousValues.deadlinesToOwners === "1"
                                    }
                                    onChange={(e: {
                                        target: { checked: any };
                                    }) =>
                                        handleSettingsChange(
                                            "deadlinesToOwners",
                                            e.target.checked ? "1" : "0"
                                        )
                                    }
                                />
                            }
                            label={t(
                                "Notifica impegni non propri anche all'avvocato titolare e ai cointestatari della pratica "
                            )}
                        />
                    </FormGroup>

                    <FormGroup>
                        <FormControlLabel
                            checked={previousValues.hearingsToOwners === "1"}
                            control={
                                <Checkbox
                                    name="hearingsToOwners"
                                    checked={
                                        previousValues.hearingsToOwners === "1"
                                    }
                                    onChange={(e: {
                                        target: { checked: any };
                                    }) =>
                                        handleSettingsChange(
                                            "hearingsToOwners",
                                            e.target.checked ? "1" : "0"
                                        )
                                    }
                                />
                            }
                            label={t(
                                "Notifica udienze anche ai cointestatari della pratica "
                            )}
                        />
                    </FormGroup>

                    <FormGroup>
                        <FormControlLabel
                            checked={
                                previousValues.deadlinesHearingsDone === "1"
                            }
                            control={
                                <Checkbox
                                    name="deadlinesHearingsDone"
                                    checked={
                                        previousValues.deadlinesHearingsDone ===
                                        "1"
                                    }
                                    onChange={(e: {
                                        target: { checked: any };
                                    }) =>
                                        handleSettingsChange(
                                            "deadlinesHearingsDone",
                                            e.target.checked ? "1" : "0"
                                        )
                                    }
                                />
                            }
                            label={t(
                                "Notifica anche udienze e impegni già evasi"
                            )}
                        />
                    </FormGroup>
                </FormControl>
            )}
        </Stack>
    );
};
