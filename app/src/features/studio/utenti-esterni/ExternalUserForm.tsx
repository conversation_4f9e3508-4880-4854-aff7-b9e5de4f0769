import {
    TextField,
    Stack,
    Select,
    MenuItem,
    Checkbox,
    FormControlLabel,
    FormControl,
    InputLabel,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { Typography } from "@vapor/react-extended";
import { SyntheticEvent } from "react";
import { useGetLawyers } from "./hooks/GetLawyers";

export const ExternalUserForm = ({
    data,
    setData,
    register,
    errors,
    isCreate,
}: {
    data: any;
    setData: any;
    register: any;
    errors: any;
    isCreate: boolean;
}) => {
    const { t } = useTranslation();

    const lawyersResponse = useGetLawyers(isCreate);
    const lawyers = lawyersResponse?.data?.searchLawyers;

    const handleAcronymChange = (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setData({
            ...data,
            accronym: event.currentTarget.value,
        });
    };

    const handleExternalCodeChange = (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setData({
            ...data,
            externalcode: event.currentTarget.value,
        });
    };

    const handleBirthPlaceChange = (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setData({
            ...data,
            birthPlace: event.currentTarget.value,
        });
    };

    const handleNameChange = (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        setData({
            ...data,
            name: event.currentTarget.value,
        });
    };

    const handleConsultantChange = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            consultant: checked,
        });
    };

    const handleExternalCanUpload = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            external_can_upload: checked,
        });
    };

    const handleUploadNotification = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            external_upload_notification: checked,
        });
    };

    const handleSendCredentials = (
        _event: SyntheticEvent<Element, Event>,
        checked: boolean
    ) => {
        setData({
            ...data,
            sendCredentials: checked,
        });
    };

    const handleActiveChange = (event: any) => {
        setData({
            ...data,
            active: event.target.value,
        });
    };

    const handleLawyerChange = (event: any) => {
        setData({
            ...data,
            lawyerCode: event.target.value,
        });
    };

    const handleBirthDayChange = (_name: string, date: string | Date) => {
        setData({
            ...data,
            birthday: date,
        });
    };

    return (
        <Stack pt={2} direction="row" gap={3}>
            <Stack direction="column" gap={4} width={250}>
                <TextField
                    label={t("Nome completo *")}
                    {...register("username", { required: true })}
                    error={!!errors.username}
                    helperText={errors.username?.message}
                    sx={{
                        "& .MuiFormHelperText-root.Mui-error": {
                            position: "absolute",
                            whiteSpace: "nowrap",
                            top: "100%",
                        },
                    }}
                />
                <TextField
                    label={t("Nome *")}
                    {...register("personalName", { required: true })}
                    error={!!errors.personalName}
                    helperText={errors.personalName?.message}
                    sx={{
                        "& .MuiFormHelperText-root.Mui-error": {
                            position: "absolute",
                            whiteSpace: "nowrap",
                            top: "100%",
                        },
                    }}
                />
                <TextField
                    label={t("Cognome *")}
                    {...register("personalSurname", { required: true })}
                    error={!!errors.personalSurname}
                    helperText={errors.personalSurname?.message}
                    sx={{
                        "& .MuiFormHelperText-root.Mui-error": {
                            position: "absolute",
                            whiteSpace: "nowrap",
                            top: "100%",
                        },
                    }}
                />
                <TextField
                    label={t("Sigla")}
                    value={data.sigla}
                    onChange={handleAcronymChange}
                />
                <DatePicker
                    name="Data di nascita"
                    label={t("Data di nascita")}
                    value={data.birthday}
                    onChange={handleBirthDayChange}
                ></DatePicker>

                <TextField
                    label={t("Codice Esterno")}
                    value={data.externalcode}
                    onChange={handleExternalCodeChange}
                />
            </Stack>
            <Stack direction="column" gap={4} width={250}>
                <TextField
                    label={t("Luogo di nascita")}
                    value={data.birthPlace}
                    onChange={handleBirthPlaceChange}
                />
                <TextField
                    label={t("Email *")}
                    {...register("Email", { required: true })}
                    error={!!errors.Email}
                    helperText={errors.Email?.message}
                    sx={{
                        "& .MuiFormHelperText-root.Mui-error": {
                            position: "absolute",
                            whiteSpace: "nowrap",
                            top: "100%",
                        },
                    }}
                />
                <TextField
                    label={t("Nome utente *")}
                    onChange={handleNameChange}
                    {...register("name", { required: true })}
                    error={!!errors.name}
                    helperText={errors.name?.message}
                    sx={{
                        "& .MuiFormHelperText-root.Mui-error": {
                            position: "absolute",
                            whiteSpace: "nowrap",
                            top: "100%",
                        },
                    }}
                />
                <TextField
                    label={t("Password")}
                    type="password"
                    {...register("password", { required: true })}
                    error={!!errors.password}
                    helperText={errors.password?.message}
                    sx={{
                        "& .MuiFormHelperText-root.Mui-error": {
                            position: "absolute",
                            whiteSpace: "nowrap",
                            top: "100%",
                        },
                    }}
                />
                <TextField
                    label={t("Conferma password")}
                    type="password"
                    {...register("passwordConfirm", { required: true })}
                    error={!!errors.passwordConfirm}
                    helperText={errors.passwordConfirm?.message}
                    sx={{
                        "& .MuiFormHelperText-root.Mui-error": {
                            position: "absolute",
                            whiteSpace: "nowrap",
                            top: "100%",
                        },
                    }}
                />
                {isCreate && lawyers && (
                    <FormControl>
                        <InputLabel>{t("Avvocato referente")}</InputLabel>
                        <Select
                            label={t("Avvocato referente")}
                            value={data.lawyerCode}
                            sx={{ width: 250 }}
                            onChange={handleLawyerChange}
                        >
                            {lawyers.map((lawyer: any) => (
                                <MenuItem value={lawyer.id}>
                                    {lawyer.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                )}
            </Stack>
            <Stack direction="column" gap={3} width={500}>
                <FormControl>
                    <InputLabel>{t("Stato")}</InputLabel>
                    <Select
                        label={t("Stato")}
                        value={data.active}
                        sx={{ width: 250 }}
                        onChange={handleActiveChange}
                    >
                        <MenuItem value={"1"}>{t("Attivo")}</MenuItem>
                        <MenuItem value={"2"}>{t("Disattivato")}</MenuItem>
                    </Select>
                </FormControl>
                <Stack direction="column" alignItems="start">
                    <Typography>{t("Commercialista")}</Typography>
                    <FormControlLabel
                        value={data.consultant}
                        checked={data.consultant}
                        onChange={handleConsultantChange}
                        control={<Checkbox />}
                        label={t(
                            "Può visualizzare invii e ricezioni e accedere alla console esterna"
                        )}
                        labelPlacement="end"
                    />
                </Stack>
                <Stack direction="column" alignItems="start">
                    <Typography>{t("Abilita documenti")}</Typography>
                    <FormControlLabel
                        value={data.external_can_upload}
                        checked={data.external_can_upload}
                        onChange={handleExternalCanUpload}
                        control={<Checkbox />}
                        label={t(
                            "Può caricare documenti nelle pratiche a cui ha accesso"
                        )}
                        labelPlacement="end"
                    />
                </Stack>
                <Stack direction="column" alignItems="start">
                    <Typography>{t("Notifica upload")}</Typography>
                    <FormControlLabel
                        value={data.external_upload_notification}
                        checked={data.external_upload_notification}
                        onChange={handleUploadNotification}
                        control={<Checkbox />}
                        label={t(
                            "Invia una mail ai soggetti interni della pratica quando l'utente carica un documento"
                        )}
                        labelPlacement="end"
                    />
                </Stack>
                {isCreate && (
                    <Stack direction="column" alignItems="start">
                        <Typography>{t("Invia credenziali")}</Typography>
                        <FormControlLabel
                            value={data.sendCredentials}
                            checked={data.sendCredentials}
                            onChange={handleSendCredentials}
                            control={<Checkbox />}
                            labelPlacement="end"
                            label=""
                        />
                    </Stack>
                )}
                <div>
                    <Typography>
                        {t(
                            'NOTA: Il "Nome utente" verrà utilizzato per accedere all\'applicazione.'
                        )}
                    </Typography>
                    {isCreate && (
                        <Typography>
                            {t(
                                'Selezionando "Invia credenziali" il "Nome utente" e la "Password" verranno inoltrate all\'indirizzo Presente nel campo "Email".'
                            )}
                        </Typography>
                    )}
                </div>
            </Stack>
        </Stack>
    );
};
