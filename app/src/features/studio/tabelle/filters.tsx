import { Box, Button, TextField } from "@vapor/react-material";
import { IFilterProps } from "./interfaces/simpleList.interface";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
export default function Filters(props: IFilterProps) {
    const { query, setQuery, filterData, fieldname, fieldLabel } = props;
    const clearAll = () => {
        setQuery((listItem: any) => ({
            ...listItem,
            [fieldname]: "",
            page: 0,
        }));
    };
    const searchWithDebounce = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
        searchWithDebounce(e);
    };

    const onDateChange = (name: string, value: Date) => {
        setQuery({
            ...query,
            [name]: value,
        });
    };
    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            {fieldLabel === "Data" ? (
                <div style={{ width: "25%" }}>
                    <DatePicker
                        label={fieldLabel}
                        name={fieldname}
                        onChange={(date: Date | null) => {
                            if (date) {
                                onDateChange(fieldname, date);
                            }
                        }}
                        value={query[fieldname] || null}
                    />
                </div>
            ) : (
                <TextField
                    label={fieldLabel !== undefined ? fieldLabel : "Nome"}
                    variant="outlined"
                    name={fieldname !== undefined ? fieldname : "searchField"}
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                    onKeyPress={handleKeywordKeyPress}
                    value={
                        query[
                            fieldname !== undefined ? fieldname : "searchField"
                        ]
                    }
                />
            )}

            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                Cerca
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => clearAll()}
            >
                Mostra tutti
            </Button>
        </Box>
    );
}
