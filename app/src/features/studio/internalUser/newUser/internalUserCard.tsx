import {
    Box,
    TextField,
    Typography,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    FormGroup,
    FormControlLabel,
    Checkbox,
    InputAdornment,
    IconButton,
} from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import VisibilityIcon from "@mui/icons-material/Visibility";
import VisibilityOffIcon from "@mui/icons-material/VisibilityOff";
import { useState } from "react";
import { typeOnlyNumbers, checkHourlyRate } from "./helpers/checkHourlyRate";

interface IInternalUserCard {
    t: any;
    newUserData: any;
    register: any;
    errors: any;
    newUserParamsData: any;
    setNewUserParamsData: React.Dispatch<React.SetStateAction<any>>;
    errorMessage: any;
}

export default function InternalUserCard(props: IInternalUserCard) {
    const {
        t,
        newUserData,
        register,
        errors,
        newUserParamsData,
        setNewUserParamsData,
        errorMessage,
    } = props;

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: value });
    };

    const handleDateChanges = (name: string, event: Date) => {
        const date = new Date(event);
        const formattedDate = date.toLocaleDateString("en-US");
        setNewUserParamsData({ ...newUserParamsData, [name]: formattedDate });
    };

    const handleCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setNewUserParamsData({ ...newUserParamsData, [name]: checked });
    };

    const [showPasswordIcon, setShowPasswordIcon] = useState({
        showPassword: false,
        showConfirmPassword: false,
    });

    const handleMouseDownPassword = (event: any) => {
        event.preventDefault();
    };

    let isEmailExisting = errorMessage?.email ? true : false;
    let isNameExisting = errorMessage?.nome ? true : false;
    let isSiglaExisting = errorMessage?.sigla ? true : false;

    return (
        <div style={{ display: "flex" }}>
            {/* Left Box */}
            <Box
                component={"section"}
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        width: 500,
                    },
                }}
            >
                <TextField
                    value={newUserParamsData.nomeutente}
                    id="nomeutente"
                    name="nomeutente"
                    label={t("Nome completo *")}
                    {...register("nomeutente")}
                    error={errors.nomeutente ? true : false}
                    helperText={errors.nomeutente?.message}
                    onChange={handleInputChanges}
                />
                <TextField
                    value={newUserParamsData.nomepersonale}
                    {...register("nomepersonale")}
                    error={errors.nomepersonale ? true : false}
                    helperText={errors.nomepersonale?.message}
                    name="nomepersonale"
                    label="Nome *"
                    onChange={handleInputChanges}
                />
                <TextField
                    value={newUserParamsData.cognomepersonale}
                    {...register("cognomepersonale")}
                    error={errors.cognomepersonale ? true : false}
                    helperText={errors.cognomepersonale?.message}
                    name="cognomepersonale"
                    label="Cognome *"
                    onChange={handleInputChanges}
                />
                <TextField
                    value={newUserParamsData.sigla}
                    name="sigla"
                    inputProps={{
                        maxLength: 5, // Set the maximum length here
                    }}
                    error={isSiglaExisting}
                    helperText={isSiglaExisting ? t("Non valido") : ""}
                    onChange={handleInputChanges}
                    label="Sigla"
                />
                <DatePicker
                    label={t("Data di nascita")}
                    value={newUserParamsData?.natoil}
                    onChange={handleDateChanges}
                    name="natoil"
                />
                <TextField
                    name="natoa"
                    value={newUserParamsData?.natoa}
                    label={t("Luogo di nascita")}
                    onChange={handleInputChanges}
                />
                <TextField
                    {...register("Email")}
                    error={!!errors.Email || isEmailExisting} // Combine existing error and isEmailExisting
                    helperText={
                        errors.Email?.message ||
                        (isEmailExisting ? t("Questa email esiste.") : "")
                    } // Show custom message if isEmailExisting is true
                    name="Email"
                    label="Email *"
                    value={newUserParamsData?.Email}
                    onChange={handleInputChanges}
                />
                <TextField
                    name="one_drive_email"
                    label="Email One Drive"
                    value={newUserParamsData?.one_drive_email}
                    onChange={handleInputChanges}
                />
                <Typography>
                    {t(
                        "NOTA: NON Utilizzare l'email utilizzata per la configurazione dell'account Master."
                    )}
                </Typography>
            </Box>

            {/* Middle Box */}
            <Box
                autoComplete="off"
                component="form"
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        width: 500,
                    },
                    "& .MuiFormControl-root": {
                        // Set width for FormControl components
                        m: 1,
                        width: 500,
                    },
                    ml: 6, // Add margin to the left side
                }}
            >
                <TextField
                    {...register("nome")}
                    error={!!errors.nome || isNameExisting}
                    helperText={
                        errors.nome?.message ||
                        (isNameExisting ? t("Questa email esiste.") : "")
                    }
                    name="nome"
                    label={t("Nome utente *")}
                    value={newUserParamsData?.nome}
                    onChange={handleInputChanges}
                />
                <TextField
                    type={showPasswordIcon.showPassword ? "text" : "password"}
                    {...register("password")}
                    error={errors.password ? true : false}
                    helperText={errors.password?.message}
                    name="password"
                    value={newUserParamsData?.password}
                    onChange={handleInputChanges}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={() =>
                                        setShowPasswordIcon({
                                            ...showPasswordIcon,
                                            showPassword:
                                                !showPasswordIcon.showPassword,
                                        })
                                    }
                                    onMouseDown={handleMouseDownPassword}
                                    edge="end"
                                >
                                    {showPasswordIcon.showPassword ? (
                                        <VisibilityIcon />
                                    ) : (
                                        <VisibilityOffIcon />
                                    )}
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                    label="Password *"
                />{" "}
                <TextField
                    type={
                        showPasswordIcon.showConfirmPassword
                            ? "text"
                            : "password"
                    }
                    {...register("passwordConfirm")}
                    error={errors.passwordConfirm ? true : false}
                    helperText={errors.passwordConfirm?.message}
                    name="passwordConfirm"
                    value={newUserParamsData?.passwordConfirm}
                    onChange={handleInputChanges}
                    InputProps={{
                        endAdornment: (
                            <InputAdornment position="end">
                                <IconButton
                                    aria-label="toggle password visibility"
                                    onClick={() =>
                                        setShowPasswordIcon({
                                            ...showPasswordIcon,
                                            showConfirmPassword:
                                                !showPasswordIcon.showConfirmPassword,
                                        })
                                    }
                                    onMouseDown={handleMouseDownPassword}
                                    edge="end"
                                >
                                    {showPasswordIcon.showConfirmPassword ? (
                                        <VisibilityIcon />
                                    ) : (
                                        <VisibilityOffIcon />
                                    )}
                                </IconButton>
                            </InputAdornment>
                        ),
                    }}
                    label={t("Conferma password *")}
                />{" "}
                <FormControl>
                    <InputLabel>{t("Tipo")}</InputLabel>
                    <Select
                        label="Tipo"
                        name="qualificautente"
                        onChange={handleInputChanges}
                        // value={newUserParamsData?.qualificautente}
                    >
                        {(newUserData?.searchQualifications || []).map(
                            (qualification: any, index: number) => (
                                <MenuItem key={index} value={qualification.id}>
                                    {qualification.nome}
                                </MenuItem>
                            )
                        )}
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>{t("Ruolo")}</InputLabel>
                    <Select
                        label={t("Ruolo")}
                        name="role"
                        onChange={handleInputChanges}
                    >
                        {(newUserData?.permissionsType || []).map(
                            (premission: any, index: number) => (
                                <MenuItem key={index} value={premission.id}>
                                    {premission.nomeruolo}
                                </MenuItem>
                            )
                        )}
                    </Select>
                </FormControl>
                <TextField
                    name="ordine"
                    label={t("Ordine")}
                    onChange={handleInputChanges}
                />
                <FormControl>
                    <InputLabel>{t("Riserva pratica a")}</InputLabel>
                    <Select
                        name="defaultgroup"
                        onChange={handleInputChanges}
                        value="0"
                    >
                        <MenuItem value="0">{t("Nessuno gruppo")}</MenuItem>
                    </Select>
                </FormControl>
                <FormControl>
                    <InputLabel>Stato</InputLabel>
                    <Select
                        name="attivo"
                        value={newUserParamsData?.attivo}
                        onChange={handleInputChanges}
                    >
                        <MenuItem value="1">{t("Attivo")}</MenuItem>
                        <MenuItem value="2">{t("Disattivato")}</MenuItem>
                    </Select>
                </FormControl>{" "}
                <FormControl>
                    <InputLabel>{t("Logo")}</InputLabel>
                    <Select
                        name="letterhead_id"
                        value="0"
                        onChange={handleInputChanges}
                    >
                        <MenuItem value="0">{t("Nessuno")}</MenuItem>
                    </Select>
                </FormControl>{" "}
            </Box>

            {/* Right Box */}
            <Box
                autoComplete="off"
                component="form"
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        // width: 500,
                    },
                    "& .MuiFormControl-root": {
                        // Set width for FormControl components
                        m: 1,
                        width: 500,
                    },
                    ml: 6, // Add margin to the left side
                }}
            >
                <FormControl>
                    <InputLabel>{t("Netlex si apre sul")}</InputLabel>
                    <Select
                        name="default_calendar"
                        onChange={handleInputChanges}
                        value={newUserParamsData.default_calendar}
                    >
                        <MenuItem value="1">
                            {t("Calendario generale")}
                        </MenuItem>
                        <MenuItem value="2">
                            {t("Calendario giornaliero")}
                        </MenuItem>
                        <MenuItem value="3">
                            {t("Calendario personale")}
                        </MenuItem>
                    </Select>
                </FormControl>{" "}
                <FormControl>
                    <InputLabel>{t("Avviso udienze")}</InputLabel>
                    <Select
                        name="extended_days_before"
                        onChange={handleInputChanges}
                    >
                        <MenuItem value="0">
                            {t("Visualizza in agenda solo X giorni prima")}
                        </MenuItem>
                        <MenuItem value="1">
                            {t("Visualizza in agenda ogni giorno")}
                        </MenuItem>
                    </Select>
                </FormControl>{" "}
                <FormControl>
                    <InputLabel>{t("Avviso impegni")}</InputLabel>
                    <Select
                        name="extended_days_before_commitments"
                        value={
                            newUserParamsData?.extended_days_before_commitments
                        }
                        onChange={handleInputChanges}
                    >
                        <MenuItem value="0">
                            {t("Visualizza in agenda solo X giorni prima")}
                        </MenuItem>
                        <MenuItem value="1">
                            {t("Visualizza in agenda ogni giorno")}
                        </MenuItem>
                    </Select>
                </FormControl>{" "}
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        alignItems: "center",
                    }}
                >
                    <TextField
                        label={t("Tar. oraria (min)")}
                        name="min_hourly_rate"
                        value={newUserParamsData?.min_hourly_rate}
                        {...register("min_hourly_rate")}
                        error={errors.min_hourly_rate ? true : false}
                        helperText={errors.min_hourly_rate?.message}
                        onChange={handleInputChanges}
                        onKeyDown={(event: any) => typeOnlyNumbers(event)}
                        onBlur={() =>
                            checkHourlyRate({
                                newUserParamsData,
                                setNewUserParamsData,
                            })
                        }
                        sx={{
                            "& .MuiInputBase-root": {
                                width: 155,
                            },
                            "& .MuiFormControl-root": {
                                width: 155,
                            },
                            width: "auto !important", // Override the width set by the Box component
                        }}
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position="start">
                                    €
                                </InputAdornment>
                            ),
                        }}
                    />
                    <TextField
                        label={t("Tar. oraria (max)")}
                        name="max_hourly_rate"
                        value={newUserParamsData?.max_hourly_rate}
                        {...register("max_hourly_rate")}
                        error={errors.max_hourly_rate ? true : false}
                        helperText={errors.max_hourly_rate?.message}
                        onChange={handleInputChanges}
                        sx={{
                            "& .MuiInputBase-root": {
                                width: 155,
                            },
                            "& .MuiFormControl-root": {
                                width: 155,
                            },
                            width: "auto !important", // Override the width set by the Box component
                        }}
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position="start">
                                    €
                                </InputAdornment>
                            ),
                        }}
                    />
                    <TextField
                        label={t("Costo orario risorsa")}
                        name="costo_risorsa"
                        value={newUserParamsData?.costo_risorsa}
                        {...register("costo_risorsa")}
                        error={errors.costo_risorsa ? true : false}
                        helperText={errors.costo_risorsa?.message}
                        onChange={handleInputChanges}
                        onKeyDown={(event: any) => typeOnlyNumbers(event)}
                        sx={{
                            "& .MuiInputBase-root": {
                                width: 155,
                            },
                            "& .MuiFormControl-root": {
                                width: 155,
                            },
                            width: "auto !important", // Override the width set by the Box component
                        }}
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position="start">
                                    €
                                </InputAdornment>
                            ),
                        }}
                    />
                </div>
                <TextField
                    name="dailyWorkload"
                    value={newUserParamsData?.dailyWorkload}
                    label={t("Ore giornaliere")}
                    onKeyDown={(event: any) => typeOnlyNumbers(event)}
                    onChange={handleInputChanges}
                />
                <FormControl>
                    <InputLabel>{t("Seniority")}</InputLabel>
                    <Select
                        label="Tipo"
                        name="seniority"
                        onChange={handleInputChanges}
                    >
                        <MenuItem key={0} value="0">
                            -
                        </MenuItem>
                        {(newUserData.hourlyItems || []).map(
                            (items: any, index: number) => (
                                <MenuItem key={index} value={items.id}>
                                    {items.descrizione}
                                </MenuItem>
                            )
                        )}
                    </Select>
                </FormControl>{" "}
                <FormControl>
                    <InputLabel>{t("N° Oggetti per pagina")}</InputLabel>
                    <Select
                        label="Tipo"
                        name="default_page_size"
                        value={newUserParamsData?.default_page_size || 1}
                        onChange={handleInputChanges}
                    >
                        {[...Array(30).keys()].map((pageNumber: number) => (
                            <MenuItem
                                key={pageNumber + 1}
                                value={pageNumber + 1}
                            >
                                {pageNumber + 1}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>{" "}
                <FormControl>
                    <FormGroup row>
                        <FormControlLabel
                            value="left"
                            control={
                                <Checkbox
                                    name="gestisce_riserva"
                                    checked={
                                        newUserParamsData?.gestisce_riserva
                                    }
                                    onChange={handleCheckboxChanges}
                                />
                            }
                            label={t("Gestione riserve")}
                            labelPlacement="start"
                        />
                        <FormControlLabel
                            value="left"
                            control={
                                <Checkbox
                                    name="sendCredentials"
                                    checked={newUserParamsData?.sendCredentials}
                                    onChange={handleCheckboxChanges}
                                />
                            }
                            label={t("Invia credenziali")}
                            labelPlacement="start"
                        />
                    </FormGroup>
                </FormControl>
                <Typography>
                    {t(`NOTA: Il "Nome utente" è utilizzato per accedere
                    all'applicazione. Selezionando "Invia credenziali" il "Nome
                    utente" e la "Password" verranno inoltrate all'indirizzo
                    Presente nel campo "Email".`)}
                </Typography>
            </Box>
        </div>
    );
}
