import React, { useState, useEffect, useRef } from "react";
import { Box, MenuItem, Select, FormControl, Stack, TextField, InputLabel, Grid, Button } from "@vapor/react-material";
import _ from "lodash";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { Tab, Tabs } from "@vapor/react-extended";
import { useTranslation } from "@1f/react-sdk";

import moment from "moment";

import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";
import { processDateValue } from "../../../helpers/dateHelper";

interface FilterProps {
    query: any;
    setQuery: any;
    reset: any;
    onSubmit?: (e: any) => void;
    data: any;
    currentStep: any;
    setCurrentStep: any;
}

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

function CustomTabPanel(props: TabPanelProps) {
    const { children, value, index, ...other } = props;

    return (
        <div role="tabpanel" hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
            {value === index && <Box sx={{ pt: 2 }}>{children}</Box>}
        </div>
    );
}

function a11yProps(index: number) {
    return {
        id: `simple-tab-${index}`,
        "aria-controls": `simple-tabpanel-${index}`
    };
}

const Filter = ({ query, setQuery, reset, onSubmit, data, currentStep, setCurrentStep }: FilterProps) => {
    const [value, setValue] = useState(0);
    // const [currentStep, setCurrentStep] = useState(0);
    const [period, setPeriod] = useState("year");
    const previousPeriod = useRef(period);

    const { t } = useTranslation();

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const handleInputChange = (e: any) => {
        const { name, value } = e.target;
        setQuery((prevQuery: any) => ({ ...prevQuery, [name]: value }));
    };

    const updateDates = (direction: any, period: any, step: any) => {
        let start: Date, end: Date;
        const base = new Date();

        if (direction === 0) {
            if (period === "month") {
                start = new Date(base.getFullYear(), base.getMonth(), 1);
                end = new Date(base.getFullYear(), base.getMonth() + 1, 0);
            } else if (period === "week") {
                const day = base.getDay();
                const diff = base.getDate() - day + (day === 0 ? -6 : 1);
                start = new Date(base.setDate(diff));
                end = new Date(start.getFullYear(), start.getMonth(), start.getDate() + 6);
            } else if (period === "year") {
                start = new Date(base.getFullYear(), 0, 1);
                end = new Date(base.getFullYear(), 11, 31);
            }
        } else {
            const startDateObj = processDateValue(query.startDateSearch) || new Date();
            const endDateObj = processDateValue(query.endDateSearch) || new Date();

            if (period === "month") {
                start = new Date(startDateObj.getFullYear(), startDateObj.getMonth() + direction, 1);
                end = new Date(startDateObj.getFullYear(), startDateObj.getMonth() + direction + 1, 0);
            } else if (period === "week") {
                start = new Date(startDateObj.getFullYear(), startDateObj.getMonth(), startDateObj.getDate() + direction * 7);
                end = new Date(endDateObj.getFullYear(), endDateObj.getMonth(), endDateObj.getDate() + direction * 7);
            } else if (period === "year") {
                start = new Date(startDateObj.getFullYear() + direction, 0, 1);
                end = new Date(endDateObj.getFullYear() + direction, 11, 31);
            }
        }

        setQuery((prevQuery: any) => ({
            ...prevQuery,
            startDateSearch: moment(start).format("DD/MM/YYYY"),
            endDateSearch: moment(end).format("DD/MM/YYYY")
        }));
        setCurrentStep(step);
    };

    const renderPeriodLabel = (period: string, currentStep: number, t: (key: string) => string) => {
        const periodLabels: Record<string, string> = {
            week: t("Settimana"),
            month: t("Mese"),
            year: t("Anno")
        };

        if (currentStep === 0) {
            return t(`Questo ${periodLabels[period].toLowerCase()}`);
        }

        return currentStep > 0 ? `${periodLabels[period]} +${currentStep}` : `${periodLabels[period]} ${currentStep}`;
    };

    useEffect(() => {
        if (previousPeriod.current !== period) {
            updateDates(0, period, 0);
            previousPeriod.current = period;
        }
    }, [period]);

    return (
        <Box component="form" gap={2} onSubmit={onSubmit} sx={{ pl: 1, pt: 3 }}>
            <Tabs value={value} onChange={handleChange} size="extraSmall" variant="standard">
                <Tab label={t("Ricerca")} {...a11yProps(0)} />
                <Tab label={t("Ricerca avanzata")} {...a11yProps(1)} />
                <Tab label={t("Ricerca per data")} {...a11yProps(2)} />
            </Tabs>
            <CustomTabPanel value={value} index={0}>
                <Stack direction="row" spacing={2}>
                    <TextField id="outlined-basic" label={t("Cliente")} name="customerSearch" variant="outlined" value={query?.customerSearch} onChange={handleInputChange} />
                    <TextField id="outlined-basic" label={t("Controparte")} name="counterpartSearch" variant="outlined" value={query?.counterpartSearch} onChange={handleInputChange} />
                    <TextField id="outlined-basic" label={t("Tag")} name="tagSearch" variant="outlined" value={query?.tagSearch} onChange={handleInputChange} />
                    <FormControl fullWidth>
                        <InputLabel id="demo-simple-select-label">{t("Sezionale")}</InputLabel>
                        <Select labelId="demo-simple-select-label" id="demo-simple-select" value={query?.sezSearch} label={t("Sezionale")} name="sezSearch" onChange={handleInputChange}>
                            <MenuItem value={-1}>{t("Tutti i sezionali")}</MenuItem>
                            {data && data?.sezionali && data?.sezionali.map((item: any) => <MenuItem value={item?.name}>{item?.name}</MenuItem>)}
                        </Select>
                    </FormControl>
                    <Button sx={{ marginTop: "28px !important" }} variant="contained" color="primary" type="submit">
                        {t("Cerca")}
                    </Button>
                    <Button sx={{ marginTop: "28px !important" }} variant="contained" color="primary" onClick={reset}>
                        {t("Mostra tutte")}
                    </Button>
                </Stack>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <Grid container spacing={2}>
                    <Grid item xs={12} sm={4} md={3}>
                        <CustomAutocomplete
                            options={data && data.judicialOffice && [{ id: "-1", nome: "Tutte le autorità" }, ...data.judicialOffice]}
                            getOptionLabel={(option: any) => option.nome}
                            filterSelectedOptions
                            sx={{ width: "auto" }}
                            onChange={(_event: any, newValue: any) => {
                                setQuery((prevQuery: any) => ({
                                    ...prevQuery,
                                    authoritySearch: newValue.id
                                }));
                            }}
                            defaultValue={{
                                id: "-1",
                                nome: "Tutte le autorità"
                            }}
                            renderOption={(props: any, option: any) => {
                                return (
                                    <li {...props} key={option.id}>
                                        {option.nome}
                                    </li>
                                );
                            }}
                            renderInput={(params: any) => <TextField {...params} name="authoritySearch" key={params?.id} placeholder="Tutte le autorità" />}
                        />
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                        <FormControl fullWidth>
                            <Select labelId="demo-simple-select-label" id="demo-simple-select" value={query?.typeSearch} name="typeSearch" onChange={handleInputChange}>
                                <MenuItem value={-1}>{t("Tutte le tipologie")}</MenuItem>
                                {data?.filesTipologies?.map((item: any) => (
                                    <MenuItem key={item?.id} value={item?.id}>
                                        {item?.nome}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                        <FormControl fullWidth>
                            <Select labelId="demo-simple-select-label" id="demo-simple-select" value={query?.statusSearch} name="statusSearch" onChange={handleInputChange}>
                                <MenuItem value={-1}>{t("Tutte gli stati")}</MenuItem>
                                {data?.filesStatus?.map((item: any) => (
                                    <MenuItem key={item?.id} value={item?.id}>
                                        {item?.nome}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                        <FormControl fullWidth>
                            <Select labelId="demo-simple-select-label" id="demo-simple-select" value={query?.sectorSearch} name="sectorSearch" onChange={handleInputChange}>
                                <MenuItem value={-1}>{t("Studi di settore")}</MenuItem>
                                <MenuItem value={1}>{t("Presente")}</MenuItem>
                                <MenuItem value={0}>{t("Non presente")}</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                        <FormControl fullWidth>
                            <Select labelId="demo-simple-select-label" id="demo-simple-select" value={query?.lawyerSearch} name="lawyerSearch" onChange={handleInputChange}>
                                <MenuItem value={-1}>{t("Tutte gli avvocati")}</MenuItem>
                                {data?.lawyers?.map((item: any) => (
                                    <MenuItem key={item?.id} value={item?.id}>
                                        {item?.nome}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                        <FormControl fullWidth>
                            <Select labelId="demo-simple-select-label" id="demo-simple-select" value={query?.objectSearch} name="objectSearch" onChange={handleInputChange}>
                                <MenuItem value={-1}>{t("Tutte gli oggeti")}</MenuItem>
                                {data?.objects?.map((item: any) => (
                                    <MenuItem key={item?.id} value={item?.id}>
                                        {item?.nome}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                        <FormControl fullWidth>
                            <Select labelId="demo-simple-select-label" id="demo-simple-select" value={query?.isArchivedSearch} name="isArchivedSearch" onChange={handleInputChange}>
                                <MenuItem value={0}>{t("Includi archiviate")}</MenuItem>
                                <MenuItem value={1}>{t("Escludi archiviate")}</MenuItem>
                                <MenuItem value={2}>{t("Solo archiviate")}</MenuItem>
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid item xs={12} sm={4} md={3}>
                        <Button variant="contained" color="primary" type="submit">
                            {t("Cerca")}
                        </Button>
                        <Button variant="contained" color="primary" onClick={reset} sx={{ ml: 2 }}>
                            {t("Mostra tutte")}
                        </Button>
                    </Grid>
                </Grid>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={2}>
                <Stack direction="row" spacing={2}>
                        <DatePicker
                            label={t("Dal")}
                            name="startDateSearch"
                            format="dd/MM/yyyy"
                            value={processDateValue(query.startDateSearch)}
                            onChange={(date: any) =>
                                setQuery((prevQuery: any) => ({
                                    ...prevQuery,
                                    startDateSearch: moment(date).format("DD/MM/YYYY")
                                }))
                            }
                            slotProps={{
                                textField: {
                                    error: false
                                }
                            }}
                        />
                        <DatePicker
                            label={t("Al")}
                            name="endDateSearch"
                            format="dd/MM/yyyy"
                            value={processDateValue(query.endDateSearch)}
                            onChange={(date: any) =>
                                setQuery((prevQuery: any) => ({
                                    ...prevQuery,
                                    endDateSearch: moment(date).format("DD/MM/YYYY")
                                }))
                            }
                            slotProps={{
                                textField: {
                                    error: false
                                }
                            }}
                        />
                    <Box sx={{ marginTop: "28px !important" }}>
                        <Button variant="contained" onClick={() => updateDates(-1, period, currentStep - 1)}>
                            <KeyboardDoubleArrowLeftIcon />
                        </Button>
                        <FormControl>
                            <Select labelId="demo-simple-select-label" id="demo-simple-select" value={period} onChange={(e: any) => setPeriod(e.target.value)} renderValue={() => renderPeriodLabel(period, currentStep, t)}>
                                <MenuItem value={"week"}>{t("Questa settimana")}</MenuItem>
                                <MenuItem value={"month"}>{t("Questo mese")}</MenuItem>
                                <MenuItem value={"year"}>{t("Questo anno")}</MenuItem>
                            </Select>
                        </FormControl>
                        <Button variant="contained" onClick={() => updateDates(1, period, currentStep + 1)}>
                            <KeyboardDoubleArrowRightIcon />
                        </Button>
                    </Box>
                    <Grid item xs={12} sm={4} md={3} sx={{ marginTop: "28px !important" }}>
                        <Button variant="contained" color="primary" type="submit">
                            {t("Cerca")}
                        </Button>
                        <Button variant="contained" color="primary" onClick={reset} sx={{ ml: 2 }}>
                            {t("Mostra tutte")}
                        </Button>
                    </Grid>
                </Stack>
            </CustomTabPanel>
        </Box>
    );
};
export default Filter;
