import { useState, useEffect ,useRef } from "react";
import { Box, MenuItem, Select, FormControl } from "@vapor/react-material";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import moment from "moment";
import { processDateValue } from "../../../helpers/dateHelper";

interface FilterProps {
    lawyers: [];
    query?: any;
    setQuery: any,
    handleInputChange?: (e: any) => void;
    handleStartDateChange?: any;
    handleEndDateChange?: any;
    onSubmit?: (e: any) => void;
}

const Filter = ({ lawyers, query, setQuery, handleInputChange, handleStartDateChange, handleEndDateChange, onSubmit }: FilterProps) => {
    const [period, setPeriod] = useState('year');
    const previousPeriod = useRef(period);

    const { t } = useTranslation();

    const updateDates = (period: any) => {
        let start: Date, end: Date;
        const base = new Date();

        if (period === 'month') {
            start = new Date(base.getFullYear(), base.getMonth(), 1);
            end = new Date(base.getFullYear(), base.getMonth() + 1, 0);
        } else if (period === 'week') {
            const day = base.getDay();
            const diff = base.getDate() - day + (day === 0 ? -6 : 1);
            start = new Date(base.setDate(diff));
            end = new Date(start.getFullYear(), start.getMonth(), start.getDate() + 6);
        } else if (period === 'year') {
            start = new Date(base.getFullYear(), 0, 1);
            end = new Date(base.getFullYear(), 11, 31);
        }

        setQuery((prevQuery: any) => ({
            ...prevQuery,
            startDashboardSearch: moment(start).format("DD/MM/YYYY"),
            endDashboardSearch: moment(end).format("DD/MM/YYYY"),
        }));
    };

    useEffect(() => {
        if (previousPeriod.current !== period) {
            updateDates(period);
            previousPeriod.current = period;
        }
    }, [period]);

    return (
        <Box component="form" display="flex" alignItems="end" gap={2} onSubmit={onSubmit} sx={{ pl: 1, pt: 3 }}>
            <DatePicker
                label="Dal"
                format="dd/MM/yyyy"
                value={processDateValue(query.startDashboardSearch)}
                onChange={handleStartDateChange}
                slotProps={{
                    textField: {
                        error: false,
                    },
                }}
            />
            <DatePicker
                label="Al"
                format="dd/MM/yyyy"
                value={processDateValue(query.endDashboardSearch)}
                onChange={handleEndDateChange}
                slotProps={{
                    textField: {
                        error: false,
                    },
                }}
            />
            <FormControl>
                <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    value={period}
                    onChange={(e: any) => setPeriod(e.target.value)}
                >
                    <MenuItem value={"week"}>{t("Questa settimana")}</MenuItem>
                    <MenuItem value={"month"}>{t("Questo mese")}</MenuItem>
                    <MenuItem value={"year"}>{t("Questo anno")}</MenuItem>
                </Select>
            </FormControl>
            <FormControl sx={{ width: 1 / 5 }} >
                <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    name="lawyerSearch"
                    value={query?.lawyerSearch || '-1'}
                    onChange={handleInputChange}
                    defaultValue='tutti'
                >
                    <MenuItem value='-1'>{t("Tutti gli avvocati")}</MenuItem>
                    {lawyers?.map((lawyer: any) => (
                        <MenuItem key={lawyer.id} value={lawyer.id}>{lawyer.nome}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControl sx={{ width: 1 / 5 }} >
                <Select
                    labelId="demo-simple-select-label"
                    id="demo-simple-select"
                    name="isArchivedSearch"
                    value={query?.isArchivedSearch || '-1'}
                    onChange={handleInputChange}
                    defaultValue='tutti'
                >
                    <MenuItem value='-1'>{t("Tutte")}</MenuItem>
                    <MenuItem value='1'>{t("Archiviate")}</MenuItem>
                    <MenuItem value='0'>{t("Non archiviate")}</MenuItem>
                </Select>
            </FormControl>
        </Box>
    )
}
export default Filter;
