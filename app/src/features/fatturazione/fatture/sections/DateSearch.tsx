import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
} from "@vapor/react-material";

import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import { TabPanel } from "../../components/TabPanel";

export const DateSearch = (props: any) => {
  const {
    value,
    onDateChange,
    defaultParams,
    onChangeInput,
    onSubmit,
    onClickReset,
    genericDate,
  } = props;

  const { t } = useTranslation();

  return (
    <TabPanel value={value} index={2}>
      <Box display="flex" alignItems="end" gap={2}>
        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
          <InputLabel id="select-label">{t("Stato")}</InputLabel>
          <Select
            labelId="select-label"
            value={defaultParams.dateTypeSearch}
            label="Stato"
            onChange={onChangeInput}
            name="dateTypeSearch"
          >
            <MenuItem value={-1}>-</MenuItem>
          </Select>
        </FormControl>
        <div style={{ width: "25%" }}>
          <DatePicker
            label="Dal:"
            value={genericDate.genericDateSearchStart}
            onChange={(value: Date | null) => {
              if (value) onDateChange("genericDateSearchStart", value);
            }}
          />
        </div>
        <div style={{ width: "25%" }}>
          <DatePicker
            label="Al:"
            value={genericDate.genericDateSearchEnd}
            onChange={(value: Date | null) => {
              if (value) onDateChange("genericDateSearchEnd", value);
            }}
          />
        </div>
        <Button
          variant="contained"
          color="primary"
          type="submit"
          onClick={onSubmit}
        >
          Cerca
        </Button>

        <Button variant="contained" color="primary" onClick={onClickReset}>
          Mostra tutti
        </Button>
      </Box>
    </TabPanel>
  );
};
