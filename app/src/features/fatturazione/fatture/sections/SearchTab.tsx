import {
    Box,
    Button,
    ButtonGroup,
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    List,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    MenuItem,
    Popover,
    Select,
    Stack,
    TextField,
    Typography,
} from "@vapor/react-material";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import BusinessCenterIcon from "@mui/icons-material/BusinessCenter";
import ScreenShareIcon from "@mui/icons-material/ScreenShare";
import AssignmentTurnedInIcon from "@mui/icons-material/AssignmentTurnedIn";
import HomeIcon from "@mui/icons-material/Home";
import ListIcon from "@mui/icons-material/List";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import { SelectMultiple } from "../../../../custom-components/SelectMultiple";
import { TabPanel } from "../../components/TabPanel";

const timeSearchValues = [
    { label: "Anno corrente", value: "year-current" },
    { label: "Anno prec.", value: "year-prev" },
    { label: "Anno succ.", value: "year-next" },
    { label: "Mese corrente", value: "month-current" },
    { label: "Mese prec", value: "month-prev" },
    { label: "Mese succ.", value: "month-next" },
];

export const SearchTab = (props: any) => {
    const {
        value,
        date,
        onDateChange,
        onTimeSearchChange,
        defaultParams,
        handleClick,
        anchorEl,
        handleClose,
        onChangeInput,
        isFattura,
        onSubmit,
        onClickReset,
        selectedValues,
        multiSelectValues,
        handleChangeMultiSelect,
        onChangeCheckbox,
        open,
        id,
        filterData,
    } = props;

    const { lawyers, sedi, office, sezionali } = filterData;

    const { t } = useTranslation();
    return (
        <TabPanel value={value} index={0}>
            <Box display="flex" alignItems="end" gap={2}>
                <div style={{ width: "25%" }}>
                    <DatePicker
                        label={t("Dal:")}
                        value={date.startDateSearch}
                        onChange={(value: Date | null) => {
                            if (value) onDateChange("startDateSearch", value);
                        }}
                    />
                </div>
                <div style={{ width: "25%" }}>
                    <DatePicker
                        label={t("Al:")}
                        value={date.endDateSearch}
                        onChange={(value: Date | null) => {
                            if (value) onDateChange("endDateSearch", value);
                        }}
                    />
                </div>
                <ButtonGroup
                    variant="contained"
                    aria-label="Basic button group"
                >
                    {timeSearchValues.map(
                        (item: { label: string; value: string }) => {
                            return (
                                <Button
                                    key={item.value}
                                    onClick={() =>
                                        onTimeSearchChange(item.value)
                                    }
                                    style={{
                                        backgroundColor:
                                            item.value ===
                                            defaultParams.timeSearch
                                                ? `lightblue`
                                                : "",
                                    }}
                                >
                                    {item.label}
                                </Button>
                            );
                        }
                    )}
                </ButtonGroup>

                <Button
                    variant="contained"
                    endIcon={<ListIcon />}
                    style={{ marginLeft: "auto" }}
                    onClick={handleClick}
                >
                    {t("Legenda")}
                </Button>
                <Popover
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                    }}
                    BackdropProps={{
                        invisible: false,
                    }}
                >
                    <Box
                        sx={{
                            width: 385,
                            px: 2,
                            py: 2,
                        }}
                    >
                        <Stack direction="row" justifyContent="space-between">
                            <Typography variant="titleSmall">
                                {t("Legenda Lista")}
                            </Typography>
                        </Stack>
                        <Box>
                            <Typography variant="body">
                                <List
                                    disablePadding
                                    sx={{
                                        width: 240,
                                    }}
                                >
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <InsertDriveFileIcon />
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={t(
                                                "Associato ad una pratica"
                                            )}
                                        />
                                    </ListItemButton>
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <BusinessCenterIcon />
                                        </ListItemIcon>
                                        <ListItemText primary={t("Acconto")} />
                                    </ListItemButton>
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <ScreenShareIcon />
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={t("XML esterno")}
                                        />
                                    </ListItemButton>
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <AssignmentTurnedInIcon />
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={t("Documento Elettronico")}
                                        />
                                    </ListItemButton>
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <InsertDriveFileIcon />
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={t(
                                                "Preavviso associato a pratica"
                                            )}
                                        />
                                    </ListItemButton>
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <Box
                                                sx={{
                                                    backgroundColor:
                                                        "rgb(229, 247, 255);",
                                                    height: 24,
                                                    width: 24,
                                                    borderRadius: "5%", // for rounded corners
                                                }}
                                            ></Box>
                                        </ListItemIcon>

                                        <ListItemText
                                            primary={t(
                                                " Preavviso interamente trasformato"
                                            )}
                                        />
                                    </ListItemButton>
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <Box
                                                sx={{
                                                    backgroundColor:
                                                        "rgba(239, 187, 90, 0.64);",
                                                    height: 24,
                                                    width: 24,
                                                    borderRadius: "5%", // for rounded corners
                                                }}
                                            ></Box>
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={t(
                                                " Preavviso parzialmente trasformato"
                                            )}
                                        />
                                    </ListItemButton>
                                    <ListItemButton>
                                        <ListItemIcon>
                                            <HomeIcon />
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={t("Studio Associato")}
                                        />
                                    </ListItemButton>
                                </List>
                            </Typography>
                        </Box>
                    </Box>
                </Popover>
            </Box>
            <Box
                display="flex"
                alignItems="end"
                gap={2}
                style={{ marginTop: "7px" }}
            >
                <TextField
                    label={t("Motivo")}
                    variant="outlined"
                    value={defaultParams.motivoSearch}
                    placeholder="Motivo:"
                    name="motivoSearch"
                    sx={{ width: 1 / 4 }}
                    onChange={onChangeInput}
                />
                <TextField
                    label={t("Destinatario")}
                    variant="outlined"
                    value={defaultParams.intestatarioSearchType}
                    placeholder="Destinatario:"
                    name="intestatarioSearchType"
                    sx={{ width: 1 / 4 }}
                    onChange={onChangeInput}
                />
                <TextField
                    label={t("R.G.(N.R.)")}
                    variant="outlined"
                    value={defaultParams.rgnSearch}
                    placeholder=""
                    name="rgnSearch"
                    sx={{ width: 1 / 12 }}
                    onChange={onChangeInput}
                />{" "}
                /
                <TextField
                    label=""
                    variant="outlined"
                    value={defaultParams.rgaSearch}
                    placeholder=""
                    name="rgaSearch"
                    sx={{ width: 1 / 12 }}
                    onChange={onChangeInput}
                />
                <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                    <InputLabel id="select-label">{t("Sede")}:</InputLabel>
                    <Select
                        labelId="select-label"
                        value={defaultParams.sede}
                        label="Tutte le sedi"
                        onChange={onChangeInput}
                        name="sede"
                    >
                        <MenuItem value={-1}>{t("Tutte le sedi")}</MenuItem>
                        {sedi?.map((sedi: any) => (
                            <MenuItem key={sedi.id} value={sedi.id}>
                                {sedi.nome}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Box>
            <Box
                display="flex"
                alignItems="end"
                gap={2}
                style={{ marginTop: "7px" }}
            >
                <FormControl sx={{ width: 1 / 4 }}>
                    <InputLabel htmlFor="grouped-native-select">
                        {t("Emittente")}:
                    </InputLabel>
                    <Select
                        value={defaultParams.emittenteSearch}
                        id="grouped-native-select"
                        label="Grouping"
                        onChange={onChangeInput}
                        name="emittenteSearch"
                        native
                    >
                        <option aria-label={t("Tutti")} value="-10">
                            {" "}
                            {t("Tutti")}
                        </option>
                        <optgroup label="Avvocati">
                            {lawyers?.map((lawyer: any) => (
                                <option key={lawyer.id} value={lawyer.id}>
                                    {lawyer.nome}
                                </option>
                            ))}
                        </optgroup>
                        <optgroup label={t("Studi associati")}>
                            <option aria-label="Tutti" value="-10">
                                {" "}
                                {t("Tutti gli studi")}
                            </option>
                            {office?.map((office: any) => (
                                <option key={office.id} value={office.id}>
                                    {office.nome}
                                </option>
                            ))}
                        </optgroup>
                    </Select>
                </FormControl>

                <SelectMultiple
                    selectedValues={selectedValues}
                    label={`${t("Tipologia")}:`}
                    options={multiSelectValues}
                    onChange={handleChangeMultiSelect}
                />

                {!isFattura ? (
                    <>
                        <TextField
                            label={t("Progressivo:")}
                            variant="outlined"
                            value={defaultParams.progressivoSearch}
                            placeholder=""
                            name="progressivoSearch"
                            sx={{ width: 1 / 12 }}
                            onChange={onChangeInput}
                        />
                        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                            <InputLabel id="select-label">
                                {t("Sezionale")}:
                            </InputLabel>
                            <Select
                                labelId="select-label"
                                value={defaultParams.sezionaleSearch}
                                label="Tutti i sezionali"
                                onChange={onChangeInput}
                                name="sezionaleSearch"
                            >
                                <MenuItem value={-1}>
                                    {t("Tutti i sezionali")}
                                </MenuItem>

                                {sezionali?.map((sezionali: any) => (
                                    <MenuItem
                                        key={sezionali.id}
                                        value={sezionali.id}
                                    >
                                        {sezionali.nome}-{sezionali.formato}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </>
                ) : (
                    <>
                        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                            <InputLabel id="select-label">
                                {t("Stato")}:
                            </InputLabel>
                            <Select
                                labelId="select-label"
                                value={defaultParams.statusSearch}
                                label="Tutti gli stati"
                                onChange={onChangeInput}
                                name="statusSearch"
                            >
                                <MenuItem value={-1}>
                                    {t("Tutti gli stati")}
                                </MenuItem>

                                <MenuItem key="1" value="1">
                                    {t("Da incassare")}
                                </MenuItem>
                                <MenuItem key="2" value="2">
                                    {t("Incassate")}
                                </MenuItem>
                            </Select>
                        </FormControl>
                        <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                            <InputLabel id="select-label">
                                {t("Stato invio")}:
                            </InputLabel>
                            <Select
                                labelId="select-label"
                                value={defaultParams.sentSearch}
                                label={t("Tutti gli stati")}
                                onChange={onChangeInput}
                                name="sentSearch"
                            >
                                <MenuItem value={-1}>-</MenuItem>

                                <MenuItem key="1" value="1">
                                    {t("Inviate")}
                                </MenuItem>
                                <MenuItem key="2" value="2">
                                    {t("Non inviate")}
                                </MenuItem>
                            </Select>
                        </FormControl>
                    </>
                )}
            </Box>
            {isFattura && (
                <Box
                    display="flex"
                    alignItems="end"
                    gap={2}
                    style={{ marginTop: "7px" }}
                >
                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <InputLabel id="select-label">
                            {t("Trasformate da preavviso")}:
                        </InputLabel>
                        <Select
                            labelId="select-label"
                            value={defaultParams.statoTrasformataSearch}
                            label="Tutte"
                            onChange={onChangeInput}
                            name="statoTrasformataSearch"
                        >
                            <MenuItem value="-1">{t("Tutte")}</MenuItem>
                            <MenuItem key="1" value="1">
                                {t("Si")}
                            </MenuItem>
                            <MenuItem key="2" value="2">
                                {t("No")}
                            </MenuItem>
                        </Select>
                    </FormControl>
                    <FormControlLabel
                        sx={{ width: 1 / 12 }}
                        control={
                            <Checkbox
                                onChange={onChangeCheckbox}
                                color="primary"
                                name="advanceSearch"
                                checked={!!defaultParams.advanceSearch}
                            />
                        }
                        label={t("Solo Acconti")}
                    />
                    <FormControlLabel
                        sx={{ width: 1 / 12 }}
                        control={
                            <Checkbox
                                onChange={onChangeCheckbox}
                                color="primary"
                                name="detraiNoteCredito"
                                checked={!!defaultParams.detraiNoteCredito}
                            />
                        }
                        label={t("Detrai NC")}
                    />
                    <TextField
                        label={t("Progressivo")}
                        variant="outlined"
                        value={defaultParams.progressivoSearch}
                        placeholder=""
                        name="progressivoSearch"
                        sx={{ width: 1 / 12 }}
                        onChange={onChangeInput}
                    />
                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <InputLabel id="select-label">
                            {t("Sezionale")}
                        </InputLabel>
                        <Select
                            labelId="select-label"
                            value={defaultParams.sezionaleSearch}
                            label={t("Tutti i sezionali")}
                            onChange={onChangeInput}
                            name="sezionaleSearch"
                        >
                            <MenuItem value={-1}>
                                {t("Tutti i sezionali")}
                            </MenuItem>

                            {sezionali?.map((sezionali: any) => (
                                <MenuItem
                                    key={sezionali.id}
                                    value={sezionali.id}
                                >
                                    {sezionali.nome}-{sezionali.formato}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Box>
            )}
            <Box
                display="flex"
                alignItems="end"
                gap={2}
                style={{ marginTop: "7px" }}
            >
                <Button
                    variant="contained"
                    sx={{ width: 1 / 10 }}
                    color="primary"
                    type="submit"
                    onClick={onSubmit}
                >
                    {t("Cerca")}
                </Button>
                <Button
                    variant="contained"
                    color="primary"
                    sx={{ width: 1 / 10 }}
                    onClick={onClickReset}
                >
                    {t("Mostra tutti")}
                </Button>
            </Box>
        </TabPanel>
    );
};
