import {
  Box,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Grid,
} from "@vapor/react-material";
import { useFiltraSospesiHook } from "../hooks/useFiltraSospesiHook";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";

export const FiltraSospesiFilters = (props: any) => {
  const { params: defaultParams, onChangeFunctions, lawyers, date } = props;
  const { onChangeInput, onClickReset, onSubmit, onDateChange } =
    onChangeFunctions;

  const { t } = useFiltraSospesiHook();

  return (
    <>
      <Box display="flex" alignItems="end" gap={2} sx={{ pt: 1 }}>
        <Grid md={3}>
          <DatePicker
            label={t("Dal") + ":"}
            value={date.startDate}
            onChange={(value: Date | null) => {
              if (value) onDateChange("startDate", value);
            }}
          />
        </Grid>
        <Grid md={3}>
          <DatePicker
            label={t("Al") + ":"}
            value={date.endDate}
            onChange={(value: Date | null) => {
              if (value) onDateChange("endDate", value);
            }}
          />
        </Grid>
        <Grid md={3}>
          <FormControl variant="outlined">
            <TextField
              label={t("Cliente") + ":"}
              variant="outlined"
              value={defaultParams.searchField}
              placeholder="Cliente"
              name="searchField"
              onChange={onChangeInput}
            />
          </FormControl>
        </Grid>

        <FormControl variant="outlined" sx={{ width: 1 / 6 }}>
          <InputLabel id="select-label">{t("Emittente")}:</InputLabel>
          <Select
            labelId="select-label"
            value={defaultParams.emittente}
            label="Tutti gli avvocati"
            onChange={onChangeInput}
            name="emittente"
          >
            <MenuItem value={-1}>{t("Tutti gli avvocati")}</MenuItem>
            {lawyers?.map((lawyer: any) => (
              <MenuItem key={lawyer.id} value={lawyer.id}>
                {lawyer.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl variant="outlined" sx={{ width: 1 / 5 }}>
          <InputLabel id="select-label">{t("Avvocato pratica:")}</InputLabel>
          <Select
            labelId="select-label"
            value={defaultParams.avvocatoSearch}
            label={t("Tutti gli avvocati")}
            onChange={onChangeInput}
            name="avvocatoSearch"
          >
            <MenuItem value={-1}>{t("Tutti gli avvocati")}</MenuItem>
            {lawyers?.map((lawyer: any) => (
              <MenuItem key={lawyer.id} value={lawyer.id}>
                {lawyer.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        <FormControl variant="outlined" sx={{ width: 1 / 5 }}>
          <InputLabel id="select-label">{t("Tipo documento:")}</InputLabel>
          <Select
            labelId="select-label"
            value={defaultParams.tipo}
            label={t("Tutti")}
            onChange={onChangeInput}
            name="tipo"
          >
            <MenuItem value={-1}>{t("Tutti")}</MenuItem>
            <MenuItem value={0}>{t("Fatture")}</MenuItem>
            <MenuItem value={6}>{t("Fatture elettroniche")}</MenuItem>
            <MenuItem value={1}>{t("Note di credito")}</MenuItem>
            <MenuItem value={2}>{t("Preavvisi di parcella")}</MenuItem>
            <MenuItem value={3}>{t("Fatture d'acquisto")}</MenuItem>
          </Select>
        </FormControl>
      </Box>
      <Box display="flex" alignItems="end" gap={2} style={{ marginTop: "7px" }}>
        <FormControl variant="outlined" sx={{ width: 1 / 5 }}>
          <InputLabel id="select-label">{t("Stato")}:</InputLabel>
          <Select
            labelId="select-label"
            value={defaultParams.stato}
            label="Tutti"
            onChange={onChangeInput}
            name="stato"
          >
            <MenuItem value={-1}>{t("Tutti")}</MenuItem>
            <MenuItem value={1}>{t("Incassata")}</MenuItem>
            <MenuItem value={2}>{t("Da incassare")}</MenuItem>
          </Select>
        </FormControl>
        <TextField
          label={t("Giorni anzianità del credito:")}
          variant="outlined"
          value={defaultParams.searchDaysOld}
          placeholder=""
          name="searchDaysOld"
          sx={{ width: 1 / 9 }}
          onChange={onChangeInput}
        />
        <TextField
          label={t("R.G.(N.R.)") + ":"}
          variant="outlined"
          value={defaultParams.rgn}
          placeholder=""
          name="rgn"
          sx={{ width: 1 / 12 }}
          onChange={onChangeInput}
        />{" "}
        /
        <TextField
          label=""
          variant="outlined"
          value={defaultParams.rga}
          placeholder=""
          name="rga"
          sx={{ width: 1 / 12 }}
          onChange={onChangeInput}
        />
        <Button
          variant="contained"
          sx={{ width: 1 / 10 }}
          color="primary"
          type="submit"
          onClick={onSubmit}
        >
          {t("Cerca")}
        </Button>
        <Button
          variant="contained"
          color="primary"
          sx={{ width: 1 / 10 }}
          onClick={onClickReset}
        >
          {t("Mostra tutti")}
        </Button>
      </Box>
      <Box
        display="flex"
        alignItems="end"
        gap={2}
        style={{ marginTop: "7px" }}
      ></Box>
    </>
  );
};
