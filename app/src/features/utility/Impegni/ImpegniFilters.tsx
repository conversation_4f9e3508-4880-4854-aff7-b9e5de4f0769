import { useTranslation } from "@1f/react-sdk";
import {
    Button,
    FormControl,
    MenuItem,
    Select,
    Stack,
} from "@vapor/react-material";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { IimpegniFilters, User } from "../../../interfaces/impegni.interface";
import { ALL_HOLDERS, SearchQuery, TYPOLOGIES } from "./constants";
import { processDateValue } from "../../../helpers/dateHelper";

export const ImpegniFilters = ({
    handleSearchChange,
    params,
    setParams,
    users,
}: IimpegniFilters) => {
    const { t } = useTranslation();

    return (
        <Stack
            direction="row"
            alignItems="end"
            gap={2}>
            <DatePicker
                value={processDateValue(params.startDate)}
                label={t("Dal")}
                onChange={(date: Date | null) => {
                    handleSearchChange("startDate")({ target: { value: date } });
                }}
            />
            <DatePicker
                value={processDateValue(params.endDate)}
                label={t("al")}
                onChange={(date: Date | null) => {
                    handleSearchChange("endDate")({ target: { value: date } });
                }}
            />
            <FormControl sx={{ minWidth: 300 }}>
                <Select
                    value={params.person}
                    onChange={handleSearchChange("person")}>
                    {[ALL_HOLDERS, ...users].map((user: User) => (
                        <MenuItem
                            key={user.id}
                            value={user.id}>
                            {t(user.nome)}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            <FormControl sx={{ minWidth: 300 }}>
                <Select
                    value={params.tipologia}
                    onChange={handleSearchChange("tipologia")}>
                    {TYPOLOGIES.map((type: any) => (
                        <MenuItem value={type.value}>{t(type.label)}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            <Button onClick={() => setParams(SearchQuery)}>
                {t("Annulla ricerca")}
            </Button>
        </Stack>
    );
};
