import { Too<PERSON>bar, VaporPage } from "@vapor/react-custom";
import PageTitle from "../../../custom-components/PageTitle";
import { useTranslation } from "@1f/react-sdk";
import { Typography } from "@vapor/react-extended";
import { <PERSON>, <PERSON><PERSON>, Divider, Stack } from "@vapor/react-material";
import { useEffect, useRef, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faUpload, faDownload } from "@fortawesome/pro-regular-svg-icons";
import usePostCustom from "../../../hooks/usePostCustom";
import useGetCustom from "../../../hooks/useGetCustom";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import ToastNotification from "../../../custom-components/ToastNotification";
import { format } from "date-fns";
import { saveFile } from "../../../utilities/utils";
import SpinnerButton from "../../../custom-components/SpinnerButton";

interface ModelResponse {
    name: string;
    type: string;
    tmp_name: string;
    error: number;
    size: number;
}

const UploadSection = ({
    selectedFile,
    fileType,
    onUploaded,
    onSelectFile,
    t,
}: {
    selectedFile: File | null;
    fileType: string | undefined;
    onUploaded: (file: ModelResponse) => void;
    onSelectFile: () => void;
    t: (key: string) => string;
}) => {
    const uploadFileRequest = usePostCustom("/import/model");

    useEffect(() => {
        if (uploadFileRequest.hasLoaded) {
            onUploaded(uploadFileRequest.data.file);
        }
    }, [uploadFileRequest.hasLoaded]);

    const handleUpload = () => {
        if (selectedFile) {
            const formData = new FormData();
            formData.append("file", selectedFile);
            if (fileType) {
                formData.append("type", fileType);
            }
            formData.append("import", "1");
            uploadFileRequest.doFetch(true, formData);
        }
    };

    return (
        <Stack gap={2}>
            <Box>
                <Button
                    startIcon={<FontAwesomeIcon icon={faUpload} />}
                    onClick={onSelectFile}>
                    {t("Select file")}
                </Button>
                {selectedFile && (
                    <Typography
                        variant="body2"
                        fontWeight="bold"
                        sx={{ mt: 1 }}>
                        {selectedFile.name}
                    </Typography>
                )}
            </Box>
            <Box>
                <SpinnerButton
                    label={t("Upload")}
                    variant="outlined"
                    onClick={handleUpload}
                    disabled={!selectedFile}
                    isLoading={uploadFileRequest.loading}
                />
            </Box>
        </Stack>
    );
};

export const ImportModel = () => {
    const { t } = useTranslation();

    const fileInputRef = useRef<HTMLInputElement>(null);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [hasUploaded, setHasUploaded] = useState(false);
    const [selectedLogDate, setSelectedLogDate] = useState<Date | null>(
        new Date()
    );
    const [fileData, setFileData] = useState<ModelResponse>();

    const [missingLog, setMissingLog] = useState(false);
    const [imported, setImported] = useState(false);
    const [fileSaved, setFileSaved] = useState(false);

    const importRequest = useGetCustom("import/import");
    const downloadLogRequest = useGetCustom("import/download-log");
    const excecuteRequest = usePostCustom("import/execute");

    const [uploadKey, setUploadKey] = useState(Date.now());

    const handleExcute = () => {
        if (fileData) {
            excecuteRequest.doFetch(true, {
                filePath: fileData.tmp_name,
                nome_file: fileData.name,
            });
        }
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (files && files.length > 0) {
            setSelectedFile(files[0]);
        }
    };

    const fileType = selectedFile?.name?.split(".").pop();

    useEffect(() => {
        importRequest.doFetch(true);
    }, []);

    useEffect(() => {
        if (downloadLogRequest.data) {
            if (downloadLogRequest.data.error === false) {
                saveFile({
                    data: downloadLogRequest.data.content,
                    fileName: `Import${
                        selectedLogDate
                            ? "_" + format(selectedLogDate, "dd_MM_yyyy")
                            : ""
                    }`,
                    type: "log",
                });
            } else {
                setMissingLog(true);
            }
        }
    }, [downloadLogRequest.data]);

    const handleDownloadLog = () => {
        if (selectedLogDate !== null) {
            downloadLogRequest.doFetch(true, {
                date: format(selectedLogDate, "dd/MM/yyyy"),
            });
        }
    };

    const handleReset = () => {
        setSelectedFile(null);
        setHasUploaded(false);
        setSelectedLogDate(new Date());
        if (fileInputRef.current) {
            fileInputRef.current.value = "";
        }
        setMissingLog(false);
        setImported(false);
        setFileSaved(false);
        setUploadKey(Date.now());
    };

    const handleMissingWarning = () => {
        setMissingLog(false);
    };

    const handleImportWarnigng = () => {
        setImported(false);
    };

    const handleDownloadWarning = () => {
        setFileSaved(false);
    };

    const permissionData = {
        isSubdomainClaudio:
            importRequest.data?.provisioningRow.subdomain_name === "claudio",
        isSubdomainOpenFiber:
            importRequest.data?.provisioningRow.subdomain_name === "openfiber",
        isSubdomainatmspa:
            importRequest.data?.provisioningRow.subdomain_name === "atmspa",
        isExternalUser: importRequest.data?.isExternalUser,
        permissions: importRequest.data?.permissions !== null,
        permissionsUpload: importRequest.data?.permissions[13]["u"],
    };

    const permissions = {
        showForm: !permissionData.isExternalUser,
        showLabel1:
            !permissionData.isExternalUser &&
            (permissionData.isSubdomainClaudio ||
                permissionData.isSubdomainOpenFiber),
        showLabel2:
            !permissionData.isExternalUser && permissionData.isSubdomainatmspa,
        showUpload1:
            !permissionData.isExternalUser &&
            (permissionData.isSubdomainClaudio ||
                permissionData.isSubdomainOpenFiber ||
                permissionData.isSubdomainatmspa),
        showUpload2:
            !permissionData.isExternalUser &&
            permissionData.permissions &&
            permissionData.permissionsUpload,
    };

    const showSecondPart = selectedFile && hasUploaded && permissions.showForm;

    const handleSelectFile = () => {
        fileInputRef.current?.click();
    };

    const handleChange = (_event: string, date: Date) => {
        setSelectedLogDate(date);
    };

    const onUploadSuccess = (file: ModelResponse) => {
        setHasUploaded(true);
        setFileData(file);
    };

    return (
        <VaporPage
            contentToolbar={
                <Toolbar
                    contentRight={
                        <Stack
                            gap={1}
                            direction="row">
                            <Button onClick={handleReset}>{t("Annulla")}</Button>
                            <Button
                                variant="outlined"
                                onClick={handleExcute}>
                                {t("Conferma")}
                            </Button>
                        </Stack>
                    }></Toolbar>
            }>
            <ToastNotification
                setShowNotification={handleMissingWarning}
                severity="warning"
                showNotification={missingLog}
                text={t("Log di importazione non presente")}
            />
            <ToastNotification
                setShowNotification={handleImportWarnigng}
                severity="success"
                showNotification={imported}
                text={t("Import Completato con Successo!")}
            />
            <ToastNotification
                setShowNotification={handleDownloadWarning}
                severity="success"
                showNotification={fileSaved}
                text={t("Log di importazione scaricato")}
            />

            <PageTitle
                title={t("Gestione Imports")}
                showBackButton={false}
            />
            <VaporPage.Section>
                <Stack gap={6}>
                    <Box>
                        <Typography variant="titleSmall">
                            {t("Fase 1")}
                        </Typography>
                        <Stack
                            direction="row"
                            gap={0.5}>
                            <Typography>
                                {t(
                                    "Caricare il file da importare, i formati accettati sono"
                                )}
                            </Typography>
                            <Typography fontWeight="bold">.xlsx</Typography>
                            <Typography>,</Typography>
                            <Typography fontWeight="bold">.csv</Typography>
                            <Typography>{t("e")}</Typography>
                            <Typography fontWeight="bold">.txt</Typography>
                        </Stack>

                        <input
                            type="file"
                            accept=".xlsx,.csv,.txt"
                            ref={fileInputRef}
                            onChange={handleFileChange}
                            style={{ display: "none" }}
                        />
                    </Box>
                    {showSecondPart ? (
                        <Stack
                            direction="row"
                            gap={0.5}>
                            <Typography variant="body2">
                                {t("File selezionato: ")}
                            </Typography>
                            <Typography
                                variant="body2"
                                fontWeight="bold">
                                {fileData?.name}
                            </Typography>
                        </Stack>
                    ) : (
                        <>
                            <UploadSection
                                key={uploadKey}
                                selectedFile={selectedFile}
                                fileType={fileType}
                                onUploaded={onUploadSuccess}
                                onSelectFile={handleSelectFile}
                                t={t}
                            />
                        </>
                    )}
                    {showSecondPart && (
                        <>
                            <Divider variant="middle" />
                            <Box>
                                <Typography variant="titleSmall">
                                    {t("Fase 2")}
                                </Typography>
                                <Typography>
                                    {t(
                                        `Il file è stato riconosciuto e validato! Confermare di voler importare il file.`
                                    )}
                                </Typography>
                            </Box>
                            <Stack
                                alignItems="end"
                                direction="row"
                                gap={2}>
                                <DatePicker
                                    name="date"
                                    label={
                                        permissions.showLabel1
                                            ? t("Testata e righe")
                                            : permissions.showUpload2
                                            ? t("Archivio e Agenda")
                                            : t("Scegli File")
                                    }
                                    value={selectedLogDate}
                                    onChange={handleChange}
                                />
                                {permissions.showUpload2 && (
                                    <Button
                                        variant="outlined"
                                        onClick={handleDownloadLog}
                                        disabled={downloadLogRequest.loading}
                                        startIcon={
                                            <FontAwesomeIcon
                                                icon={faDownload}
                                            />
                                        }>
                                        {t("Scarica")}
                                    </Button>
                                )}
                            </Stack>
                        </>
                    )}
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
};
