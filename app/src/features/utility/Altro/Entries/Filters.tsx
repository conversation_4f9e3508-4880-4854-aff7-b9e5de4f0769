import { useTranslation } from "@1f/react-sdk";
import {
    Button,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
} from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";

import { DEFAULT_ENTRIES_QUERY, QUALIFICATION_OPTIONS } from "./constants";
import useGetCustom from "../../../../hooks/useGetCustom";
import { useEffect } from "react";

export const Filters = ({
    handleQueryChange,
    query,
    setQuery,
}: {
    handleQueryChange: (key: any) => (event: any) => void;
    query: any;
    setQuery: any;
}) => {
    const { t } = useTranslation();
    const { doFetch, hasLoaded, data } = useGetCustom("entries");

    useEffect(() => {
        doFetch(true);
    }, []);

    return (
        <Stack
            direction="row"
            gap={2}
            alignItems="end">
            <DatePicker
                value={query.startDate}
                onChange={(date: Date | null) => {
                    handleQueryChange("startDate")({ target: { value: date } });
                }}
                label={t("Dal")}
            />
            <DatePicker
                onChange={(date: Date | null) => {
                    handleQueryChange("endDate")({ target: { value: date } });
                }}
                value={query.endDate}
                label={t("al")}
            />

            <FormControl sx={{ minWidth: 300 }}>
                <InputLabel>{t("Qualifica")}</InputLabel>
                <Select
                    value={query.qualification}
                    onChange={handleQueryChange("qualification")}>
                    {QUALIFICATION_OPTIONS.map(option => (
                        <MenuItem
                            key={option.label}
                            value={option.value}>
                            {t(option.label)}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            <FormControl sx={{ minWidth: 300 }}>
                <InputLabel>{t("Utente")}</InputLabel>
                <Select
                    value={query.user}
                    onChange={handleQueryChange("user")}>
                    {hasLoaded &&
                        [
                            { id: -1, nome: "Tutti gli utenti" },
                            ...data.people,
                        ].map((user: any) => (
                            <MenuItem
                                value={user.id}
                                key={user.id}>
                                {user.nome}
                            </MenuItem>
                        ))}
                </Select>
            </FormControl>

            <TextField
                value={query.operation}
                onChange={handleQueryChange("operation")}
                sx={{ width: 300 }}
                label={t("Azione")}
            />

            <Button
                variant="outlined"
                onClick={() => setQuery({ ...query, search: query.search + 1 })}>
                {t("Cerca")}
            </Button>
            <Button
                variant="outlined"
                onClick={() => {
                    setQuery(DEFAULT_ENTRIES_QUERY);
                }}>
                {t("Mostra tutti")}
            </Button>
        </Stack>
    );
};
