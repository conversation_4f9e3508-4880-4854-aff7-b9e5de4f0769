import { useTranslation } from "@1f/react-sdk";
import { <PERSON><PERSON>, <PERSON><PERSON>, TextField } from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";

import { Dispatch, SetStateAction } from "react";
import { ILogPecQuery } from "./interfaces";
import { defaultLogPecQuery } from "./constants";

export const Filters = ({
    handleQueryChange,
    setQuery,
    setReload,
    query,
}: {
    handleQueryChange: any;
    setQuery: (value: SetStateAction<ILogPecQuery>) => void;
    setReload: Dispatch<SetStateAction<boolean>>;
    query: ILogPecQuery;
}) => {
    const { t } = useTranslation();

    const handleShowAll = () => {
        setQuery(defaultLogPecQuery);
        setReload(true);
    };

    const handleReload = () => {
        setReload(true);
    };

    return (
        <Stack
            direction="row"
            gap={2}
            alignItems="end">
            <TextField
                value={query.mittenteSearch}
                sx={{ width: 300 }}
                label={t("Mittente")}
                onChange={handleQueryChange("mittenteSearch")}
            />
            <TextField
                value={query.inviataSearch}
                sx={{ width: 300 }}
                label={t("Inviato da")}
                onChange={handleQueryChange("inviataSearch")}
            />
            <DatePicker
                onChange={(date: Date | null) => {
                    handleQueryChange("startDate")({ target: { value: date } });
                }}
                value={query.startDate}
                label={t("Dal")}
            />
            <DatePicker
                onChange={(date: Date | null) => {
                    handleQueryChange("endDate")({ target: { value: date } });
                }}
                value={query.endDate}
                label={t("al")}
            />
            <Button
                variant="outlined"
                onClick={handleReload}>
                {t("Cerca")}
            </Button>
            <Button
                variant="outlined"
                onClick={handleShowAll}>
                {t("Mostra tutti")}
            </Button>
        </Stack>
    );
};
