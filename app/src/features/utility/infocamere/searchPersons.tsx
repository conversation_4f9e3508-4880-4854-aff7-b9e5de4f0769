import React from "react";
import { useEffect, useState } from "react";
import { VaporPage, Title } from "@vapor/react-custom";
import { Tabs, Tab } from "@vapor/react-extended";
import { TabContext, TabPanel } from "@vapor/react-lab";
import {
    Box,
    Button,
    Stack,
    TextField,
    Radio,
    InputLabel,
    Chip,
    Select,
    Typography,
    FormControl,
    MenuItem,
    OutlinedInput,
    Breadcrumbs,
} from "@vapor/react-material";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import AngleRight from "@vapor/react-icons/AngleRight";
import useGetCustom from "../../../hooks/useGetCustom";
import { useNavigate, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Spinner from "../../../custom-components/Spinner";
import { IDataType, IAmounts } from "./interfaces/infocamere.interface";
import { currencyFormat } from "../../../helpers/currencyFormat";
import PriceChoice from "./helpers/priceChoiceSection";

const dataType: Array<IDataType> = [
    { value: "nominativo", name: "Nominativo" },
    { value: "codicefiscale", name: "Cod. Fiscale" },
];

const personType: Array<IDataType> = [
    { value: "fisica", name: "Fisica" },
    { value: "giuridica", name: "Giuridica" },
];

export default function SearchPersons() {
    const navigate = useNavigate();
    const { t } = useTranslation();
    const [tabs, setTabs] = useState<string>("1");
    const [provinces, setProvinces] = useState<any[]>([]);
    const [selectProvinces, setSelectProvinces] = useState<string[]>([]);
    const [personTypeSelected, setPersonTypeSelected] = useState<string>(
        personType[0].value
    );
    const [dataTypeSelected, setDataTypeSelected] = useState<string>(
        dataType[0].value
    );
    const [insufficientCredit, setInsufficientCredit] =
        useState<boolean>(false);
    const [selectedCredit, setSelectedCredit] =
        useState<string>("studioCredit");
    const [inputLabelName, setInputLabelName] = useState<string>("Nominativo");
    const [amounts, setAmounts] = useState<IAmounts>({
        studioAmount: 0,
        userAmount: 0,
        price: 0,
        total: 0,
    });
    const [serviceData, setServiceData] = useState<any>({
        ambito: "persone",
        ricerca: "persone",
        dataType: dataTypeSelected,
        tipoPersona: personTypeSelected,
        data: "",
        cciaa: [""],
        annoNascita: "",
        dtNascita: "",
        creditChoice: selectedCredit,
    });

    const searchPersonsRequest = useGetCustom(
        "infocamere/search?search=persone"
    );

    useEffect(() => {
        const fetchData = async () => {
            const response: any = await searchPersonsRequest.doFetch();
            const { statesCitiesJSON, studioCredit, userCredit, prezzo } =
                response.data;

            const total =
                parseFloat(studioCredit.studioAmount) - parseFloat(prezzo);

            setInsufficientCredit(total <= 0 ? true : false);
            setProvinces(JSON.parse(statesCitiesJSON));
            setAmounts({
                studioAmount: parseFloat(studioCredit.studioAmount),
                userAmount: parseFloat(userCredit.userAmount),
                price: parseFloat(prezzo),
                total,
            });
        };

        fetchData();
    }, []);

    function switchingTabs(_event: React.SyntheticEvent, newTab: string) {
        setTabs(newTab);
    }

    function handleSearchBySelect(e: any) {
        const value = e.target.value;
        const findInputName: any = dataType.find(
            (data: any) => data.value === value
        );
        setDataTypeSelected(value);
        setInputLabelName(findInputName.name);
        setServiceData({ ...serviceData, dataType: value });
    }

    function onChangeMultipleSelection(event: any) {
        const { value } = event.target;
        setSelectProvinces(value);

        const cciaaValues = [];

        for (let i in value) {
            const provinceValue = provinces.find((item: any) => {
                return item.provincia === value[i];
            });
            if (provinceValue) {
                cciaaValues.push(provinceValue.iniziali);
            }
        }
        setServiceData({ ...serviceData, cciaa: cciaaValues });
    }

    const handlePersonTypeChoice = (type: string) => {
        setPersonTypeSelected(type);
        setServiceData({ ...serviceData, dataType: type });
    };

    function handleSearchDate(e: Date) {
        const date = new Date(e);
        const formattedDate = date.toLocaleDateString("en-US");
        setServiceData({ ...serviceData, dtNascita: formattedDate });
    }

    function handleCreditChoice(type: any) {
        let amountsUpdate = { ...amounts };

        if (type === "studioCredit") {
            amountsUpdate.total = amounts.studioAmount - amounts.price;
            if (amountsUpdate.total <= 0) {
                setInsufficientCredit(true);
            } else {
                setInsufficientCredit(false);
            }
        } else if (type === "userCredit") {
            amountsUpdate.total = amounts.userAmount - amounts.price;
            if (amountsUpdate.total <= 0) {
                setInsufficientCredit(true);
            } else {
                setInsufficientCredit(false);
            }
        }

        setSelectedCredit(type);
        setAmounts({ ...amountsUpdate });
        setServiceData({ ...serviceData, creditChoice: type });
    }

    async function handleWebServiceCall() {
        const params = {
            ...serviceData,
            personType: personTypeSelected,
            dataType: dataTypeSelected,
        };
        navigate("/infocamere/webservicecall/person", {
            state: {
                params: params,
            },
        });
    }

    return (
        <VaporPage>
            <Title
                title="Ricerca tra le persone"
                breadcrumbs={
                    <Breadcrumbs
                        aria-label="breadcrumb"
                        separator={<AngleRight />}
                    >
                        <Link to="/infocamere">Visure e Bilanci</Link>
                        <Typography>Ricerca Persone</Typography>
                    </Breadcrumbs>
                }
            />

            <VaporPage.Section>
                <TabContext value={tabs} sx={{ width: "100%" }}>
                    <Tabs size="extraSmall" onChange={switchingTabs}>
                        <Tab label="Dati impresa" value="1" />
                        <Tab label="Ricerce avanzata (1)" value="2" />
                    </Tabs>
                    {searchPersonsRequest.loading && <Spinner />}
                    <TabPanel value="1">
                        <Box
                            autoComplete="off"
                            component="form"
                            noValidate
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                "& .MuiTextField-root": {
                                    m: 1,
                                    width: 500,
                                },
                            }}
                        >
                            <Stack>
                                <FormControl>
                                    <div style={{ marginLeft: "8px" }}>
                                        <InputLabel id="text-label">
                                            Utilizza
                                        </InputLabel>
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                            }}
                                        >
                                            <Radio
                                                checked={
                                                    personTypeSelected ===
                                                    "fisica"
                                                }
                                                inputProps={{
                                                    "aria-label": "A",
                                                }}
                                                name="radio-buttons"
                                                onChange={() =>
                                                    handlePersonTypeChoice(
                                                        "fisica"
                                                    )
                                                }
                                            />
                                            <Typography>Fisica</Typography>
                                        </div>
                                        <div
                                            style={{
                                                display: "flex",
                                                alignItems: "center",
                                            }}
                                        >
                                            <Radio
                                                inputProps={{
                                                    "aria-label": "B",
                                                }}
                                                name="radio-buttons"
                                                checked={
                                                    personTypeSelected ===
                                                    "giuridica"
                                                }
                                                onChange={() =>
                                                    handlePersonTypeChoice(
                                                        "giuridica"
                                                    )
                                                }
                                            />
                                            <Typography>Giuridica</Typography>
                                        </div>
                                    </div>
                                </FormControl>
                                <FormControl
                                    sx={{
                                        m: 1,
                                        minWidth: 120,
                                        maxWidth: 500,
                                    }}
                                >
                                    <InputLabel id="demo-simple-select-helper-label">
                                        Cerca per
                                    </InputLabel>
                                    <Select
                                        id="dataType"
                                        name="dataType"
                                        labelId="demo-simple-select-helper-label"
                                        onChange={handleSearchBySelect}
                                        value={dataTypeSelected}
                                    >
                                        {dataType.map(
                                            (item: any, index: number) => {
                                                return (
                                                    <MenuItem
                                                        key={index}
                                                        value={item.value}
                                                    >
                                                        {item.name}
                                                    </MenuItem>
                                                );
                                            }
                                        )}
                                    </Select>
                                </FormControl>

                                <TextField
                                    id="outlined-required"
                                    name="data"
                                    onChange={(e: any) =>
                                        setServiceData({
                                            ...serviceData,
                                            data: e.target.value,
                                        })
                                    }
                                    label={inputLabelName}
                                />

                                {personTypeSelected === "fisica" && (
                                    <>
                                            <FormControl>
                                                <DatePicker
                                                    onChange={(e: any) =>
                                                        handleSearchDate(e)
                                                    }
                                                    value={
                                                        serviceData.dtNascita
                                                    }
                                                    slotProps={{
                                                        textField: {
                                                            name: "pickdaymonth",
                                                            error: false,
                                                        },
                                                    }}
                                                    label="Data di nascita"
                                                    minDate={new Date(2015, 0, 1)}
                                                />
                                            </FormControl>

                                        <TextField
                                            id="outlined-required"
                                            name="data"
                                            inputProps={{ maxLength: 4 }}
                                            onChange={(e: any) =>
                                                setServiceData({
                                                    ...serviceData,
                                                    data: e.target.value,
                                                })
                                            }
                                            label="Anno di nascita"
                                        />
                                    </>
                                )}
                            </Stack>
                        </Box>
                    </TabPanel>

                    <TabPanel value="2">
                        <Box
                            autoComplete="off"
                            component="form"
                            noValidate
                            sx={{
                                display: "flex",
                                justifyContent: "center",
                                "& .MuiTextField-root": {
                                    m: 1,
                                    width: 500,
                                },
                            }}
                        >
                            <Stack>
                                <FormControl
                                    sx={{
                                        m: 1,
                                        minWidth: 500,
                                        maxWidth: 500,
                                    }}
                                >
                                    <InputLabel id="demo-simple-select-helper-label">
                                        Province
                                    </InputLabel>
                                    <Select
                                        multiple
                                        id="provincia"
                                        name="provincia"
                                        labelId="demo-simple-select-helper-label"
                                        onChange={onChangeMultipleSelection}
                                        value={selectProvinces}
                                        input={
                                            <OutlinedInput
                                                id="select-multiple-chip"
                                                label="Chip"
                                            />
                                        }
                                        renderValue={(selected: any) => (
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    flexWrap: "wrap",
                                                    gap: 0.5,
                                                    color: "red",
                                                    outerHeight: "auto",
                                                }}
                                            >
                                                {Object.values(selected).map(
                                                    (value: any) => {
                                                        return (
                                                            <Chip
                                                                variant="outlined"
                                                                key={value}
                                                                label={value}
                                                            />
                                                        );
                                                    }
                                                )}
                                            </Box>
                                        )}
                                    >
                                        {provinces.map(
                                            (province: any, index: number) => {
                                                return (
                                                    <MenuItem
                                                        key={index}
                                                        value={
                                                            province.provincia
                                                        }
                                                    >
                                                        {province.provincia}
                                                    </MenuItem>
                                                );
                                            }
                                        )}
                                    </Select>
                                </FormControl>
                            </Stack>
                        </Box>
                    </TabPanel>

                    {/* common data */}
                    <PriceChoice
                        selectedCredit={selectedCredit}
                        amounts={amounts}
                        handleCreditChoice={handleCreditChoice}
                        insufficientCredit={insufficientCredit}
                    />

                    <Stack
                        direction="row"
                        justifyContent="center"
                        marginTop="30px"
                    >
                        <Button
                            variant="contained"
                            onClick={() => navigate("/infocamere")}
                            color="primary"
                            type="submit"
                        >
                            Indietro
                        </Button>

                        <Button
                            variant="contained"
                            color="primary"
                            sx={{ marginLeft: "20px" }}
                            onClick={handleWebServiceCall}
                        >
                            {t("Avvia la ricerca")} (€{" "}
                            {currencyFormat(amounts.price, 2, ",", ".")})
                        </Button>
                    </Stack>
                </TabContext>
            </VaporPage.Section>
        </VaporPage>
    );
}
