import { <PERSON>, Button, Typography } from "@vapor/react-material";
import { useCallback, useEffect, useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../hooks/useGetCustom";
import { getInfocamereDownloadsGrid } from "../../../utilities/infocamere/gridColumn";
import { IList } from "../../../interfaces/general.interfaces";
import Spinner from "../../../custom-components/Spinner";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import SaveToPracticeModal from "./components/saveToPracticeModal";
import Breadcrumbs from "@vapor/react-material/Breadcrumbs";
import { Link } from "react-router-dom";
import AngleRight from "@vapor/react-icons/AngleRight";
import { IDefaultQuery } from "./interfaces/infocamere.interface";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import { GridPaginationModel } from "@mui/x-data-grid-pro";

const defaultQuery: IDefaultQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "id",
    sortOrder: "desc",
    startDate: "",
    endDate: "",
};

export default function GoToDownloads() {
    const { t } = useTranslation();
    const infocamereDownloadsRequest = useGetCustom(
        "infocamere/list?noTemplateVars=true",
        defaultQuery
    );
    const infocamereDownloadsContentRequest = useGetCustom(
        "infocamere/downloadfoutput?noTemplateVars=true"
    );
    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
        page: 0,
    });
    const [query, setQuery] = useState<IDefaultQuery>(defaultQuery);
    const [fileUniqueId, setFileUniqueId] = useState<string>("");
    const [searchResult, setSearchResult] = useState<any[]>([]);
    const [disableChangeButton, setDisableChangeButton] =
        useState<boolean>(false);
    const [showSelectedArchiveName, setShowSelectedArchiveName] =
        useState<string>("");
    const [openSearchPracticeModal, setOpenSearchPracticeModal] = useState<{
        state: boolean;
        id: string;
    }>({
        state: false,
        id: "",
    });

    const fetchDownloads = useCallback(
        async (query?: any) => {
            try {
                const response: any = await infocamereDownloadsRequest.doFetch(
                    true,
                    query
                );
                const columns = await getInfocamereDownloadsGrid(
                    handleDownloadContent,
                    saveToPractice,
                    t
                );

                const { currentPage, totalRows } = response.data;

                const formatedRow = currentPage.map((item: any) => ({
                    ...item,
                    ...item.startParams, // Merge the startParams object into the main object
                    startParams: undefined, // Remove the startParams property
                }));

                setList({
                    ...list,
                    rows: formatedRow,
                    columns,
                    totalRows,
                });
            } catch (error: any) {
                console.error("Fetch downloads error: ", error);
            }
        },
        [t]
    );

    useEffect(() => {
        fetchDownloads(query);
    }, [query, fetchDownloads]);

    const handleDownloadContent = async (id: string) => {
        const response: any = await infocamereDownloadsContentRequest.doFetch(
            true,
            { id }
        );
        if (response.statusText === "OK") {
            window.open(response.data.result, "_blank");
        }
    };

    const handleSaveFileInPractice = async () => {
        const { data }: any = await infocamereDownloadsContentRequest.doFetch(
            true,
            { id: openSearchPracticeModal.id, fileUniqueId }
        );
        if (data.result) {
            fetchDownloads(query);
            setOpenSearchPracticeModal({
                ...openSearchPracticeModal,
                state: false,
            });
            setFileUniqueId("");
            setShowSelectedArchiveName("");
            setDisableChangeButton(false);
        }
    };

    const onPageChange = (model: GridPaginationModel) => {
        setQuery({
            ...query,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    function onChangeFilterInputs(event: Date, name: string) {
        const date = new Date(event);
        const formattedDate = date.toLocaleDateString("en-US");
        setQuery({ ...query, [name]: formattedDate });
    }

    function onClickReset() {
        setQuery(defaultQuery);
        fetchDownloads();
    }

    function saveToPractice(state: boolean = false, id: string) {
        setOpenSearchPracticeModal({ state, id });
    }

    const renderDataTable = () => {
        if (!list.columns.length) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="infocamereDownloads"
                setQuery={setQuery}
                columns={list.columns}
                data={list.rows}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                loading={infocamereDownloadsRequest.loading}
                onPageChangeCallback={onPageChange}
                query={query}
                disableColumnResize={true}
                disableColumnReorder={true}
            />
        );
    };

    return (
        <>
            <VaporPage title={t("Area download")}>
                <div
                    style={{
                        paddingTop: "30px",
                        paddingBottom: "30px",
                        paddingLeft: "8px",
                    }}
                >
                    <Breadcrumbs
                        aria-label="breadcrumb"
                        separator={<AngleRight />}
                    >
                        <Link to="/infocamere">Visure e Bilanci</Link>
                        <Typography>{t("Area download")}</Typography>
                    </Breadcrumbs>
                </div>
                <VaporPage.Section>
                    <Box
                        component="form"
                        display="flex"
                        alignItems="end"
                        gap={2}
                    >
                      <DatePicker
                        sx={{ width: 500 }}
                        value={
                          query.startDate
                            ? new Date(query.startDate)
                            : null
                        }
                                onChange={(event: any) =>
                                    onChangeFilterInputs(event, "startDate")
                                }
                        slotProps={{
                          textField: {
                            error: false,
                          },
                        }}
                      />
                      <DatePicker
                        sx={{ width: 500 }}
                        value={
                          query.endDate
                            ? new Date(query.endDate)
                            : null
                        }
                                onChange={(event: any) =>
                                    onChangeFilterInputs(event, "endDate")
                                }
                        slotProps={{
                          textField: {
                            error: false,
                          },
                        }}
                      />

                        <Button
                            onClick={() => fetchDownloads(query)}
                            variant="contained"
                            color="primary"
                            type="button"
                        >
                            Cerca
                        </Button>

                        <Button
                            variant="contained"
                            color="primary"
                            onClick={onClickReset}
                        >
                            Mostra tutti
                        </Button>
                    </Box>
                </VaporPage.Section>

                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>

            <SaveToPracticeModal
                state={openSearchPracticeModal.state}
                setOpenSearchPracticeModal={setOpenSearchPracticeModal}
                fileUniqueId={fileUniqueId}
                setFileUniqueId={setFileUniqueId}
                handleSaveFileInPractice={handleSaveFileInPractice}
                showSelectedArchiveName={showSelectedArchiveName}
                setShowSelectedArchiveName={setShowSelectedArchiveName}
                searchResult={searchResult}
                setSearchResult={setSearchResult}
                disableChangeButton={disableChangeButton}
                setDisableChangeButton={setDisableChangeButton}
                confirmLoading={infocamereDownloadsContentRequest.loading}
            />
        </>
    );
}
