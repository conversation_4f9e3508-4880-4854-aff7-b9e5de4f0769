import { useTranslation } from "@1f/react-sdk";
import { Stack } from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { LibraryQueryParams } from "../../../../interfaces/library.interfaces";
import { parseDate } from "../hooks/utils";


export const DateSearch = ({
    handleDateChange,
    libraryQuery,
}: {
    handleDateChange: any;
    libraryQuery: LibraryQueryParams;
}) => {
    const { t } = useTranslation();

    const startDate = parseDate(libraryQuery.startDate);
    const endDate = parseDate(libraryQuery.endDate);

    return (
        <Stack
            direction="row"
            gap={2}>
            <DatePicker
                value={startDate}
                label={t("Dal")}
                onChange={(date: Date | null) => {
                    handleDateChange("startDate")(date);
                }}
            />
            <DatePicker
                value={endDate}
                label={t("al")}
                onChange={(date: Date | null) => {
                    handleDateChange("endDate")(date);
                }}
            />
        </Stack>
    );
};
