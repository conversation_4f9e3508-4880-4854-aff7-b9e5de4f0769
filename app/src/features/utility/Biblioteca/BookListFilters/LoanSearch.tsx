import { useTranslation } from "@1f/react-sdk";
import { Stack, TextField } from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { LibraryQueryParams } from "../../../../interfaces/library.interfaces";
import { parseDate } from "../hooks/utils";


export const LoanSearch = ({
    handleInputChange,
    handleDateChange,
    libraryQuery,
}: {
    handleInputChange: (
        field: keyof LibraryQueryParams
    ) => (e: React.ChangeEvent<HTMLInputElement>) => void;
    handleDateChange: any;
    libraryQuery: LibraryQueryParams;
}) => {
    const { t } = useTranslation();

    const startDate = parseDate(libraryQuery.startLoanDate);
    const endDate = parseDate(libraryQuery.endLoanDate);

    return (
        <Stack
            direction="row"
            gap={2}>
            <TextField
                value={libraryQuery.prestatoSearch}
                label={t("Prestato a")}
                onChange={handleInputChange("prestatoSearch")}
            />
            <DatePicker
                value={startDate}
                label={t("Dal")}
                onChange={(date: Date | null) => {
                    handleDateChange("startLoanDate")(date);
                }}
            />
            <DatePicker
                value={endDate}
                label={t("al")}
                onChange={(date: Date | null) => {
                    handleDateChange("endLoanDate")(date);
                }}
            />
        </Stack>
    );
};
