import { useTranslation } from "@1f/react-sdk";
import { VaporPage } from "@vapor/react-custom";
import { Typography } from "@vapor/react-extended";
import { Stack } from "@vapor/react-material";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { format, addDays } from "date-fns";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import ConfirmModal from "../../../custom-components/ConfirmModal";
import PageTitle from "../../../custom-components/PageTitle";
import { LoanForm, User } from "../../../interfaces/library.interfaces";
import { Borrowers } from "./Borrowers";
import { DEFAULT_LOAN_FORM } from "./constants";
import { useCreateUpdateLoan } from "./hooks/useCreateLoan";
import { useDeleteLoan } from "./hooks/useDeleteLoan";
import { useGetLoanDetails } from "./hooks/useGetLoanDetails";
import { usePrintAllLoans } from "./hooks/usePrintAllLoans";
import { parseDate } from "./hooks/utils";


export const LoanCreateUpdate = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const [searchParams] = useSearchParams();
    const loanId = searchParams.get("loanId");
    const bookId = searchParams.get("bookId");

    const [loanData, setLoanData] = useState<LoanForm>(DEFAULT_LOAN_FORM);

    const [saveLoan, setSaveLoan] = useState(false);
    const [confirmDelete, setConfirmDelete] = useState(false);
    const [printAllLoans, setPrintAllLoans] = useState(false);
    const [deleteLoan, setDeleteLoan] = useState(false);
    const [validate, setValidate] = useState(false);
    const [selectedUser, setSelectedUser] = useState<User | null>(null);

    const deleteLoanResponse = useDeleteLoan({
        uniqueid: loanId,
        remove: deleteLoan,
    });

    const { data, hasLoaded } = useGetLoanDetails({ uniqueId: loanId });

    useEffect(() => {
        if (hasLoaded) {
            setLoanData({
                form: {
                    data_fine: data.form.data_fine,
                    data_inizio: data.form.data_inizio,
                    data_restituzione: data.form.data_restituzione || "",
                    loanUid: data.form.loanUid,
                    nome_prestatario: data.form.nome_prestatario,
                },
            });
        }
    }, [hasLoaded]);

    const allLoansResponse = usePrintAllLoans({ print: printAllLoans });

    useEffect(() => {
        if (allLoansResponse.hasLoaded) {
            setPrintAllLoans(false);
        }
    }, [allLoansResponse.hasLoaded]);

    const isUpdate = !!loanId;

    const handleDateChange: any =
        (field: keyof LoanForm["form"]) => (date: Date | null) => {
            setValidate(true),
                setLoanData({
                    form: {
                        ...loanData.form,
                        [field]: date ? format(date, "dd/MM/yyyy") : date,
                    },
                });
        };

    const startDate = parseDate(loanData.form.data_inizio) || new Date();
    const endDate =
        parseDate(loanData.form.data_fine) || addDays(new Date(), 1);
    const returnDate = parseDate(loanData.form.data_restituzione);

    const isValidReturnDate =
        startDate !== null &&
        endDate !== null &&
        returnDate !== null &&
        returnDate >= startDate;

    const isValidEndDate =
        startDate !== null && endDate !== null && endDate > startDate;

    const isValidUser = isUpdate || selectedUser !== null;

    const saveLoanResponse = useCreateUpdateLoan({
        params: {
            loanUid: loanData.form.loanUid,
            id_prestatario: selectedUser !== null ? selectedUser.id : "",
            data_inizio: format(startDate, "dd/MM/yyyy"),
            data_fine: format(endDate, "dd/MM/yyyy"),
            data_restituzione: returnDate
                ? format(returnDate, "dd/MM/yyyy")
                : "",
            tipo_prestatario: selectedUser !== null ? selectedUser.type : "2",
            uid: bookId,
        },
        save: saveLoan,
    });

    useEffect(() => {
        if (saveLoanResponse.hasLoaded || deleteLoanResponse.hasLoaded) {
            navigate(`/library/library/details?uniqueId=${bookId}`);
        }
    }, [saveLoanResponse.hasLoaded, deleteLoanResponse.hasLoaded]);

    const ANNULLA_BUTTON = {
        label: t("Annulla"),
        onclick: () => {
            navigate(`/library/library/details?uniqueId=${bookId}`);
        },
    };

    const STAMPA_BUTTON = {
        label: t("Stampa modulo"),
        onclick: () => {
            setPrintAllLoans(true);
        },
    };
    const ELIMINA_BUTTON = {
        label: t("Elimina"),
        onclick: () => {
            setConfirmDelete(true);
        },
        variant: "outlined",
        color: "error",
    };

    const SALVA_BUTTON = {
        label: t("Salva"),
        onclick: () => {
            if (isUpdate) {
                if (isValidEndDate && isValidUser && isValidReturnDate) {
                    setSaveLoan(true);
                } else {
                    setValidate(true);
                }
            } else {
                if (isValidEndDate && isValidUser) {
                    setSaveLoan(true);
                } else {
                    setValidate(true);
                }
            }
        },
    };

    return (
        <VaporPage>
            <ConfirmModal
                agree={t("Elimina")}
                decline={t("Annulla")}
                open={confirmDelete}
                title={t("Elimina?")}
                confirmText={t("Eliminare definitivamente l'oggetto?")}
                handleAgree={() => {
                    setDeleteLoan(true);
                }}
                handleDecline={() => setConfirmDelete(false)}
                colorConfirmButton="error"
            />
            <PageTitle
                title={t("NUOVO PRESTITO")}
                pathToPrevPage={`/library/library/details?uniqueId=${bookId}`}
                actionButtons={
                    isUpdate
                        ? [
                              ANNULLA_BUTTON,
                              STAMPA_BUTTON,
                              ELIMINA_BUTTON,
                              SALVA_BUTTON,
                          ]
                        : [ANNULLA_BUTTON, SALVA_BUTTON]
                }
            />

            <VaporPage.Section>
                <Stack
                    maxWidth={300}
                    gap={2}>
                    {isUpdate ? (
                        <Typography>{`${t("Prestatario")} : ${
                            loanData.form.nome_prestatario
                        }`}</Typography>
                    ) : (
                        <Borrowers
                            selectedUser={selectedUser}
                            setSelectedUser={setSelectedUser}
                        />
                    )}
                    <DatePicker
                        label={t("Inizio Prestito")}
                        value={startDate}
                        onChange={(date: Date | null) => {
                            handleDateChange("data_inizio")(date);
                        }}
                    />
                    <DatePicker
                        label={t("Fine Prestito")}
                        value={endDate || addDays(new Date(), 1)}
                        onChange={(date: Date | null) => {
                            handleDateChange("data_fine")(date);
                        }}
                        slotProps={{
                            textField: {
                                error: !isValidEndDate && validate,
                                helperText:
                                    !isValidEndDate &&
                                    validate &&
                                    t(
                                        "Attenzione! La data di fine prestito è antecedente alla data di inizio prestito."
                                    ),
                            },
                        }}
                    />
                    {isUpdate && (
                        <DatePicker
                            label={t("Data Restituzione")}
                            value={returnDate}
                            slotProps={{
                                textField: {
                                    error: !isValidReturnDate && validate,
                                    helperText:
                                        !isValidReturnDate &&
                                        validate &&
                                        t(
                                            "La data di restituzione deve essere successiva o uguale alla data di inizio."
                                        ),
                                },
                            }}
                            onChange={(date: Date | null) => {
                                handleDateChange("data_restituzione")(date);
                            }}
                        />
                    )}
                </Stack>
            </VaporPage.Section>
        </VaporPage>
    );
};
