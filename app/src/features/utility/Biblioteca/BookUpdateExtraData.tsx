import { useTranslation } from "@1f/react-sdk";
import { Stack, TextField } from "@vapor/react-material";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { processDateValue } from "../../../helpers/dateHelper";
import { BookUpdateExtraDataProps } from "../../../interfaces/library.interfaces";


export const BookUpdateExtraData = ({
    handleInputChange,
    handleDateChange,
    bookData,
}: BookUpdateExtraDataProps) => {
    const { t } = useTranslation();

    return (
        <Stack gap={4}>
            <TextField
                value={bookData.editore}
                label={t("Editore")}
                onChange={handleInputChange("editore")}
            />
            <DatePicker
                label={t("Anno Pubblicazione")}
                value={processDateValue(bookData.data_pubblicazione)}
                onChange={(date: Date | null) => {
                    handleDateChange("data_pubblicazione")(date);
                }}
            />
            <TextField
                value={bookData.collana}
                label={t("Collana")}
                onChange={handleInputChange("collana")}
            />
            <TextField
                label={t("Codice ISBN")}
                value={bookData.b_codice}
                onChange={handleInputChange("b_codice")}
            />
            <TextField
                value={bookData.valore}
                label={t("Valore")}
                onChange={handleInputChange("valore")}
                type="number"
            />
        </Stack>
    );
};
