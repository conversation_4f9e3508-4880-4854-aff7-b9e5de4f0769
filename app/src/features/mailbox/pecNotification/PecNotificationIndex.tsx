import { useState, useEffect } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import { CustomDataGrid } from "../../../custom-components/CustomDataGrid";
import Spinner from "../../../custom-components/Spinner";
import { useTranslation } from "@1f/react-sdk";
import PageTitle from "../../../custom-components/PageTitle";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import {
    Box,
    Button,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    Grid,
} from "@vapor/react-material";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { useNavigate } from "react-router-dom";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { getPecNotificationsGrid } from "../../../utilities/pecNotifications/gridColumn";
import { NewNotificaModal } from "./NewNotificaModal";
import { usePecNotificationContext } from "../pecNotification/PecProvider";

export default function PecNotificationIndex() {
    const navigate = useNavigate();

    const [columns, setColumns] = useState<any[]>([]);
    const [newNotificaModal, setNewNotificaModal] = useState(false);
    const {
        search,
        data,
        totalRows,
        date,
        setDate,
        isLoading,
        defaultParams,
        setDefaultParams,
        fetchData,
    } = usePecNotificationContext();

    useEffect(() => {
        const getColumns = async () => {
            const gridColumns: any[] = getPecNotificationsGrid(t);
            setColumns(gridColumns);
        };
        getColumns();
    }, []);

    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const year = date.getFullYear().toString();
        return `${day}/${month}/${year}`;
    };

    const onDateChange = (name: string, value: Date) => {
        const formatedDate = formatDate(value);
        setDate((prevDate: any) => ({ ...prevDate, [name]: value }));
        setDefaultParams({
            ...defaultParams,
            [name]: formatedDate,
        });
    };

    const onClickCallback = (row: any) => {
        navigate(
            `/legacy/archivenotifications/update/?fileUniqueid=${row.fileUniqueid}&uniqueid=${row.uniqueid}`
        );
    };

    const onChangeInput = (e: any) => {
        const { value } = e.target;

        setDefaultParams({
            ...defaultParams,
            ["status"]: value,
        });
    };

    const onClickReset = async () => {
        setDate((prevDate: any) => ({
            ...prevDate,
            start_date: search.start_date,
            end_date: search.end_date,
        }));

        fetchData(search, true);
    };
    const onSubmit = () => {
        fetchData(search);
    };

    const { t } = useTranslation();

    const onPageChange = (model: GridPaginationModel) => {
        setDefaultParams({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const renderDataTable = () => {
        if (!columns?.length) {
            return <Spinner fullPage={false} />;
        }

        return (
            <CustomDataGrid
                name="pecNotifications"
                setQuery={setDefaultParams}
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={isLoading}
                onPageChangeCallback={onPageChange}
                query={defaultParams}
                onClickKey="row"
                onClickCallback={onClickCallback}
            />
        );
    };

    const handleNewNotifica = () => {
        setNewNotificaModal(true);
    };

    const handleDialog = () => {
        setNewNotificaModal(!newNotificaModal);
    };

    return (
        <>
            <VaporPage>
                <NewNotificaModal
                    newNotificaModal={newNotificaModal}
                    handleClose={handleDialog}
                />
                <PageTitle
                    title={t("NOTIFICHE IN PROPRIO")}
                    showBackButton={false}
                    actionButtons={[
                        {
                            label: t(" Nuova notifica in proprio"),
                            onclick: handleNewNotifica,
                            variant: "contained",
                            startIcon: <AddCircleOutlineIcon />,
                        },
                    ]}
                />
                <VaporPage.Section>
                    {" "}
                    <Box display="flex" alignItems="end" gap={2} sx={{ pt: 1 }}>
                        <Grid md={3}>
                            <DatePicker
                                label={t("Dal") + ":"}
                                value={date.start_date}
                                onChange={(value: Date | null) => {
                                    if (value) onDateChange("start_date", value);
                                }}
                            />
                        </Grid>
                        <Grid md={3}>
                            <DatePicker
                                label={t("Al") + ":"}
                                value={date.end_date}
                                onChange={(value: Date | null) => {
                                    if (value) onDateChange("end_date", value);
                                }}
                            />
                        </Grid>

                        <FormControl variant="outlined" sx={{ width: 1 / 6 }}>
                            <InputLabel id="select-label">
                                {t("Stato")}:
                            </InputLabel>
                            <Select
                                labelId="select-label"
                                value={defaultParams.status}
                                label="Tutti gli stati"
                                onChange={onChangeInput}
                                name="status"
                            >
                                <MenuItem value={-1} key={-1}>
                                    {t("Tutti gli stati")}
                                </MenuItem>
                                <MenuItem value={0} key={0}>
                                    {t("Bozza")}
                                </MenuItem>
                                <MenuItem value={1} key={1}>
                                    {t("File pronti")}
                                </MenuItem>
                                <MenuItem value={2} key={2}>
                                    {t("Da inviare")}
                                </MenuItem>
                                <MenuItem value={3} key={3}>
                                    {t("Inviata")}
                                </MenuItem>
                            </Select>
                        </FormControl>
                        <Button
                            variant="contained"
                            sx={{ width: 1 / 10 }}
                            color="primary"
                            type="submit"
                            onClick={onSubmit}
                        >
                            {t("Cerca")}
                        </Button>
                        <Button
                            variant="contained"
                            color="primary"
                            sx={{ width: 1 / 10 }}
                            onClick={onClickReset}
                        >
                            {t("Mostra tutti")}
                        </Button>
                    </Box>
                </VaporPage.Section>
                <VaporPage.Section>{renderDataTable()}</VaporPage.Section>
            </VaporPage>
        </>
    );
}
