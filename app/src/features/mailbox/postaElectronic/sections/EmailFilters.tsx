import * as React from "react";

import {
    Box,
    TextField,
    Button,
    FormControl,
    Checkbox,
    Menu,
    Divider,
    ButtonGroup,
    Typography,
    Modal,
} from "@vapor/react-material";
import DeleteIcon from "@mui/icons-material/Delete";
import RefreshIcon from "@mui/icons-material/Refresh";
import SearchIcon from "@mui/icons-material/Search";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import ChevronRightIcon from "@mui/icons-material/ChevronRight";
import InsertDriveFileIcon from "@mui/icons-material/InsertDriveFile";
import { useState, useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { styled, alpha } from "@mui/material/styles";
import MenuItem from "@mui/material/MenuItem";
import { useMailboxProvider } from "../../provider/MailboxProvider";
import usePostCustom from "../../../../hooks/usePostCustom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { useNavigate } from "react-router-dom";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import CloseIcon from "@mui/icons-material/Close";
import Tooltip from "@mui/material/Tooltip";
import Fascicola from "./Fascicola";

const StyledMenu = styled((props: any) => (
    <Menu
        elevation={0}
        anchorOrigin={{
            vertical: "bottom",
            horizontal: "right",
        }}
        transformOrigin={{
            vertical: "top",
            horizontal: "right",
        }}
        {...props}
    />
))(({ theme }) => ({
    "& .MuiPaper-root": {
        borderRadius: 6,
        marginTop: theme.spacing(1),
        minWidth: 180,
        color: "rgb(55, 65, 81)",
        boxShadow:
            "rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px",
        "& .MuiMenu-list": {
            padding: "4px 0",
        },
        "& .MuiMenuItem-root": {
            "& .MuiSvgIcon-root": {
                fontSize: 18,
                color: theme.palette.text.secondary,
                marginRight: theme.spacing(1.5),
            },
            "&:active": {
                backgroundColor: alpha(
                    theme.palette.primary.main,
                    theme.palette.action.selectedOpacity
                ),
            },
        },
        ...theme.applyStyles("dark", {
            color: theme.palette.grey[300],
        }),
    },
}));

type View = "default" | "advanced";

export const EmailFilters = (props: any) => {
    const {
        selectedMails,
        messagesIds,
        setSelectedMails,
        setQuery,
        query,
        handleRefreshButton,
        openedMessage,
        selectedFolder,
    } = props;

    const { t } = useTranslation();

    const {
        mailbox,
        message,
        clearMessage,
        messages,
        uid,
        getMessages,
        openMessage,
    } = useMailboxProvider();

    const [date] = useState({
        date: null,
        untilDate: null,
    });

    const selectedMessages = messages?.filter((item: any) =>
        selectedMails.includes(item.uid)
    );

    const { maxMailsPerPage, countMessages, moveFolders, folders } = mailbox;

    const navigate = useNavigate();

    const [view, setView] = React.useState<View>("default");

    const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
    const [anchorMoveEl, setAnchorMoveEl] = React.useState<null | HTMLElement>(
        null
    );
    const [openConfirmModal, setOpenConfirmModal] = useState(false);
    const [checkedAll, setCheckedAll] = useState(false);
    const [loader, setLoader] = useState(false);

    const [openFascicola, setOpenFascicola] = React.useState(false);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [notificationText, setNotificationText] = useState("");
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);

    React.useEffect(() => {
        if (date.date || date.untilDate) {
            query.date = date.date;
            query.untilDate = date.untilDate;
            getMessages(query);
        }
    }, [date.date, date.untilDate]);

    const [isOpenFromInsideMessage, setIsOpenFromInsideMessage] =
        React.useState(false);

    const handleOpenFascicola = (inside: boolean = false) => {
        setOpenFascicola(true);
        setIsOpenFromInsideMessage(inside);
    };
    const handleCloseFascicola = () => setOpenFascicola(false);

    const open = Boolean(anchorEl);
    const openMoveEl = Boolean(anchorMoveEl);

    const moveMessageReq = usePostCustom(
        `mailbox/movemessages?noTemplateVars=true`
    );

    const deleteDefinetelyMessageReq = usePostCustom(
        `mailbox/deletemessages?noTemplateVars=true`
    );

    const saveMessageRequest = usePostCustom(
        `default/mailbox/saveemail?noTemplateVars=true`
    );

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleMoveElClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorMoveEl(event.currentTarget);
    };

    const closeMoveDropdown = () => {
        setAnchorMoveEl(null);
    };

    const handleMoveElClose = (folder: any) => {
        const folderName = folder.id ? folder.id : folder.name;
        setAnchorMoveEl(null);
        moveMessage(folderName);
    };

    const handleClose = (event: any) => {
        if (typeof event === "string" && event === "tutti") {
            setSelectedMails(messagesIds);
            setCheckedAll(true);
        }
        if (typeof event === "string" && event === "nessuno") {
            setSelectedMails([]);
            setCheckedAll(false);
        }

        if (typeof event === "string" && event === "unreed") {
            const unreadMessages = messages.filter(
                (message: any) => message.seen === 0 || message.seen === false
            );

            setSelectedMails(unreadMessages.map((item: any) => item.uid));
            setCheckedAll(false);
        }
        if (typeof event === "string" && event === "read") {
            const unreadMessages = messages.filter(
                (message: any) => message.seen === 1 || message.seen === true
            );
            setSelectedMails(unreadMessages.map((item: any) => item.uid));
            setCheckedAll(false);
        }
        setAnchorEl(null);
    };

    useEffect(() => {
        if (checkedAll) {
            setSelectedMails(messagesIds);
        } else {
            setSelectedMails([]);
        }
    }, [checkedAll]);

    const handleSelectAll = () => {
        setCheckedAll(!checkedAll);
    };

    const moveMessage = async (folderToMove: string) => {
        const formData = new FormData();
        selectedMails.forEach((id: any) => {
            formData.append("messages[]", id);
        });
        formData.append("uid", uid);
        formData.append("originFolder", query.folder);
        if (selectedFolder.printName === "Deleted Items" || "Bin") {
            formData.append("fromTrash", "1");
        } else {
            formData.append("fromTrash", "");
        }
        formData.append("folder", folderToMove);
        const response: any = await moveMessageReq.doFetch(true, formData);

        if (!response.data) {
            setShowErrorMessage(true);
            setNotificationText(t("Questo messaggio non può essere spostato"));
        }

        getMessages(query);
        setSelectedMails([]);
    };

    const deleteMessage = async (type: string) => {
        if (
            selectedFolder.printName === "Deleted Items" ||
            selectedFolder.printName === "Cestino"
        ) {
            setOpenConfirmModal(true);
        } else {
            const deletedFolder = folders.find(
                (folder: any) =>
                    folder.printName === "Deleted Items" ||
                    folder.printName === "Cestino"
            );
            const formData = new FormData();

            if (type === "inside") {
                formData.append("messages[]", message.messageUid);
            }

            if (type === "outside" && selectedMails.length > 0) {
                selectedMails.forEach((id: any) => {
                    formData.append("messages[]", id);
                });
            }

            formData.append("uid", uid);
            const deletedFolderId = deletedFolder?.id || deletedFolder.name;
            formData.append("originFolder", query.folder);
            formData.append("folder", deletedFolderId);
            formData.append("folder", deletedFolderId);
            formData.append("fromTrash", "false");
            const response: any = await moveMessageReq.doFetch(true, formData);

            if (!response.data) {
                setShowErrorMessage(true);
                setNotificationText(
                    t("Questo messaggio non può essere spostato")
                );
            }

            getMessages(query);
            setSelectedMails([]);
        }
    };

    const confermaDefinetelyDelete = async () => {
        setOpenConfirmModal(false);

        const formData = new FormData();
        selectedMails.forEach((id: any) => {
            formData.append("messages[]", id);
        });

        formData.append("uid", uid);
        formData.append("originFolder", query.folder);
        const response: any = await deleteDefinetelyMessageReq.doFetch(
            true,
            formData
        );
        if (response.data) {
            getMessages(query);
        } else {
            setShowErrorMessage(true);
            setNotificationText(t(`Questo messaggio non può essere eliminato`));
            getMessages(query);
        }
    };

    const cancelDefinetelyDelete = async () => {
        setOpenConfirmModal(false);

        const formData = new FormData();
        selectedMails.forEach((id: any) => {
            formData.append("messages[]", id);
        });

        formData.append("uid", uid);

        formData.append("originFolder", query.folder);
        formData.append("folder", query.folder);
        formData.append("fromTrash", "1");
        const response: any = await moveMessageReq.doFetch(true, formData);
        if (response.data) {
            getMessages(query);
        } else {
            setShowErrorMessage(true);
            setNotificationText(t(`Questo messaggio non può essere eliminato`));
            getMessages(query);
        }
    };

    const startPage = () => {
        if (query.page === 0) {
            return countMessages ? 1 : 0;
        } else {
            if (maxMailsPerPage < countMessages) {
                return query.page * maxMailsPerPage + 1;
            } else {
                return countMessages ? 1 : 0;
            }
        }
    };

    const endPage = () => {
        if (query.page === 0) {
            return Math.min(countMessages, maxMailsPerPage);
        } else {
            if (maxMailsPerPage < countMessages) {
                return (query.page + 1) * maxMailsPerPage;
            } else {
                return countMessages;
            }
        }
    };

    const getPrevPage = () => {
        const newQuery = { ...query, page: query.page - 1 };
        getMessages(newQuery);
        setQuery(newQuery);
    };

    const getNextPage = () => {
        const newQuery = { ...query, page: query.page + 1 };
        getMessages(newQuery);
        setQuery(newQuery);
    };

    const getMessageByView = (view: View) => {
        const newQuery = {
            messageUid: openedMessage[0],
            originFolder: query.folder,
            showFullMsg: view === "advanced" ? 1 : 0,
        };
        openMessage(newQuery);
        setView(view);
    };

    const handleSubmitData = async (data: any) => {
        const formData = new FormData();
        formData.append("uid", uid);
        formData.append(
            "messageUid",
            selectedMails.length === 0
                ? openedMessage[0]
                : selectedMails.join(",")
        );
        formData.append("originFolder", query.folder);
        formData.append("fileUid", data.practiceSearch.value);
        formData.append("archiveSearchTarget", data.archiveSearchTarget);
        formData.append("title", data.title);
        formData.append("fileName", data.fileName);
        formData.append("attachmentOneDrive", data.attachmentOneDrive);
        formData.append("saveFullMsg", data.saveFullMsg);
        setLoader(true);
        const response: any = await saveMessageRequest.doFetch(true, formData);

        if (data.createMacro) {
            return navigate(
                `/legacy/archivedeadlines/deadlines?fileUniqueid=${data.practiceSearch.value}&openMacro=1`
            );
        }

        if (response.data === true) {
            setLoader(false);
            handleCloseFascicola();
            setShowSuccessMessage(true);
        } else {
            setLoader(false);
            setShowErrorMessage(true);
            setNotificationText(
                t("L'email è già presente nel contratto indicato.")
            );
            handleCloseFascicola();
            return;
        }
    };

    return (
        <>
            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={notificationText}
            />
            <ToastNotification
                showNotification={showSuccessMessage}
                setShowNotification={setShowSuccessMessage}
                severity="success"
                text={t("Fascicolazione email completata correttamente")}
            />
            <ConfirmModal
                open={openConfirmModal}
                agree={t("Conferma")}
                decline={t("Cancel")}
                title={t("Vuoi continuare?")}
                handleAgree={() => confermaDefinetelyDelete()}
                handleDecline={() => cancelDefinetelyDelete()}
                confirmText={t(
                    "I messaggi selezionati verranno eliminati definitivamente. Continuare?"
                )}
            />
            <Box display="flex" alignItems="end" gap={1} sx={{ pt: 1, pb: 4 }}>
                <ButtonGroup variant="outlined" aria-label="Basic button group">
                    <Button>
                        <Checkbox
                            onChange={handleSelectAll}
                            checked={checkedAll}
                            size="large"
                        />
                    </Button>
                    <Button
                        onClick={handleClick}
                        sx={{ mr: "1rem" }}
                        endIcon={<ArrowDropDownIcon />}
                    >
                        {t("Seleziona")}
                    </Button>
                    <StyledMenu
                        id="demo-customized-menu"
                        MenuListProps={{
                            "aria-labelledby": "demo-customized-button",
                        }}
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                    >
                        <MenuItem
                            onClick={() => handleClose("tutti")}
                            disableRipple
                        >
                            {t("Tutti")}
                        </MenuItem>
                        <MenuItem
                            onClick={() => handleClose("nessuno")}
                            disableRipple
                        >
                            {t("Nessuno")}
                        </MenuItem>
                        <Divider sx={{ my: 0.5 }} />
                        <MenuItem
                            onClick={() => handleClose("unreed")}
                            disableRipple
                        >
                            {t("Da leggere")}
                        </MenuItem>
                        <MenuItem
                            onClick={() => handleClose("read")}
                            disableRipple
                        >
                            {t("Letti")}
                        </MenuItem>
                    </StyledMenu>
                </ButtonGroup>
                <FormControl variant="outlined">
                    <TextField
                        variant="outlined"
                        value={query.query}
                        placeholder="Oggetto..."
                        name="searchField"
                        onChange={(event: any) => {
                            setQuery((prevQuery: any) => ({
                                ...prevQuery,
                                query: event.target.value,
                            }));
                        }}
                    />
                </FormControl>
                <div style={{ width: 180 }}>
                    <DatePicker
                        label={t("Dal") + ":"}
                        value={query.date}
                        onChange={(date: Date | null) =>
                            setQuery((prevQuery: any) => ({
                                ...prevQuery,
                                date,
                            }))
                        }
                    />
                </div>
                <div style={{ width: 180 }}>
                    <DatePicker
                        label={t("Al") + ":"}
                        value={query.untilDate}
                        onChange={(date: Date | null) =>
                            setQuery((prevQuery: any) => ({
                                ...prevQuery,
                                untilDate: date,
                            }))
                        }
                    />
                </div>
                <Button
                    variant="outlined"
                    color="primary"
                    onClick={handleRefreshButton}
                >
                    <SearchIcon />
                </Button>

                <Button
                    variant="outlined"
                    color="primary"
                    disabled={query.page === 0}
                    onClick={getPrevPage}
                >
                    <ChevronLeftIcon />
                </Button>
                <Button
                    variant="outlined"
                    color="primary"
                    disabled={endPage() === countMessages}
                    onClick={getNextPage}
                >
                    <ChevronRightIcon />
                </Button>
                <Typography
                    sx={{ marginBottom: "8px", fontWeight: "bold" }}
                    variant="body"
                >
                    {startPage()} - {endPage()} Di {countMessages}
                </Typography>
            </Box>

            <Box display="flex" alignItems="end" gap={1} sx={{ pb: 4 }}>
                {selectedMails.length > 0 && (
                    <>
                        <ButtonGroup
                            variant="outlined"
                            aria-label="Basic button group"
                        >
                            <Button
                                onClick={handleMoveElClick}
                                sx={{ mr: "1rem" }}
                                endIcon={<ArrowDropDownIcon />}
                            >
                                {t("Sposta in:")}
                            </Button>
                            <StyledMenu
                                id="demo-customized-menu-move"
                                MenuListProps={{
                                    "aria-labelledby":
                                        "demo-customized-button-move",
                                }}
                                anchorEl={anchorMoveEl}
                                open={openMoveEl}
                                onClose={closeMoveDropdown}
                            >
                                {moveFolders?.map((folder: any) => {
                                    return (
                                        <MenuItem
                                            key={folder.name}
                                            onClick={() =>
                                                handleMoveElClose(folder)
                                            }
                                            disableRipple
                                        >
                                            {folder.printName}
                                        </MenuItem>
                                    );
                                })}
                                <Divider sx={{ my: 0.5 }} />
                                <MenuItem
                                    key={"emailsStorage"}
                                    onClick={() => handleOpenFascicola(true)}
                                    disableRipple
                                >
                                    {"Email fascicolate"}
                                </MenuItem>
                            </StyledMenu>
                        </ButtonGroup>

                        <Tooltip
                            title={
                                selectedFolder.printName === "Deleted Items"
                                    ? t("Elimina definitivamente")
                                    : t("Cestina")
                            }
                        >
                            <Button
                                variant="outlined"
                                color="error"
                                onClick={() => deleteMessage("outside")}
                            >
                                {selectedFolder.printName ===
                                "Deleted Items" ? (
                                    <CloseIcon />
                                ) : (
                                    <DeleteIcon />
                                )}
                            </Button>
                        </Tooltip>
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={() => handleOpenFascicola(false)}
                        >
                            {t("Fascicola")}
                        </Button>
                    </>
                )}
            </Box>

            {Object.keys(message).length > 0 && (
                <Box
                    display="flex"
                    alignItems="end"
                    gap={1}
                    sx={{ pt: 1, pb: 1 }}
                >
                    <Button
                        variant="outlined"
                        color="primary"
                        size="small"
                        onClick={clearMessage}
                    >
                        <ArrowBackIcon /> {t("Indietro")}
                    </Button>
                    {selectedFolder.printName != "Deleted Items" && (
                        <Button
                            variant="outlined"
                            color="error"
                            onClick={() => deleteMessage("inside")}
                            size="small"
                        >
                            <DeleteIcon /> {t("Cestina")}
                        </Button>
                    )}

                    <Button
                        variant="outlined"
                        color="secondary"
                        size="small"
                        onClick={() => handleOpenFascicola(true)}
                    >
                        <InsertDriveFileIcon /> {t("Fascicola")}
                    </Button>
                    <Typography
                        variant="bodySmall"
                        gutterBottom
                        component="div"
                    >
                        {t(
                            "(Dimensioni massime di un messaggio da fascicolare: 4MB)"
                        )}
                    </Typography>
                </Box>
            )}

            {Object.keys(message).length > 0 && (
                <Box display="flex" gap={1} sx={{ pt: 1, pb: 1 }}>
                    {view === "default" && (
                        <Typography variant="body" gutterBottom component="div">
                            {t(
                                "Se non visualizzi correttamente questo messaggio puoi passare alla "
                            )}{" "}
                            <b>{t("Visualizzazione avanzata")}</b>
                        </Typography>
                    )}
                    {view === "default" && (
                        <Button
                            variant="outlined"
                            color="secondary"
                            size="small"
                            sx={{ ml: 2 }}
                            onClick={() => getMessageByView("advanced")}
                        >
                            <RefreshIcon /> {t("Clicca qui")}
                        </Button>
                    )}

                    {view === "advanced" && (
                        <Typography variant="body" gutterBottom component="div">
                            {t("Modalità di ")}
                            <b>{t("Visualizzazione avanzata")}</b>{" "}
                            {t("Attivata, per tornare alla ")}
                            <b>{t("Visualizzazione classica")}</b>
                        </Typography>
                    )}
                    {view === "advanced" && (
                        <Button
                            variant="outlined"
                            color="secondary"
                            size="small"
                            sx={{ ml: 2 }}
                            onClick={() => getMessageByView("default")}
                        >
                            <RefreshIcon /> {t("Clicca qui")}
                        </Button>
                    )}
                </Box>
            )}

            <Modal
                open={openFascicola}
                onClose={handleCloseFascicola}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Fascicola
                    handleCloseFascicola={handleCloseFascicola}
                    handleSubmitData={handleSubmitData}
                    isOpenFromInsideMessage={isOpenFromInsideMessage}
                    selectedMails={selectedMails}
                    fileName={selectedMessages
                        ?.map((item: any) => item.subject)
                        .join(",")}
                    loader={loader}
                />
            </Modal>
        </>
    );
};
