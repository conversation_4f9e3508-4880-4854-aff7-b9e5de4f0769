import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
} from "@vapor/react-material";
import Select from "@vapor/v3-components/Select";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./../interfaces/archive.interface";

export default function Tab4(props: IFilterProps) {
  const {
    query,
    archiveData,
    onChangeFilterInputs,
  } = props;

  const { t } = useTranslation();

  const { sedi } = archiveData;

  return (
    <>
      <Box display='flex' alignItems='end' gap={2}>
        <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
          <InputLabel id='select-label'></InputLabel>
          <Select
            labelId='select-label'
            value={query.officeSearch}
            label='Tutte le sedi'
            onChange={onChangeFilterInputs}
            name='officeSearch'>
            <MenuItem value='-1'>{t("Tutte le sedi")}</MenuItem>
            {sedi?.map((category: any) => (
              <MenuItem key={"sediList" + category.id} value={category.id}>
                {category.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Box>
    </>
  );
}
