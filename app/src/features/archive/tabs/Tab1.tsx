import {
    Box,
    TextField,
    Button,
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    MenuItem,
    Tooltip,
    IconButton,
    Typography,
  } from "@vapor/react-material";
  import Select from "@vapor/v3-components/Select";
  import InfoIcon from '@mui/icons-material/Info';
  import { useTranslation } from "@1f/react-sdk";
  import { DatePicker } from "../../../components/ui-kit/DatePicker";
  import { IFilterProps } from "../interfaces/archive.interface";
  import { processDateValue } from "../../../helpers/dateHelper";
  
  export default function Tab1(props: IFilterProps) {
    const {
        defaultQuery,
        query,
        setQuery,
        archiveData,
        filterArchiveData,
        onChangeFilterInputs,
        onDateChange,
        onChangeCheckbox,
    } = props;
  
    const { t } = useTranslation();
  
    const {
        identificatori,
        outcomes,
        relazioniUtente,
        peopleCategories,
    } = archiveData;
  
    return (
        <>
            <Box display='flex' alignItems='end' gap={2}>
                <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
                    <InputLabel id='select-label'></InputLabel>
                    <Select
                        labelId='select-label'
                        label={t('Tutti i codici')}
                        value={query.codeType}
                        onChange={onChangeFilterInputs}
                        name='codeType'>
                        <MenuItem value='-1'>{t("Tutti i codici")}</MenuItem>
                        <MenuItem value='0'>{t("Codice")}</MenuItem>
                        <MenuItem value='1'>{t("Codice Archivio")}</MenuItem>
                        <MenuItem value='2'>{t("Decreto ingiuntivo")}</MenuItem>
                        <MenuItem value='-2'>{t("Codice puntuale")}</MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    label={t('Codice:')}
                    variant='outlined'
                    value={query.code}
                    placeholder={t('Cerca Codice')}
                    name='code'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
                    <InputLabel id='select-label'></InputLabel>
                    <Select
                        labelId='select-label'
                        value={query.identificatore}
                        label={t('Tutte le Relazioni')}
                        onChange={onChangeFilterInputs}
                        name='identificatore'>
                        <MenuItem value='-1'>{t("Tutti i sezionali")}</MenuItem>
                        {(Array.isArray(identificatori) ? identificatori : Object.values(identificatori || {}))?.map((category: any) => (
                            <MenuItem key={category.name} value={category.name}>
                                {category.name}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
                    <InputLabel id='select-label'></InputLabel>
                    <Select
                        labelId='select-label'
                        value={query.dateType}
                        label={t('Data:')}
                        onChange={onChangeFilterInputs}
                        name='dateType'>
                        <MenuItem value='0'>{t('Tutte')}</MenuItem>
                        <MenuItem value='1'>{t('Aperta')}</MenuItem>
                        <MenuItem value='2'>{t('Chiusa')}</MenuItem>
                        <MenuItem value='3'>{t('Archiviata')}</MenuItem>
                        <MenuItem value='4'>{t('Con Incassi')}</MenuItem>
                        <MenuItem value='5'>{t('Data Inserimento')}</MenuItem>
                        <MenuItem value='6'>{t('Ultima Modifica')}</MenuItem>
                        <MenuItem value='7'>{t('Senza impegni/udienze')}</MenuItem>
                        <MenuItem value='8'>{t('Macero')}</MenuItem>
                        <MenuItem value='9'>{t('Sentenza')}</MenuItem>
                    </Select>
                </FormControl>
                <div style={{ width: "25%" }}>
                    <DatePicker
                        label={t('Dal')}
                        name='startDate'
                        value={processDateValue(query.startDate)}
                        onChange={(date: Date | null) => {
                            if (date) {
                                onDateChange('startDate', date);
                            }
                        }}
                    />
                </div>
                <div style={{ width: "25%" }}>
                    <DatePicker
                        label={t('Al')}
                        name='endDate'
                        value={processDateValue(query.endDate)}
                        onChange={(date: Date | null) => {
                            if (date) {
                                onDateChange('endDate', date);
                            }
                        }}
                    />
                </div>
            </Box>
            <Box display='flex' alignItems='end' gap={2} style={{ marginTop: "7px" }}>
                <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
                    <InputLabel id='select-label'></InputLabel>
                    <Select
                        labelId='select-label'
                        value={query.relazioneType}
                        label={t('Tutte le relazioni')}
                        onChange={onChangeFilterInputs}
                        name='relazioneType'>
                        <MenuItem value='0'>{t('Tutte le relazioni')}</MenuItem>
                        <MenuItem value='1'>{t('Cliente')}</MenuItem>
                        <MenuItem value='2'>{t('Controparte')}</MenuItem>
                        <MenuItem value='3'>{t('Avversario')}</MenuItem>
                        <MenuItem value='4'>{t('Altro')}</MenuItem>
                        <MenuItem value='5'>{t('Dominus')}</MenuItem>
                        <MenuItem value='6'>{t('Procuratore')}</MenuItem>
                        <MenuItem value='7'>{t('Esterno')}</MenuItem>
                        <MenuItem value='8'>{t('Professionista esterno')}</MenuItem>
                        <MenuItem value='20'>{t('Cointestatario')}</MenuItem>
                        <MenuItem value='21'>{t('Domiciliatario')}</MenuItem>
                        <MenuItem value='22'>{t('Corrispondente')}</MenuItem>
                        <MenuItem value='23'>{t('Cliente Fatturazione')}</MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    label={t('Nome relazione')}
                    variant='outlined'
                    value={query.relazioneName}
                    placeholder={t('Nome relazione')}
                    name='relazioneName'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
                    <InputLabel id='select-label'></InputLabel>
                    <Select
                        labelId='select-label'
                        value={query.relazioneTypeUTN}
                        label={t('Tutte i sezionali')}
                        onChange={onChangeFilterInputs}
                        name='relazioneTypeUTN'>
                        {relazioniUtente?.map((category: any) => (
                            <MenuItem
                                key={"relazioniUtente" + category.id}
                                value={category.id}>
                                {category.nome}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                <TextField
                    label={t('Nome utente')}
                    variant='outlined'
                    value={query.relazioneNameUTN}
                    placeholder={t('Nome utente')}
                    name='relazioneNameUTN'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
                    <InputLabel id='select-label'>{t('Categoria anagrafiche')}</InputLabel>
                    <Select
                        labelId='select-label'
                        value={query.peopleCategory || "-1"}
                        label={t('Seleziona categoria')}
                        onChange={onChangeFilterInputs}
                        name='peopleCategory'>
                        <MenuItem value='-1'>{t("Seleziona categoria")}</MenuItem>
                        {peopleCategories?.map((category: any) => (
                            <MenuItem
                                key={"peopleCategories" + category.id}
                                value={category.id}>
                                {category.nome}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
  
                <TextField
                    label={t('Cliente')}
                    variant='outlined'
                    value={query.customer}
                    placeholder={t('Cliente')}
                    name='customer'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
            </Box>
            <Box display='flex' alignItems='end' gap={2} style={{ marginTop: "7px" }}>
                <TextField
                    label={t('Controparte')}
                    variant='outlined'
                    value={query.counterpart}
                    placeholder={t('Controparte')}
                    name='counterpart'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <TextField
                    label={t('Responsabile')}
                    variant='outlined'
                    value={query.referent}
                    placeholder={t('Responsabile')}
                    name='referent'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <TextField
                    label={t('Nome/Descrizione')}
                    variant='outlined'
                    value={query.descrizione}
                    placeholder={t('Nome/Descrizione')}
                    name='descrizione'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <TextField
                    label={t('RG')}
                    variant='outlined'
                    value={query.rgn}
                    placeholder={t('RG')}
                    name='rgn'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <Typography variant="h5">/</Typography>
                <TextField
                    label={t('Anno')}
                    variant='outlined'
                    value={query.rga}
                    placeholder={t('Anno')}
                    name='rga'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
                <Typography variant="h5">-</Typography>
                <TextField
                    label={t('Sub')}
                    variant='outlined'
                    value={query.subprocedimento}
                    placeholder={t('Sub')}
                    name='subprocedimento'
                    sx={{ width: 1 / 3 }}
                    onChange={onChangeFilterInputs}
                />
            </Box>
            <Box display='flex' alignItems='end' gap={2} style={{ marginTop: "7px" }}>
                <FormControl variant='outlined' sx={{ width: 1 / 4 }}>
                    <InputLabel id='select-label'></InputLabel>
                    <Select
                        labelId='select-label'
                        value={query.contractId || "-1"}
                        label={t('Tutti gli esiti')}
                        onChange={onChangeFilterInputs}
                        name='contractId'>
                        <MenuItem value='-1'>{t("Tutti gli esiti")}</MenuItem>
                        {outcomes?.map((category: any) => (
                            <MenuItem key={"outcomesList" + category.id} value={category.id}>
                                {category.nome}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
                {query.isArchived &&
                    <DatePicker
                        label={t('Data archivio')}
                        name='dataArchivio'
                        value={processDateValue(query.dataArchivio)}
                        onChange={(date: Date | null) => {
                            if (date) {
                                onDateChange('dataArchivio', date);
                            }
                        }}
                    />
                }
                <FormControlLabel
                    sx={{ width: 1 / 7 }}
                    control={
                        <Checkbox
                            onChange={onChangeCheckbox}
                            color='primary'
                            name='isArchived'
                            checked={!!query.isArchived}
                        />
                    }
                    label={t('Mostra archiviate')}
                />
                <FormControlLabel
                    sx={{}}
                    control={
                        <Checkbox
                            onChange={onChangeCheckbox}
                            color='primary'
                            name='isWithoutLink'
                            checked={!!query.isWithoutLink}
                        />
                    }
                    label={t('Pratiche')}
                />
                <Tooltip
                    arrow
                    title={t(
                        "Cerca le sole pratiche che NON abbiano impegni, udienze, fatture, timesheet associati"
                    )}
                >
                    <IconButton sx={{ mt: 3.5 }}>
                        <InfoIcon />
                    </IconButton>
                </Tooltip>
                <Button
                    variant='contained'
                    color='primary'
                    type='submit'
                    onClick={() => filterArchiveData(query)}
                >
                    {t('Cerca')}
                </Button>
  
                <Button
                    variant='contained'
                    color='primary'
                    onClick={() => setQuery({ ...defaultQuery })}
                >
                    {t('Mostra tutti')}
                </Button>
            </Box>
        </>
    );
  }