import {
  Grid,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Box,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { IFilterProps } from "../interfaces/archive.interface";
import { processDateValue } from "../../../helpers/dateHelper";
import Select from "@vapor/v3-components/Select";

export default function Tab8(props: IFilterProps) {
  const {
    defaultQuery,
    query,
    setQuery,
    filterArchiveData,
    onChangeFilterInputs,
    onDateChange,
  } = props;

  const { t } = useTranslation();

  return (
    <Grid container spacing={2} alignItems="flex-end">
      <Grid item xs={12} sm={6} md={3}>
        <FormControl variant="outlined" fullWidth>
          <InputLabel id="select-label">{t("Pratiche che")}</InputLabel>
          <Select
            labelId="select-label"
            value={query.movimentate_have}
            onChange={onChangeFilterInputs}
            name="movimentate_have"
          >
            <MenuItem value="-1">-</MenuItem>
            <MenuItem value="1">{t("Contengono")}</MenuItem>
            <MenuItem value="0">{t("Non contengono")}</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <FormControl variant="outlined" fullWidth>
          <InputLabel id="select-type-label">{t("Tipo di pratica")}</InputLabel>
          <Select
            labelId="select-type-label"
            value={query.movimentate_type}
            onChange={onChangeFilterInputs}
            name="movimentate_type"
          >
            <MenuItem value="prestazioni">{t("Prestazioni")}</MenuItem>
            <MenuItem value="prestazioni_fatturate">{t("Prestazioni fatturate")}</MenuItem>
            <MenuItem value="prestazioni_da_fatturare">{t("Prestazioni da fatturare")}</MenuItem>
            <MenuItem value="fatture">{t("Documenti fiscali")}</MenuItem>
            <MenuItem value="preavvisi">{t("Preavvisi di parcella")}</MenuItem>
            <MenuItem value="udienze">{t("Udienze")}</MenuItem>
            <MenuItem value="documenti">{t("Documenti inseriti")}</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={2.5}>
        <DatePicker
          label={t("Nel periodo dal")}
          name="movimentate_startDate"
          value={processDateValue(query.movimentate_startDate)}
          onChange={(date: Date | null) => {
            if (date) {
              onDateChange('movimentate_startDate', date);
            }
          }}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2.5}>
        <DatePicker
          label={t("Al")}
          name="movimentate_endDate"
          value={processDateValue(query.movimentate_endDate)}
          onChange={(date: Date | null) => {
            if (date) {
              onDateChange('movimentate_endDate', date);
            }
          }}
        />
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "flex-start", gap: 2, mt: 2, ml: 2 }}>
        <Button
          variant="contained"
          color="primary"
          type="submit"
          onClick={() => filterArchiveData(query)}
        >
          {t("Cerca")}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={() => setQuery({ ...defaultQuery })}
        >
          {t("Mostra tutti")}
        </Button>
      </Box>
    </Grid>
  );
}
