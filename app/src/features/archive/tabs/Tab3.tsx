import {
  Grid,
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Box,
  Typography,
} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import Select from "@vapor/v3-components/Select";
import useGetCustom from "../../../hooks/useGetCustom";
import AutocompleteFilter from "./AutocompleteFilter";
import { IFilterProps } from "../interfaces/archive.interface";

export default function Tab3(props: IFilterProps) {
  const {
    defaultQuery,
    query,
    setQuery,
    archiveData,
    filterArchiveData,
    onChangeFilterInputs,
  } = props;

  const archiveFilterRequest = useGetCustom("archive/search-objects?noTemplateVars=true");

  const { dominusused, crimes } = archiveData;
  const { t } = useTranslation();

  const fetchOptions = async (searchValue: string) => {
    const response: any = await archiveFilterRequest.doFetch(true, { q: searchValue, increment: 0 });

    return Array.isArray(response?.data) ? response.data : [];
  };

  return (
    <Grid container spacing={2}>
      {/* First Row */}
      <Grid item xs={12} sm={6} md={3}>
        <AutocompleteFilter
          {...props}
          fetchOptions={fetchOptions}
          queryKey="object"
          queryKey2="searchObject"
          label="Cerca per oggetto..."
          placeholder="Cerca per oggetto..."
          multiple={false}
          target="nome"
          queryVal="id"
        />
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <FormControl variant="outlined" fullWidth>
          <InputLabel id="select-dominus-label">{t("Tutti i dominus")}</InputLabel>
          <Select
            labelId="select-dominus-label"
            value={query.archiveDominus}
            label="Tutti i dominus"
            onChange={onChangeFilterInputs}
            name="archiveDominus"
          >
            <MenuItem value="-1">{t("Tutti i dominus")}</MenuItem>
            {dominusused?.map((category: any) => (
              <MenuItem key={`dominususedList-${category.id}`} value={category.id}>
                {category.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <FormControl variant="outlined" fullWidth>
          <InputLabel id="select-crimes-label">{t("Tutti i reati")}</InputLabel>
          <Select
            labelId="select-crimes-label"
            value={query.archiveCrimes}
            label="Tutti i reati"
            onChange={onChangeFilterInputs}
            name="archiveCrimes"
          >
            <MenuItem value="-1">{t("Tutti i reati")}</MenuItem>
            {crimes?.map((category: any) => (
              <MenuItem key={`crimesList-${category.id}`} value={category.id}>
                {category.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <FormControl variant="outlined" fullWidth>
          <InputLabel id="select-spesa-label">{t("Tutte i tipi spesa")}</InputLabel>
          <Select
            labelId="select-spesa-label"
            value={query.archiveTipiSpesa}
            label="Tutte i tipi spesa"
            onChange={onChangeFilterInputs}
            name="archiveTipiSpesa"
          >
            <MenuItem value="0">{t("Tutti i tipi spesa")}</MenuItem>
            <MenuItem value="1">{t("Compensata")}</MenuItem>
            <MenuItem value="2">{t("Condanna Comune")}</MenuItem>
          </Select>
        </FormControl>
      </Grid>

      <Grid item xs={12} sm={6} md={3}>
        <TextField
          label="Giudice:"
          variant="outlined"
          value={query.istruttore}
          placeholder="Cerca Giudice"
          name="istruttore"
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={3}>
        <TextField
          label="Sede:"
          variant="outlined"
          value={query.sede}
          placeholder="Cerca Sede"
          name="sede"
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid>

      </Grid>
      <Grid item xs={12} sm={6} md={1}>
        <TextField
          label="Sentenza:"
          variant="outlined"
          value={query.ns}
          placeholder="Numero"
          name="ns"
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 3.5 }}>
        <Typography variant="h5">/</Typography>
      </Grid>
      <Grid item xs={12} sm={6} md={1} sx={{ mt: 3.5 }}>
        <TextField
          variant="outlined"
          value={query.nsa}
          placeholder="Anno"
          name="nsa"
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Box sx={{ display: "flex", justifyContent: "flex-start", gap: 2, mt: 5.5, ml: 2 }}>
        <Button
          variant="contained"
          color="primary"
          type="submit"
          onClick={() => filterArchiveData(query)}
        >
          {t('Cerca')}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={() => setQuery(defaultQuery)}
        >
          {t('Mostra tutti')}
        </Button>
      </Box>
    </Grid>
  );
}
