import {
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Grid,
} from "@vapor/react-material";
import Select from "@vapor/v3-components/Select";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "../interfaces/archive.interface";

export default function Tab6(props: IFilterProps) {
  const {
    defaultQuery,
    query,
    setQuery,
    archiveData,
    filterArchiveData,
    onChangeFilterInputs,
  } = props;

  const { t } = useTranslation();

  const { situazione, situazioneContabile } = archiveData;

  return (
    <Grid container spacing={2}>
      <Grid item xs={12} sm={6} md={2}>
        <TextField
          label={t('Protocollo generale:')}
          variant='outlined'
          value={query.generalProtocol}
          placeholder={t('Cerca Protocollo generale')}
          name='generalProtocol'
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <FormControl variant='outlined' fullWidth>
          <InputLabel id='select-label'>{t("Situazione")}</InputLabel>
          <Select
            labelId='select-label'
            value={query.situazione}
            label={t('Tutte i sezionali')}
            onChange={onChangeFilterInputs}
            name='situazione'>
            <MenuItem value='-1'>{t("Tutti")}</MenuItem>
            <MenuItem value='0'>{t("Non assegnati")}</MenuItem>
            {situazione?.map((category: any) => (
              <MenuItem
                key={"situazioneList" + category.id}
                value={category.id}>
                {category.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <FormControl variant='outlined' fullWidth>
          <InputLabel id='select-label'>{t("Situazione contabile")}</InputLabel>
          <Select
            labelId='select-label'
            value={query.situazioneContabile}
            label={t('Tutte i sezionali')}
            onChange={onChangeFilterInputs}
            name='situazioneContabile'>
            <MenuItem value='-1'>{t("Tutti")}</MenuItem>
            <MenuItem value='0'>{t("Non assegnati")}</MenuItem>
            {situazioneContabile?.map((category: any) => (
              <MenuItem
                key={"situazioneContabileList" + category.id}
                value={category.id}>
                {category.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <FormControl variant='outlined' fullWidth>
          <InputLabel id='select-label'>{t("Centro profitto")}</InputLabel>
          <Select
            labelId='select-label'
            value={query.situazione}
            label={t('Tutti')}
            onChange={onChangeFilterInputs}
            name='situazione'>
            <MenuItem value='-1'>{t("Tutti")}</MenuItem>
            <MenuItem value='0'>{t("Non assegnati")}</MenuItem>
            {situazione?.map((category: any) => (
              <MenuItem
                key={"situazioneList" + category.id}
                value={category.id}>
                {category.nome}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
      <Grid item xs={12} sm={6} md={2} sx={{ mt: 3.5 }}>
        <TextField
          label={t('Annotazioni:')}
          variant='outlined'
          value={query.annotazioni}
          placeholder='Annotazioni'
          name='annotazioni'
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2} sx={{ mt: 3.5 }}>
        <TextField
          label={t('Stanza:')}
          variant='outlined'
          value={query.stanza}
          placeholder='Stanza'
          name='stanza'
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <TextField
          label={t('Palchetto:')}
          variant='outlined'
          value={query.palchetto}
          placeholder='Palchetto'
          name='palchetto'
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <TextField
          label={t('Scaffale:')}
          variant='outlined'
          value={query.scaffale}
          placeholder='Scaffale'
          name='scaffale'
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <TextField
          label={t('Faldone:')}
          variant='outlined'
          value={query.faldone}
          placeholder='Faldone'
          name='faldone'
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} sm={6} md={2}>
        <TextField
          label={t('Scatolone:')}
          variant='outlined'
          value={query.scatolone}
          placeholder='Scatolone'
          name='scatolone'
          fullWidth
          onChange={onChangeFilterInputs}
        />
      </Grid>
      <Grid item xs={12} md={3} >
        <Button
          variant='contained'
          color='primary'
          type='submit'
          onClick={() => filterArchiveData(query)}>
          {t("Cerca")}
        </Button>
        <Button
          variant='contained'
          color='primary'
          sx={{ ml: 2 }}
          onClick={() => {
            setQuery(defaultQuery)
          }
          }>
          {t("Mostra tutti")}
        </Button>
      </Grid>

    </Grid>
  );
}