import {
  TextField,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Grid,
} from "@vapor/react-material";
import Select from "@vapor/v3-components/Select";
import { useTranslation } from "@1f/react-sdk";
import useGetCustom from "../../../hooks/useGetCustom";

import { SelectMultiple } from "../../../custom-components/SelectMultiple";
import AutocompleteFilter from "./AutocompleteFilter";
import { IFilterProps } from "../interfaces/archive.interface";
import { CustomSelect } from "../../../custom-components/CustomSelect";

export default function Tab2(props: IFilterProps) {
  const {
    defaultQuery,
    query,
    setQuery,
    archiveData,
    filterArchiveData,
    onChangeFilterInputs,
    handleChangeMultiSelect,
    selectedValues,
    setSelectedValues,
  } = props;

  const { t } = useTranslation();

  const { filesTipologies, filesStatus, lawyers, subjectsGroups } = archiveData;

  const archiveFilterRequest = useGetCustom("archive/searchoffices?noTemplateVars=true");

  const fetchOptions = async (searchValue: string) => {
    const response: any = await archiveFilterRequest.doFetch(true, { q: searchValue, increment: 0 });
    return Array.isArray(response?.data) ? response.data : [];
  };

  return (
    <>
      <Grid container spacing={2}>
        <Grid item xs={12} md={3}>
          <AutocompleteFilter
            {...props}
            fetchOptions={fetchOptions}
            queryKey="authority"
            queryKey2="authoritySearch"
            label="Filtra per autorità"
            placeholder="Filtra per autorità..."
            multiple={false}
            target="nome"
            queryVal="id"
          />
        </Grid>
        <Grid item xs={12} md={3}>
          <FormControl variant='outlined' fullWidth sx={{ pt: 2.5 }} >
            <InputLabel id='select-label'></InputLabel>
            <CustomSelect
              value={query.tipology}
              onChange={onChangeFilterInputs}
              name='tipology'>
              <MenuItem value='-1'>{t("Tutte le tipologie")}</MenuItem>
              {filesTipologies?.map((category: any) => (
                <MenuItem
                  key={"filesTipologiesList" + category.id}
                  value={category.id}>
                  {category.nome}
                </MenuItem>
              ))}
            </CustomSelect>
          </FormControl></Grid>
        <Grid item xs={12} md={3}>
          <FormControl variant='outlined' fullWidth sx={{ pt: 2.5 }}>
            <InputLabel id='select-label'></InputLabel>
            <CustomSelect
              value={query.status}
              onChange={onChangeFilterInputs}
              name='status'>
              <MenuItem value='-1'>{t("Tutti gli stati")}</MenuItem>
              {filesStatus?.map((category: any) => (
                <MenuItem
                  key={"filesStatusList" + category.id}
                  value={category.id}>
                  {category.nome}
                </MenuItem>
              ))}
            </CustomSelect>
          </FormControl></Grid>
        <Grid item xs={12} md={3}>
          <FormControl variant='outlined' fullWidth sx={{ pt: 2.5 }}>
            <InputLabel id='select-label'></InputLabel>
            <Select
              labelId='select-label'
              value={query.sectorStudy}
              label='Non presente'
              onChange={onChangeFilterInputs}
              name='sectorStudy'>
              <MenuItem value='-1'>{t("Studi di settore")}</MenuItem>
              <MenuItem value='0'>{t("Non presente")}</MenuItem>
              <MenuItem value='1'>{t("Presente")}</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={3}>
          <SelectMultiple
            style={{ width: "100%" }}
            width="100%"
            name="searchAnagraficheTypes"
            label={t("Avvocati")}
            options={lawyers && lawyers.map((lawyer: any) => ({
              label: lawyer.nome || '',
              value: lawyer.id || lawyer.uniqueid || '',
              ...lawyer
            }))}
            onChange={handleChangeMultiSelect}
            selectedValues={selectedValues}
          />
        </Grid>
        <Grid item xs={12} md={3}>
          <TextField
            label='Sezione:'
            variant='outlined'
            value={query.sezioneFascicolo}
            placeholder='Cerca Sezione'
            name='sezioneFascicolo'
            onChange={onChangeFilterInputs}
          />
        </Grid>
        <Grid item xs={12} md={3}>
          <FormControl variant='outlined' fullWidth>
            <InputLabel id='select-label'>{t("Gruppo utenti:")}</InputLabel>
            <Select
              labelId='select-label'
              value={query?.subjects_group}
              onChange={onChangeFilterInputs}
              name='subjects_group'>
              <MenuItem value="-1">-</MenuItem>
              {subjectsGroups?.map((item: any) =>
                <MenuItem key={item?.id} value={item?.id}>{item?.name}</MenuItem>
              )}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={3} sx={{ mt: 3.5 }}>
          <Button
            variant='contained'
            color='primary'
            type='submit'
            onClick={() => filterArchiveData(query)}>
            {t('Cerca')}
          </Button>
          <Button
            variant='contained'
            color='primary'
            sx={{ ml: 2 }}
            onClick={() => {
              setQuery(defaultQuery)
              setSelectedValues([])
            }
            }>
            {t('Mostra tutti')}
          </Button>
        </Grid>
      </Grid>
    </>
  );
}
