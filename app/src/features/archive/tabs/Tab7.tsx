import { useState } from "react";
import {
    Box,
    FormControl,
    MenuItem,
    FormControlLabel,
    Checkbox,
    IconButton,
    Grid,
    Button,
    TextField,
    Stack,
    CircularProgress,
    FormGroup,
} from "@vapor/react-material";
import Select from "@vapor/v3-components/Select";
import { useTranslation } from "@1f/react-sdk";
import { useFieldArray, useForm, Controller } from "react-hook-form";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import usePostCustom from "../../../hooks/usePostCustom";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import moment from "moment";
import { IFilterProps } from "../interfaces/archive.interface";
import { CustomSelect } from "../../../custom-components/CustomSelect";

interface DynamicField {
    id: string;
    nome: string;
    tipo: string;
    opt: any;
}

export default function Tab7(props: IFilterProps) {
    const { query, setQuery, archiveData, defaultQuery } = props;
    const { categories } = archiveData;
    const { t } = useTranslation();

    const [dynamicFields, setDynamicFields] = useState<{ [key: string]: DynamicField[] }>({});
    const [selectedDynamicFields, setSelectedDynamicFields] = useState<{ [key: string]: string }>({});
    const [fieldValues, setFieldValues] = useState<{ [key: string]: any }>({});
    const [selectedFieldTypes, setSelectedFieldTypes] = useState<{ [key: string]: string }>({});
    const [fieldOptions, setFieldOptions] = useState<{ [key: string]: any[] }>({});

    const getDynamicFields = usePostCustom('/categorie/getcampiricerca?noTemplateVars=true');
    const getFieldDetails = usePostCustom('/categorie/getcamporicerca?noTemplateVars=true');

    const { control, watch } = useForm({
        defaultValues: {
            categorySelections: [{ categoryId: '-1', showAdvanceDynamic: false }]
        }
    });

    const { fields, append, remove } = useFieldArray({
        control,
        name: "categorySelections"
    });

    const categorySelections = watch('categorySelections');

    const fetchDynamicFields = async (categoryId: string, index: number) => {
        if (categoryId === '-1') return;

        try {
            const { data }: any = await getDynamicFields.doFetch(true, { categoryId });
            setDynamicFields(prev => ({
                ...prev,
                [index]: data
            }));
        } catch (error) {
            console.error(`Error fetching dynamic fields for row ${index}:`, error);
        }
    };

    const addNewCategory = () => {
        append({ categoryId: '-1', showAdvanceDynamic: false });
    };

    const removeCategory = (index: number) => {
        if (fields.length > 1) {
            remove(index);

            const newDynamicFields = { ...dynamicFields };
            const newSelectedFields = { ...selectedDynamicFields };
            const newFieldValues = { ...fieldValues };
            const newFieldTypes = { ...selectedFieldTypes };
            const newFieldOptions = { ...fieldOptions };

            delete newDynamicFields[index];
            delete newSelectedFields[index];
            delete newFieldValues[index];
            delete newFieldTypes[index];
            delete newFieldOptions[index];

            setDynamicFields(newDynamicFields);
            setSelectedDynamicFields(newSelectedFields);
            setFieldValues(newFieldValues);
            setSelectedFieldTypes(newFieldTypes);
            setFieldOptions(newFieldOptions);
        }
    };

    const parseSelectOptions = (optString: any) => {
        try {
            if (!optString) return [];
            const parsedObj = JSON.parse(optString);

            return Object.entries(parsedObj).map(([label, value]: any) => ({
                id: value.toString(),
                nome: label
            }));
        } catch (error) {
            console.error("Error parsing select options:", error);
            return [];
        }
    };

    const handleDynamicFieldChange = async (index: number, value: string) => {
        setSelectedDynamicFields(prev => ({
            ...prev,
            [index]: value
        }));

        setFieldValues(prev => {
            const categoryId = categorySelections[index].categoryId;

            return {
                ...prev,
                [index]: {
                    categoryId: categoryId,
                    campodinamicoid: value,
                }
            };
        });

        if (value !== '-1' && dynamicFields[index]) {
            try {
                const { data }: any = await getFieldDetails.doFetch(true, { campoId: value });
                setSelectedFieldTypes(prev => ({
                    ...prev,
                    [index]: data.tipo
                }));

                if (data.tipo === 'select' && data.opt) {
                    const options = parseSelectOptions(data.opt);
                    setFieldOptions(prev => ({
                        ...prev,
                        [index]: options
                    }));
                }
            } catch (error) {
                console.error(`Error fetching field details for field ID ${value}:`, error);
            }
        }
    };

    const handleFieldValueChange = (index: number, value: any, isToDate = false) => {
        setFieldValues(prev => ({
            ...prev,
            [index]: {
                ...prev[index],
                ...(isToDate ? { valorecampo_to: value } : { valorecampo: value })
            }
        }));
    };

    const renderFieldInput = (index: number, fieldType: string) => {
        const currentFieldValue = fieldValues[index]?.valorecampo || '';

        switch (fieldType) {
            case 'date':
                return (
                    <Stack direction="row" spacing={2}>
                        <DatePicker
                            value={fieldValues[index]?.valorecampo || null}
                            onChange={(newValue: Date | null) => {
                                handleFieldValueChange(index, newValue);
                            }}
                        />
                        <DatePicker
                            value={fieldValues[index]?.valorecampo_to || null}
                            onChange={(newValue: Date | null) => {
                                handleFieldValueChange(index, newValue, true);
                            }}
                        />
                    </Stack>
                );
            case 'regex':
                return (
                    <TextField
                        label={t("")}
                        type="text"
                        value={currentFieldValue}
                        onChange={(e) => handleFieldValueChange(index, e.target.value)}
                        fullWidth
                    />
                );
            case 'select':
                const options = fieldOptions[index] || [];
                return (
                    <FormControl fullWidth>
                        <Select
                            value={currentFieldValue}
                            onChange={(e) => handleFieldValueChange(index, e.target.value)}
                        >
                            <MenuItem value="">{t("Seleziona un'opzione")}</MenuItem>
                            {options.map((option) => (
                                <MenuItem key={option.id} value={option.id}>
                                    {option.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                );
            case 'checkbox':
                const selectedField = dynamicFields[index]?.find(field => field.id === selectedDynamicFields[index]);
                const checkboxLabel = selectedField?.nome || "";

                return (
                    <FormGroup>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={fieldValues[index]?.valorecampo === true}
                                    onChange={(e) => handleFieldValueChange(index, e.target.checked)}
                                />
                            }
                            label={checkboxLabel}
                        />
                    </FormGroup>
                );
            default:
                return (
                    <Box><CircularProgress size={32} /></Box>
                );
        }
    };

    return (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            {fields.map((field, index) => (
                <Grid container spacing={2} key={field.id} alignItems="center">
                    <Grid item md={3} xs={12}>
                        <FormControl variant="outlined" fullWidth>
                            <Controller
                                name={`categorySelections.${index}.categoryId`}
                                control={control}
                                render={({ field: selectField }) => (
                                    <CustomSelect
                                        {...selectField}
                                        name={`select-label-${index}`}
                                        value={selectField.value}
                                        onChange={(e: { target: { value: any; }; }) => {
                                            const newCategoryId: any = e.target.value;
                                            selectField.onChange(e);
                                            handleDynamicFieldChange(index, '-1');

                                            if (categorySelections[index].showAdvanceDynamic && newCategoryId !== '-1') {
                                                fetchDynamicFields(newCategoryId, index);
                                            }
                                        }}>
                                        <MenuItem value="-1">{t("Tutte le categorie")}</MenuItem>
                                        {categories?.map((category: any) => (
                                            <MenuItem
                                                key={`categoriesList${category.id}`}
                                                value={category.id.toString()}>
                                                {category.nome}
                                            </MenuItem>
                                        ))}
                                    </CustomSelect>
                                )}
                            />
                        </FormControl>
                    </Grid>
                    {categorySelections[index].showAdvanceDynamic &&
                        categorySelections[index].categoryId !== '-1' &&
                        dynamicFields[index] && dynamicFields[index].length > 0 && (
                            <>
                                <Grid item md={3} xs={12}>
                                    <FormControl variant="outlined" fullWidth>
                                        <Select
                                            value={selectedDynamicFields[index] || '-1'}
                                            onChange={(e: any) => handleDynamicFieldChange(index, e.target.value)}
                                        >
                                            <MenuItem value="-1">{t("Seleziona Campo Dinamico")}</MenuItem>
                                            {dynamicFields[index].map((field: DynamicField) => (
                                                <MenuItem key={field.id} value={field.id}>
                                                    {field.nome}
                                                </MenuItem>
                                            ))}
                                        </Select>
                                    </FormControl>
                                </Grid>
                                {selectedDynamicFields[index] && selectedDynamicFields[index] !== '-1' && (
                                    <Grid item md={3} xs={12}>
                                        {renderFieldInput(index, selectedFieldTypes[index])}
                                    </Grid>
                                )}
                            </>
                        )}

                    <Grid item md={1.5} xs={3}>
                        <FormControlLabel
                            control={
                                <Controller
                                    name={`categorySelections.${index}.showAdvanceDynamic`}
                                    control={control}
                                    render={({ field: checkboxField }) => (
                                        <Checkbox
                                            {...checkboxField}
                                            checked={checkboxField.value}
                                            color="primary"
                                            onChange={(e) => {
                                                checkboxField.onChange(e);
                                                const isChecked = e.target.checked;
                                                if (isChecked && categorySelections[index].categoryId !== '-1') {
                                                    fetchDynamicFields(categorySelections[index].categoryId, index);
                                                }
                                            }}
                                        />
                                    )}
                                />
                            }
                            label={t("Mostra Campi")}
                        />
                    </Grid>

                    {index === fields.length - 1 && (
                        <Grid item md={1} xs={2}>
                            <IconButton
                                color="primary"
                                size="small"
                                onClick={addNewCategory}>
                                <AddIcon />
                            </IconButton>
                        </Grid>
                    )}
                    <Grid item md={1} xs={2} >
                        {fields.length > 1 && (
                            <IconButton
                                color="error"
                                size="small"
                                onClick={() => removeCategory(index)}>
                                <RemoveIcon />
                            </IconButton>
                        )}
                    </Grid>
                </Grid>
            ))}
            <Box sx={{ display: "flex", justifyContent: "flex-start", gap: 2, mt: 2 }}>
                <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    onClick={() => {
                        const processedFieldValues = Object.values(fieldValues)
                            .filter(value => value.categoryId !== '-1');

                        const categoryIds = processedFieldValues.map(item => item.categoryId);
                        const campodinamicoids = processedFieldValues.map(item => item.campodinamicoid);
                        const valorecampos = processedFieldValues.map(item => item?.valorecampo ?
                            typeof item.valorecampo === 'object' ?
                                moment(item.valorecampo).format("yyyy-MM-DD") :
                                item.valorecampo
                            : "");
                        const valorecampo_tos = processedFieldValues.map(item => item.valorecampo_to ? moment(item.valorecampo_to).format(
                            "yyyy-MM-DD"
                        ) : "");

                        const formatValue = (arr: any) => arr.length === 1 ? arr[0] : arr;

                        setQuery({
                            ...query,
                            categoryId: formatValue(categoryIds),
                            campodinamicoId: formatValue(campodinamicoids),
                            valorecampo: formatValue(valorecampos),
                            valorecampo_to: formatValue(valorecampo_tos)
                        });
                    }}>
                    {t("Cerca")}
                </Button>
                <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                        setQuery({ ...defaultQuery })
                        setSelectedDynamicFields({});
                        setFieldValues({});
                    }}
                >
                    {t("Mostra tutti")}
                </Button>
            </Box>
        </Box>
    );
}