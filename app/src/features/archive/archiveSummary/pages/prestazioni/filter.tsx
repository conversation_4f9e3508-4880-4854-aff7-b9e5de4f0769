import { Box, Button, TextField, Select, MenuItem, InputLabel, FormControl, Checkbox, FormControlLabel } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./interfaces/simpleList.interface";
import { DatePicker } from "../../../../../components/ui-kit/DatePicker";
import { useConfigs } from "../../../../../store/ConfigStore";
import { processDateValue } from "../../../../../helpers/dateHelper";

export default function Filters(props: IFilterProps) {
    const { defaultQuery, query, setQuery, filterData, mountedData } = props;
    const { t } = useTranslation();
    const searchWithDebounce = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0
        }));
    };

    const handleKeywordKeyPress = (e: React.KeyboardEvent<any>) => {
        if (e.key == "Enter") {
            e.preventDefault();
        }
    };
    const onChangeFilterInputs = (e: any) => {
        searchWithDebounce(e);
    };

    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const year = date.getFullYear().toString();
        return `${day}/${month}/${year}`;
    };
    const onDateChange = (name: string, value: Date) => {
        const formatedDate = formatDate(value);

        setQuery({
            ...query,
            [name]: formatedDate
        });
    };
    const { configs }: any = useConfigs();

    return (
        <>
            <Box component="form" display="flex" alignItems="end" gap={2}>
                <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                    <InputLabel id={"tipo-label"}>{t("Tipo")}</InputLabel>
                    <Select id={"typeSearch"} name={"typeSearch"} labelId={"typeSearch-label"} onChange={(e) => onChangeFilterInputs(e)} value={query["typeSearch"]}>
                        <MenuItem value="-1">{t("Tutti")}</MenuItem>
                        <MenuItem value="1">{t("Timesheet")}</MenuItem>
                        <MenuItem value="2">{t("Movimento")}</MenuItem>
                        <MenuItem value="3">{t("Impegno")}</MenuItem>
                        <MenuItem value="4">{t("Udienza")}</MenuItem>
                        <MenuItem value="5">{t("Libera")}</MenuItem>
                        <MenuItem value="6">{t("Listino")}</MenuItem>
                        <MenuItem value="7">{t("Tariffario")}</MenuItem>
                    </Select>
                </FormControl>

                <TextField label={"Nome"} placeholder={t("Testo")} variant="outlined" name={"inputSearch"} sx={{ width: 1 / 3 }} onChange={onChangeFilterInputs} onKeyPress={handleKeywordKeyPress} value={query["inputSearch"]} />

                <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                    <InputLabel id={"status-label"}>Stato</InputLabel>
                    <Select id={"statusSearch"} name={"statusSearch"} labelId={"statusSearch-label"} onChange={(e) => onChangeFilterInputs(e)} value={query["statusSearch"]}>
                        <MenuItem value="-1">{t("Tutti")}</MenuItem>
                        <MenuItem value="1">{t("In fattura")}</MenuItem>
                        <MenuItem value="2">{t("Non in fattura")}</MenuItem>
                    </Select>
                </FormControl>
            </Box>
            <Box component="form" display="flex" alignItems="end" gap={2} sx={{ mt: "1em" }}>
                <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                    <InputLabel id={"feeType-label"}>Situazione </InputLabel>
                    <Select id={"feeType"} name={"feeType"} labelId={"feeType-label"} onChange={(e) => onChangeFilterInputs(e)} value={query["feeType"]}>
                        <MenuItem value="-1">{t("-")}</MenuItem>
                        {(mountedData.feeTypes || []).map((row: any, i: any) => {
                            return (
                                <MenuItem value={row.id} key={row.id + "-" + i}>
                                    {row.name}
                                </MenuItem>
                            );
                        })}
                    </Select>
                </FormControl>
                <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                    <InputLabel id={"fatturabile-label"}></InputLabel>
                    <Select id={"fatturabile"} name={"fatturabile"} labelId={"fatturabile-label"} onChange={(e) => onChangeFilterInputs(e)} value={query["fatturabile"]}>
                        <MenuItem value="-1">{t("Tutti")}</MenuItem>
                        <MenuItem value="1">{t("Non Addebitabile")}</MenuItem>
                        <MenuItem value="2">{t("Addebitabile")}</MenuItem>
                        <MenuItem value="3">{t("Addebitabile Non Fatturabile")}</MenuItem>
                        <MenuItem value="4">{t("Addebitabile Fatturabile")}</MenuItem>
                        <MenuItem value="5">{t("Da Fatturare")}</MenuItem>
                        <MenuItem value="6">{t("Fatturate")}</MenuItem>
                        {configs.config("app.write_off_bool") && <MenuItem value="7">{t("Write Off")}</MenuItem>}
                    </Select>
                </FormControl>
            </Box>
            <Box component="form" display="flex" alignItems="end" gap={2} sx={{ mt: "1em" }}>
                <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                    <InputLabel id={"userSearch-label"}>Utente </InputLabel>
                    <Select id={"userSearch"} name={"userSearch"} labelId={"userSearch-label"} onChange={(e) => onChangeFilterInputs(e)} value={query["userSearch"]}>
                        <MenuItem value="-1">{t("Tutti")}</MenuItem>
                        {(mountedData.usersList || []).map((row: any, i: any) => {
                            return (
                                <MenuItem value={row.id} key={row.id + "-" + i}>
                                    {row.nomeutente}
                                </MenuItem>
                            );
                        })}
                    </Select>
                </FormControl>

                <div style={{ width: "25%" }}>
                    <InputLabel>{t("Dal")}</InputLabel>
                    <DatePicker 
                        label={t("Dal:")} 
                        name="itemStartDate" 
                        value={processDateValue(query.itemStartDate)} 
                        onChange={(date: Date | null) => {
                            if (date) {
                                onDateChange('itemStartDate', date);
                            }
                        }} 
                    />
                </div>
                <div style={{ width: "25%" }}>
                    <InputLabel>{t("al")}</InputLabel>
                    <DatePicker 
                        label={t("al:")} 
                        name="itemEndDate" 
                        value={processDateValue(query.itemEndDate)} 
                        onChange={(date: Date | null) => {
                            if (date) {
                                onDateChange('itemEndDate', date);
                            }
                        }} 
                    />
                </div>

                <FormControlLabel
                    control={
                        <Checkbox
                            checked={query.voicesWithAmount || false} // Bind to query state
                            onChange={(e: any) =>
                                setQuery({
                                    ...query,
                                    voicesWithAmount: e.target.checked // Update query state
                                })
                            }
                            name="voicesWithAmount"
                            color="primary"
                        />
                    }
                    label={t("Solo voci con importi")} // Add a label for the checkbox
                />

                <Button onClick={() => filterData(query)} variant="contained" color="primary" type="button">
                    {t("Cerca")}
                </Button>

                <Button variant="contained" color="primary" onClick={() => setQuery(defaultQuery)}>
                    {t("Mostra tutti")}
                </Button>
            </Box>
        </>
    );
}
