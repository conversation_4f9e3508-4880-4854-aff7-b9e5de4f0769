import { useState, useEffect } from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContentText,
    Divider,
    FormGroup,
} from "@vapor/react-material";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import usePostCustom from "../../../../hooks/usePostCustom";
import { useForm, useFieldArray, Controller } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import FormInput from "../../../../custom-components/FormInput";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import useGetCustom from "../../../../hooks/useGetCustom";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { Typography, TextField, Stack } from "@mui/material";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import moment from "moment";

const getSchemaValidation = () => {
    return yup.object().shape({
        addBasket: yup.string(),
        fileUniqueid: yup.string(),
        basketId: yup.string(),
        date_from: yup.date(),
        allMembers: yup.string().nullable(),
        users: yup.array(),
    });
};

const CreateUpdatePercentualiModal = (props: any) => {
    const {
        openModal,
        setOpenModal,
        fetchAllData,
        typeOfModal,
        fileUniqueid,
        baskets,
    } = props;

    const { t } = useTranslation();

    const [rowData, setRowData] = useState<any[]>([]);
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const [showModalConfirmDelete, setShowModalConfirmDelete] =
        useState<boolean>(false);

    const updateRequest = useGetCustom(
        "archive-profits-distribution/update?noTempleteVars=true"
    );

    const checkDateRequest = usePostCustom(
        "archive-profits-distribution/check-date?noTempleteVars=true"
    );

    const saveRequest = useGetCustom(
        "archive-profits-distribution/save?noTempleteVars=true"
    );

    const usersGetList = usePostCustom(
        `archive-profits-distribution/get-users-for-list?noTemplateVars=true`
    );

    const fileBasketRequest = usePostCustom(
        `archive-profits-distribution/get-file-baskets?noTemplateVars=true`
    );

    const usersBasketRequest = usePostCustom(
        `archive-profits-distribution/get-users-for-basket?noTemplateVars=true`
    );

    const deleteSlectedReq = usePostCustom(
        "archive-profits-distribution/delete-perc?noTemplateVars=true"
    );

    const { control, handleSubmit, setValue, watch, register } = useForm({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            fileUniqueid: "",
            addBasket: "1",
            basketId:
                typeOfModal !== "update"
                    ? `${baskets[0]?.id}-${moment().format("DD/MM/YYYY")}`
                    : `${rowData[0]?.id}-${rowData[0]?.date_from}`,
            date_from: new Date(),
            allMembers: "",
            users: [],
        },
    });

    const { fields, append } = useFieldArray({ control, name: "users" });

    const values = watch();

    const basketIdValue = watch("basketId");
    const selectedBasketId = basketIdValue?.split("-")[0];
    const selectedDateBasket = basketIdValue?.split("-")[1];

    useEffect(() => {
        if (typeOfModal === "update") getFileBasket();
    }, []);

    useEffect(() => {
        async function initRow() {
            const users = await getListUsers();
            await getBasketUsers(users);
        }

        if (typeOfModal === "update" && selectedBasketId !== "undefined") {
            initRow();
        }

        if (typeOfModal === "create") {
            getListUsers();
        }
    }, [selectedBasketId, values.allMembers]);

    const deletePerc = async () => {
        if (values.basketId) {
            setShowModalConfirmDelete(true);
        } else {
            alert(t("Non sono presenti percentuali da eliminare"));
        }
    };

    const handleModalConfirm = async (confirm: boolean) => {
        if (confirm) {
            const formData = new FormData();
            formData.append("fileUid", fileUniqueid as string);
            formData.append("basketId", selectedBasketId ?? "");
            formData.append("dateFrom", selectedDateBasket ?? "");
            const response: any = await deleteSlectedReq.doFetch(
                true,
                formData
            );
            if (response.data) {
                setOpenModal(false);
                fetchAllData();
            }
        }
        setShowModalConfirmDelete(false);
    };

    const getFileBasket = async () => {
        try {
            const response: any = await fileBasketRequest.doFetch(true, {
                fileUid: fileUniqueid,
            });

            setRowData(response.data);
            setValue(
                "basketId",
                `${response.data[0].id}-${response.data[0].date_from}`
            );
            setValue(
                "date_from",
                typeOfModal === "update"
                    ? response.data[0].date_from
                    : moment().format("DD/MM/YYYY")
            );
        } catch (error) {
            console.log("Error", error);
            return;
        }
    };

    const confermaButton = async () => {
        onSubmit(values);
        setOpenModal(!openModal);
    };

    const getListUsers = async () => {
        const isOnCreate = typeOfModal === "create";
        const paramsToSend: any = {
            fileUid: fileUniqueid,
            basketId: isOnCreate ? false : selectedBasketId,
            // date: isOnCreate ? false : values.date_from,
            date: isOnCreate ? false : selectedDateBasket,
        };

        paramsToSend.allMembers = values.allMembers === "1";

        const response: any = await usersGetList.doFetch(true, {
            ...paramsToSend,
        });

        setValue("users", []);

        response.data.map((user: any) => {
            const userBasket = response.data.find(
                (item: any) => item.id === user.id
            );

            append({
                ...userBasket,
                percentuale: userBasket?.percentuale || "0.00",
                sob: userBasket?.sob || "0.00",
                nome: user.nome,
                userId: user.id,
            });
        });

        return response.data;
    };

    const getBasketUsers = async (users: any[]) => {
        const paramsToSend = {
            fileUniqueid,
            basketId: selectedBasketId,
            dateFrom: selectedDateBasket,
        };

        const response: any = await usersBasketRequest.doFetch(true, {
            ...paramsToSend,
        });

        if (!response.data.error) {
            const usersData: any[] = [];
            users.map((user: any) => {
                const userBasket = response.data.list.find(
                    (item: any) => item.id === user.id
                );
                usersData.push({
                    ...userBasket,
                    percentuale: userBasket?.percentuale || "0.00",
                    sob: userBasket?.sob || "0.00",
                    nome: user.nome,
                    userId: user.id,
                });
            });

            setValue("users", usersData);
        } else {
            alert(t("Errore nel recupero dei dati."));
        }
    };

    const onSubmit = async (data: any) => {
        const params: any = {
            fileUniqueid,
            date_from:
                data.date_from && typeof data.date_from === "string"
                    ? data.date_from
                    : moment(data.date_from).format("DD/MM/YYYY"),
        };

        if (typeOfModal === "create") {
            const checkDateResponse: any = await checkDateRequest.doFetch(
                true,
                {
                    basketId: selectedBasketId,
                    dateFrom: params.date_from,
                    fileUid: fileUniqueid,
                }
            );

            const match = checkDateResponse.data?.match(/^"(\d+)"{/);
            const firstValue = match ? match[1] : null;

            if (firstValue !== "0") {
                setShowErrorMessage(true);
                return;
            }
        }

        if (values.allMembers) {
            params.allMembers = "on";
        }
        data.users.forEach((user: any) => {
            params[`rowUtente${user.userId}`] =
                typeOfModal === "update" ? user.id_row : "";
            params[`percUtente${user.userId}`] = user.percentuale;
            params[`sobUtente${user.userId}`] = user.sob;
        });

        if (typeOfModal === "update") {
            const dataToSplit = data.basketId;
            const datePart = dataToSplit.split("-")[1];
            params.modBasket = selectedBasketId;
            params.date_from = datePart;
            await updateRequest.doFetch(true, params);
            fetchAllData();
            setOpenModal(false);
            return;
        }
        params.addBasket = selectedBasketId;
        await saveRequest.doFetch(true, params);
        setOpenModal(false);
        fetchAllData();
    };

    return (
        <>
            <ConfirmModal
                open={showModalConfirmDelete}
                handleDecline={() => handleModalConfirm(false)}
                handleAgree={() => handleModalConfirm(true)}
                decline={t("Annulla")}
                agree={t("Conferma")}
                title={t("Conferma")}
                confirmText={t(
                    "Procedendo con l'eliminazione verranno cancellate le percentuali di ogni socio collegato al basket selezionato"
                )}
            />

            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={t("Selezionare una data valida per il nuovo basket")}
            />
            <Dialog
                open={openModal}
                onClose={() => setOpenModal(!openModal)}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle>
                    {typeOfModal != "update"
                        ? t("Aggiungi percentuali")
                        : t("Modifica percentuali")}

                    <Box display="flex" alignItems="right">
                        <IconButton
                            color="primary"
                            onClick={() => setOpenModal(!openModal)}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <FormGroup>
                                    <FormInput
                                        control={control}
                                        name="basketId"
                                        type="select"
                                        label={t("Basket ")}
                                        options={
                                            typeOfModal !== "update"
                                                ? baskets.map((item: any) => ({
                                                      value: item?.date_from
                                                          ? `${item.id}-${item.date_from}`
                                                          : `${
                                                                item.id
                                                            }-${moment(
                                                                values.date_from
                                                            ).format(
                                                                "DD/MM/YYYY"
                                                            )}`,
                                                      label: `${item.nome}`,
                                                  }))
                                                : rowData.map((item: any) => ({
                                                      value: `${item.id}-${item.date_from}`,
                                                      label: `${item.nome} ~ ${item.date_from}`,
                                                  }))
                                        }
                                        sx={{ width: 1 }}
                                    />
                                </FormGroup>

                                {typeOfModal != "update" && (
                                    <Controller
                                        name="date_from"
                                        control={control}
                                        render={({ field: { value, onChange } }) => (
                                            <DatePicker
                                                label={t("Dal")}
                                                value={value}
                                                onChange={onChange}
                                            />
                                        )}
                                    />
                                )}

                                <FormGroup>
                                    <FormInput
                                        name="allMembers"
                                        control={control}
                                        type="checkbox"
                                        options={[
                                            {
                                                label: t("Mostra tutti i soci"),
                                                value: "1",
                                            },
                                        ]}
                                    />
                                </FormGroup>

                                {fields.map((user: any, index: number) => {
                                    return (
                                        <Box
                                            key={user.id}
                                            sx={{
                                                border: "1px solid #ccc",
                                                borderRadius: "4px",
                                                padding: "16px",
                                                display: "flex",
                                                flexDirection: "column",
                                                marginTop: "1rem",
                                            }}
                                        >
                                            {/* Title */}
                                            <Typography
                                                variant="h6"
                                                sx={{ marginBottom: "8px" }}
                                            >
                                                {user.nome}
                                            </Typography>

                                            {/* Inputs Section */}
                                            <Stack
                                                direction="row"
                                                alignItems="center"
                                                spacing={2}
                                            >
                                                {/* First Section */}
                                                <Typography variant="body1">
                                                    {t("Utile")}
                                                </Typography>
                                                <TextField
                                                    variant="outlined"
                                                    size="small"
                                                    type="number"
                                                    sx={{ maxWidth: "100px" }}
                                                    {...register(
                                                        `users.${index}.percentuale`
                                                    )}
                                                    InputProps={{
                                                        endAdornment: (
                                                            <Typography
                                                                sx={{ mr: 1 }}
                                                            >
                                                                %
                                                            </Typography>
                                                        ),
                                                    }}
                                                />

                                                {/* Second Section */}
                                                <Typography variant="body1">
                                                    {t("SOB")}
                                                </Typography>
                                                <TextField
                                                    variant="outlined"
                                                    size="small"
                                                    type="number"
                                                    sx={{ maxWidth: "100px" }}
                                                    {...register(
                                                        `users.${index}.sob`
                                                    )}
                                                    InputProps={{
                                                        endAdornment: (
                                                            <Typography
                                                                sx={{ mr: 2 }}
                                                            >
                                                                %
                                                            </Typography>
                                                        ),
                                                    }}
                                                />
                                            </Stack>
                                        </Box>
                                    );
                                })}
                            </form>
                        </Box>
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="outlined"
                        onClick={() => setOpenModal(!openModal)}
                    >
                        {t("Annulla")}
                    </Button>
                    {typeOfModal != "create" && (
                        <Button
                            color="error"
                            variant="outlined"
                            onClick={deletePerc}
                        >
                            {t("Elimina")}
                        </Button>
                    )}

                    <Button
                        variant="contained"
                        type="submit"
                        onClick={confermaButton}
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    );
};

export default CreateUpdatePercentualiModal;
