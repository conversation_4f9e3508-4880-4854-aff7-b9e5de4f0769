import React, {useState, useEffect } from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContentText,
    Divider,
    FormGroup,
    Typography,
    FormControl,
    MenuItem,
    InputLabel
} from "@vapor/react-material";
import { DatePicker } from "../../../../../../components/ui-kit/DatePicker";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useForm, Controller } from "react-hook-form";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import  FormInput  from "../../../../../../custom-components/FormInput";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import moment from "moment";
import ToastNotification from "../../../../../../custom-components/ToastNotification";
import ConfirmModal from "../../../../../../custom-components/ConfirmModal";
import  { CustomSelect } from "../../../../../../custom-components/CustomSelect";
const getSchemaValidation = () => {
    return yup.object().shape({
          relazione: yup
            .string()
            .required()
            .notOneOf(["-1"], "Selezionare un utente!"),
        relazioneUtente: yup.string().required(),
        inizioMandatoUtenteInserimento: yup.date(),
        riservazione:yup.string()
    });
};

const UpdateUtenteModal = (props: any) => {
    const {
        fileUniqueid,
        openModal,
        toggleUserModal,
        internals,
        fetchAllData,
        relazioniutenti,
        rowToUpdate
    } = props;


    const { control, handleSubmit, watch, setValue, getValues } = useForm({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            relazione: "-1",
            relazioneUtente: "1",
            inizioMandatoUtenteInserimento: undefined,
            riservazione:  "0",
        },
    });

    const [showErrorMessage, setShowErrorMessage] = useState<boolean>(false);
    const [notificationText, setNotificationText] = useState("");
    const [notificationSeverity, setNotificationSeverity] = useState<"error" | "warning" | "info" | "success">("info");
    const [deleteModalConfirm, setDeleteModalConfirm] = useState(false);
    const [oneDriveReserveResponse, setOneDriveReserveResponse] = useState<number>(1);


  useEffect(() => {
    if (rowToUpdate) {
      setValue('relazione', rowToUpdate.id);
      setValue('relazioneUtente', rowToUpdate.id_relazione);
      setValue('riservazione', String(rowToUpdate.id_riserva));

      if(rowToUpdate.data_iniziomandato){
      const [day, month, year] = rowToUpdate.data_iniziomandato.split('/'); 
      const dateObj = new Date(`${year}-${month}-${day}`);
      setValue('inizioMandatoUtenteInserimento', dateObj);}
    }
  }, [rowToUpdate, setValue]);

    const { t } = useTranslation();
    const checkboxValueRequest = usePostCustom("archiveanagrafiche/getcheckboxvalue?noTemplateVars=true");
    const removeResponsibileRequest = usePostCustom("archiveanagrafiche/removeresponsabile?noTemplateVars=true");
    const addResponsabileRequest = usePostCustom("archiveanagrafiche/aggiungiresponsabile?noTemplateVars=true");
    const addUtentePracticaRequest = usePostCustom("archiveanagrafiche/addutentepratica?noTemplateVars=true");
    const checkRemoveRequest = usePostCustom("archiveanagrafiche/checkremove?noTemplateVars=true");
    const getIdisevraRequest = usePostCustom("archiveanagrafiche/getidriserva?noTemplateVars=true");
    const reserveFileRequest = usePostCustom("archiveanagrafiche/reservefile?noTemplateVars=true");
    const unreserveFileRequest = usePostCustom(`archiveanagrafiche/unreservefile?noTemplateVars=true`);
    const oneDrivePermissionsRequest = usePostCustom(`archiveanagrafiche/handle-one-drive-permissions?noTemplateVars=true`);
    const addCointestatarioRequest = usePostCustom(`archiveanagrafiche/addcointestatario?noTemplateVars=true`);
    const removeUtentePraticaRequest = usePostCustom(`archiveanagrafiche/removeutentepratica?noTemplateVars=true`);
    const rimuoviCointestatarioRequest = usePostCustom(`/archiveanagrafiche/removecointestatario?noTemplateVars=true`);


    const updateUtenteRequest = usePostCustom(`archiveanagrafiche/updutentepratica?noTemplateVars=true`);

    const setIntestatarioRequest = usePostCustom("archiveanagrafiche/set-intestatario-pratica?noTemplateVars=true");

    const checkFoldersRequest = usePostCustom("archiveanagrafiche/checkfolders?noTemplateVars=true");


    const addCointestatario = async (codiceavvocato: string, file_uid: string) => {
    
        const {data}:any = await addCointestatarioRequest.doFetch(true, 
        {
            id : codiceavvocato , 
            file_uid : file_uid,
        });

        if (data == 0){
            alert(t("Si è verificato un errore!"));
        }
    }

    const removeResponsabile = async (file_uid: any, modifica: number) => {
        const {data}:any = await removeResponsibileRequest.doFetch(true, 
        {
            file_uid : file_uid, 
            modifica: modifica,
        });
    
        if (data === 0){
            alert(t("Si è verificato un errore!"));
            return -1;
        }else{
            return data;
        }
    }

    const aggiungiResponsabile = async (previousReferent: any, id: string, file_uid: string) => {

        const formData = new FormData();
        formData.append("id", id);
        formData.append("file_uid", file_uid);
        formData.append("previous", JSON.stringify(previousReferent));
        const { data }:any = await addResponsabileRequest.doFetch(true, formData);
    
        if (data === 0){
            alert(t("Si è verificato un errore!"));
        }
    }

    const addUtentePratica = async (previousRelazione:string ,previousRelazioneName?:string, currentRelazioneName?:string) => {

        const addUtenteFormData = new FormData();
        const values =  getValues();
        const utente = internals.find((utente: any) => utente.id.toString() === values.relazione);
        addUtenteFormData.append("id", values.relazione);
        addUtenteFormData.append("relazione", values.relazioneUtente);
        addUtenteFormData.append("file_uid", fileUniqueid);
        addUtenteFormData.append("logsData[nome_utente]",utente.nomeutente);
        addUtenteFormData.append(`logsData[tipo_action]`,`aggiornato - da ${previousRelazioneName} a ${currentRelazioneName}`);
        addUtenteFormData.append("logsData[relazione_precedente]",previousRelazione);

        addUtenteFormData.append("data_iniziomandato",moment(values.inizioMandatoUtenteInserimento).format("YYYY-MM-DD"));

        const {data}:any = await addUtentePracticaRequest.doFetch(true, addUtenteFormData);
        if (data.success === false) alert(data.message);
    }

    const removeUtentePratica = async ( relazione: string) => {

        const removeUtenteFormData = new FormData();
        const values =  getValues();
        removeUtenteFormData.append("id", relazione);
        removeUtenteFormData.append("relazione", values.relazioneUtente);
        removeUtenteFormData.append("file_uid", fileUniqueid);
        removeUtenteFormData.append("logsData", "false");
        await removeUtentePraticaRequest.doFetch(true, removeUtenteFormData);
    
    }
    const addAccessoInterno = async (file_uid: string, id: string) => {
        await reserveFileRequest.doFetch(true, 
        {
            person : id, 
            fileUniqueid : file_uid, 
            massive : true
        });
    }

    const riservaUtente = async (riserva: boolean, file_uid: string, id: string) => {
        if (riserva == true){
            addAccessoInterno(file_uid, id);
            fetchAllData();
        }else{
            let data: any;
            let results;

            if (riserva) {
                await reserveFileRequest.doFetch(true, { person: id, fileUniqueid: file_uid });
            } else {
                data = await checkRemoveRequest.doFetch(true, { id, fileUniqueid: file_uid });
                if (data.data !== null) {               
                    results = data.data;
                    if(results === 0 || results === "0"){
                        setOneDriveReserveResponse(0);
                        setNotificationText("Non è possibile rimuovere la riserva all'intestatario quando ci sono altre riserve!");
                        setNotificationSeverity("error");
                        setShowErrorMessage(true);
                        fetchAllData();
                        
                    } else if(results === 1 || results === "1"){
                        const response:any =  await getIdisevraRequest.doFetch(true, { id, file_uid: file_uid });
                        if(response.data != "-1"){
                            const formData = new FormData();
                            formData.append("id", response.data);
                            formData.append("fileUniqueid", file_uid);
                         
                            await unreserveFileRequest.doFetch(true, formData)   
                          
                        }                                             
                        fetchAllData();

                    } else if(results === 3 || results === "3"){
                        setOneDriveReserveResponse(3);
                        setNotificationText("Attenzione! È necessario eliminare prima gli altri utenti.");
                        setNotificationSeverity("warning");
                        setShowErrorMessage(true);
                        
                        fetchAllData();
                    } else if(results === 4 || results === "4"){
                        setOneDriveReserveResponse(1);
                        fetchAllData();
                    }
                }
            }
        }
    }

    const handleOneDrivePermissions = async ({ id, riserva, type }: any) => {
        if (oneDriveReserveResponse !== 0){
            try {
                const formData = new FormData();
                formData.append("id", id);
                formData.append("riserva", riserva);
                formData.append("type", type);
                formData.append("file_uid", fileUniqueid);
                
            const response:any=await oneDrivePermissionsRequest.doFetch(true, formData);
            if(response.data === 0){
                setNotificationText("Si è verificato un errore!");
                setNotificationSeverity("error");
                setShowErrorMessage(true);
            }
            } catch (error) {
                console.error("Error handling OneDrive permissions:", error);
            }
        }
    };

    const updateCalls = async (data: any) => {
    
        if(rowToUpdate.id_relazione != data.relazioneUtente){
            if(rowToUpdate.codiceavvocato != "-1" && data.relazioneUtente === "1" && rowToUpdate.tipoutente === "1"){
                await riservaUtente(data.riservazione === "1", fileUniqueid, data.relazione);
                if(rowToUpdate.id_relazione ==="2" || rowToUpdate.id_relazione === 2){
                    await removeResponsabile(fileUniqueid, 0);;
                }
                const previousRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === rowToUpdate.id_relazione);
                const currentRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === data.relazioneUtente);
                await addUtentePratica(rowToUpdate.id_relazione, previousRelazioneName.nome.toLowerCase(),currentRelazioneName.nome.toLowerCase());
                await removeUtentePratica(data.relazione);
                const user = internals.find((user: any) => user.id === data.relazione);
                await addCointestatario(user.codiceavvocato, fileUniqueid);
                await handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione === "1", type: 0 });
            } else if(rowToUpdate.codiceavvocato != "-1" && rowToUpdate.id_relazione === "1"){
                await riservaUtente(data.riservazione === "1", fileUniqueid, data.relazione);
                const previousRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === rowToUpdate.id_relazione);
                const currentRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === data.relazioneUtente);
                await addUtentePratica(rowToUpdate.id_relazione, previousRelazioneName.nome.toLowerCase(),currentRelazioneName.nome.toLowerCase());
                await removeUtentePratica(data.relazione);
                await rimuoviCointestatarioRequest.doFetch(true, { id : rowToUpdate.codiceavvocato , file_uid : fileUniqueid });
                if (data.relazioneUtente == 2){ 
                    const previousReferente:any = await removeResponsabile(fileUniqueid, 0);
                    await aggiungiResponsabile(previousReferente, data.relazione, fileUniqueid);   
                } 
                await handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione === "1", type: 0 });
            } else if(rowToUpdate.tipoutente != 1 && data.relazione == 1 || (rowToUpdate.codiceavvocato == -1 && data.relazione == 1))  {
                setNotificationText("L'utente non può essere cointestatario");
                setNotificationSeverity("error");
                setShowErrorMessage(true);
            } else if(data.relazioneUtente === "2"){
                await riservaUtente(data.riservazione, fileUniqueid, data.relazione);
                const previousReferente:any = await removeResponsabile(fileUniqueid, 0);
                
                await aggiungiResponsabile(previousReferente, data.relazione, fileUniqueid);
                const previousRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === rowToUpdate.id_relazione);
                const currentRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === data.relazioneUtente);
                await addUtentePratica(rowToUpdate.id_relazione, previousRelazioneName.nome.toLowerCase(),currentRelazioneName.nome.toLowerCase());
                await removeUtentePratica(data.relazione);
                if (rowToUpdate.id_relazione === "1"){  
                    await rimuoviCointestatarioRequest.doFetch(true, { id : rowToUpdate.codiceavvocato , file_uid : fileUniqueid });
                }
                await handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione === "1", type: 0 });
            
            } else if (rowToUpdate.id_relazione === "2"){ 
                await riservaUtente(data.riservazione, fileUniqueid, data.relazione);
                const previousRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === rowToUpdate.id_relazione);
                const currentRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === data.relazioneUtente);
                await addUtentePratica(rowToUpdate.id_relazione, previousRelazioneName.nome.toLowerCase(),currentRelazioneName.nome.toLowerCase());
                await removeUtentePratica(data.relazione);
                await removeResponsabile(fileUniqueid, 1);
                if (data.relazioneUtente === "1"){  
                    await addCointestatario(rowToUpdate.codiceavvocato, fileUniqueid);
                }
                await handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione, type: 0 });
              
            } else {
                await riservaUtente(data.riservazione, fileUniqueid, data.relazione);
                const previousRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === rowToUpdate.id_relazione);
                const currentRelazioneName = relazioniutenti.find((relazione: any) => relazione.id === data.relazioneUtente);
                await addUtentePratica(rowToUpdate.id_relazione, previousRelazioneName.nome.toLowerCase(),currentRelazioneName.nome.toLowerCase());
                await removeUtentePratica(data.relazione);
                await handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione, type: 0 });
            }
        } else {
            await updateUtenteRequest.doFetch(true, { 
                id: rowToUpdate.id,
                file_uid: fileUniqueid,
                relazione: data.relazioneUtente,
                data_iniziomandato: data.data_iniziomandato,
            });
            await riservaUtente(data.riservazione === "1", fileUniqueid, data.relazione);
            await handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione === "1", type: 1 });
        }
    } 

    const relazione = watch("relazione");

    React.useEffect(() => {
        const checkboxFetch = async () => {
            const formData = new FormData();
            formData.append("id", relazione);
            formData.append("fileUniqueid",fileUniqueid);
            const response: any = await checkboxValueRequest.doFetch(true,formData)
            setValue("riservazione", response.data === 1 ? "1" : "0");
        }
        checkboxFetch();
    }, [relazione]);


    const onSubmit = async (data: any) => {
        await updateCalls(data);
        fetchAllData();
        toggleUserModal(true)
    };
 
    const setIntestatario = async () => {
        let params = {
            avvocato: rowToUpdate.id,
            fileId: fileUniqueid
        };
        const removeResponse: any = await setIntestatarioRequest.doFetch(true,
            params,
            "post",
            "json",
            true
        )   
        if(removeResponse.data.success){
            fetchAllData()
            toggleUserModal(true)
            
        }
    }

    const deleteUtente = async () => {
        const formData = new FormData();
        formData.append("userId",rowToUpdate.id);
        const checkFoldersResponse: any = await checkFoldersRequest.doFetch(true,formData)
        if (checkFoldersResponse.data === 0 || checkFoldersResponse.data === "0"){
            setDeleteModalConfirm(true) 
        }
    }

    const handleModalDelete = async (confirm: boolean) => {

        const deleteformData = new FormData();
        const values =  getValues();
        const utente = internals.find((utente: any) => utente.id.toString() === values.relazione);
        deleteformData.append("id", values.relazione);
        deleteformData.append("relazione", values.relazioneUtente);
        deleteformData.append("file_uid", fileUniqueid);
        deleteformData.append("logsData[nome_utente]",utente.nomeutente);
        deleteformData.append(`logsData[tipo_action]`,"cancellato");
        if (confirm){
            if (rowToUpdate.id_relazione == 1){
             await removeUtentePraticaRequest.doFetch(true, deleteformData);
             await rimuoviCointestatarioRequest.doFetch(true, { id : rowToUpdate.codiceavvocato , file_uid : fileUniqueid });
            } else if (rowToUpdate.id_relazione == 2){
                await removeResponsabile(fileUniqueid, 0);
            } else {
                await removeUtentePraticaRequest.doFetch(true, deleteformData);
            }
            await handleOneDrivePermissions({ id: values.relazione, riserva: "0", type: 2 });
            fetchAllData();
            toggleUserModal(true)       
        }         
    }


    return (
        <>
            <Dialog
                open={openModal}
                onClose={toggleUserModal}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <ConfirmModal
                open={deleteModalConfirm}
                decline={t("Annulla")}
                agree={t("Conferma")}
                title={t("Attenzione")}
                 
                handleAgree={() => handleModalDelete(true)}
               
                handleDecline={() => setDeleteModalConfirm(false)}
                confirmText={t("Attenzione, questo utente è indicato come unico possessore dei permessi di visualizzazione di cartelle di documenti.Rimuovere questo utente dai soggetti renderà le suddette cartelle visibili a tutti gli utenti della pratica. Continuare?")}
            />

                <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity={notificationSeverity}
                text={t(notificationText)}
            />
                <DialogTitle>
                    {t("Modifica Utente")}

                    <Box display="flex" alignItems="right">
                        <IconButton
                            color="primary"
                            onClick={toggleUserModal}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <form onSubmit={handleSubmit(onSubmit)}>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >

                        <FormControl variant='outlined' fullWidth  >
                        <InputLabel id='select-label'>{t("Utente")}</InputLabel>
                        <CustomSelect       
                            value={relazione}
                            onChange={(e: any) => setValue("relazione", e.target.value)}
                            name='relazione'
                            group='attivo'
                            dataSource={internals}
                            valueKey='id'
                            disabled={true}
                            
                        >
                           <MenuItem value='-1'>{t("Seleziona un utente")}</MenuItem>
                            {internals
                            ?.filter((internal: any) => internal.attivo === "1")
                            .map((internal: any) => (
                                <MenuItem key={internal.id} value={internal.id}>
                                {internal.nomeutente}
                                </MenuItem>
                            ))}
                        
                        </CustomSelect>
                        </FormControl>
                        </Box>
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                            <FormGroup>
                                <FormInput
                                    control={control}
                                    name="relazioneUtente"
                                    type="select"
                                    label={t("Relazione")}
                                    options={relazioniutenti.map((relazioniutent: any) => ({
                                        value: relazioniutent.id,
                                        label: relazioniutent.nome,
                                    }))}
                                    sx={{ width: 1 }}
                                />
                            </FormGroup>  
                        </Box>
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                        <Controller
                            name="inizioMandatoUtenteInserimento"
                            control={control}
                            render={({ field: { value, onChange } }) => (
                                <DatePicker
                                    label={t("Inizio mandato")}
                                    value={value}
                                    onChange={onChange}
                                />
                            )}
                        /></Box>
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                        <FormGroup>
                            <FormInput
                                name="riservazione"
                                control={control}
                                type="checkbox"
                                options={[
                                    {
                                    label: t("Riserva Accesso"),
                                    value: "1",
                                    },
                                    ]}
                            />
                        </FormGroup>
                        </Box>
                        <Box display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}>
                            <Typography variant="body" gutterBottom component="div">
                                <b>{t("N.B.")}</b>
                                {t("La Spunta di Riserva Accesso influenzerà ogni altra relazione della stessa utenza. In presenza di riserva l'intestatario sarà sempre presente.")}
                            </Typography>
                        </Box>
                    </DialogContentText>
                </DialogContent>
                <DialogActions sx={{  width: '100%' }}>  
                    {rowToUpdate.tipoutente === "1" && (  <Button
                        variant="outlined"
                        onClick={setIntestatario}
                        sx={{ marginRight: 7 }}                       
                    >
                        {t("Imposta intestatario")}
                    </Button>)}                 
                    
                    <Button
                        variant="outlined"
                        onClick={toggleUserModal}                      
                    >
                        {t("Annulla")}
                    </Button>

                       <Button
                        variant="outlined"
                        color="error"
                        disabled={rowToUpdate.id_riserva != "-1"}
                        onClick={deleteUtente}
                    >
                        {t("Rimuovi")}
                    </Button>

                    <Button
                        variant="contained"
                        type="submit"
                        
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
                 </form>
            </Dialog>
        </>
    );
};

export default UpdateUtenteModal;
