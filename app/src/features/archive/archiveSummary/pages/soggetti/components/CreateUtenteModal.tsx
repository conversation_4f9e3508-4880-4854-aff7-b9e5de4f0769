import React, { useState } from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContentText,
    Divider,
    FormGroup,
    Typography,
    FormControl,
    InputLabel,
    MenuItem
} from "@vapor/react-material";
import ToastNotification from "../../../../../../custom-components/ToastNotification";
import { DatePicker } from "../../../../../../components/ui-kit/DatePicker";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { useForm, Controller } from "react-hook-form";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import  FormInput  from "../../../../../../custom-components/FormInput";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import moment from "moment";
import { CustomSelect } from "../../../../../../custom-components/CustomSelect";
const getSchemaValidation = () => {
    return yup.object().shape({
          relazione: yup
            .string()
            .required()
            .notOneOf(["-1"], "Selezionare un utente!"),
        relazioneUtente: yup.string().required(),
        inizioMandatoUtenteInserimento: yup.date(),
        riservazione:yup.string()
    });
};

const CreateSoggettiModal = (props: any) => {
    const {
        fileUniqueid,
        openModal,
        toggleUserModal,
        internals,
        fetchAllData,
        relazioniutenti
    } = props;

    const { control, handleSubmit, watch, setValue, getValues } = useForm({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            relazione: "-1",
            relazioneUtente: "1",
            inizioMandatoUtenteInserimento: undefined,
            riservazione:  "0",
        },
    });
    const [showErrorMessage, setShowErrorMessage] = useState<boolean>(false);
    const [notificationText, setNotificationText] = useState("");
    const [notificationSeverity, setNotificationSeverity] = useState<"error" | "warning" | "info" | "success">("info");

    let one_drive_reserve_responses: any = []

    const { t } = useTranslation();
    const checkboxValueRequest = usePostCustom("archiveanagrafiche/getcheckboxvalue?noTemplateVars=true");
    const removeResponsibileRequest = usePostCustom("archiveanagrafiche/removeresponsabile?noTemplateVars=true");
    const addResponsabileRequest = usePostCustom("archiveanagrafiche/aggiungiresponsabile?noTemplateVars=true");
    const addUtentePracticaRequest = usePostCustom("archiveanagrafiche/addutentepratica?noTemplateVars=true");
    const checkRemoveRequest = usePostCustom("archiveanagrafiche/checkremove?noTemplateVars=true");
    const getIdisevraRequest = usePostCustom("archiveanagrafiche/getidriserva?noTemplateVars=true");
    const reserveFileRequest = usePostCustom("archiveanagrafiche/reservefile?noTemplateVars=true");
    const unreserveFileRequest = usePostCustom(`archiveanagrafiche/unreservefile?noTemplateVars=true`);
    const oneDrivePermissionsRequest = usePostCustom(`archiveanagrafiche/handle-one-drive-permissions?noTemplateVars=true`);
    const addCointestatarioRequest = usePostCustom(`archiveanagrafiche/addcointestatario?noTemplateVars=true`);


    const addCointestatario = async (codiceavvocato: string, file_uid: string) => {
    
        const {data}:any = await addCointestatarioRequest.doFetch(true, 
        {
            id : codiceavvocato , 
            file_uid : file_uid,
        });

        if (data == 0){
            setNotificationText("Si è verificato un errore!");
            setNotificationSeverity("error");
            setShowErrorMessage(true);
            
        }
    }

    const removeResponsabile = async (file_uid: any, modifica: number) => {
        const {data}:any = await removeResponsibileRequest.doFetch(true, 
        {
            file_uid : file_uid, 
            modifica: modifica,
        });
    
        if (data === 0){
            setNotificationText("Si è verificato un errore!");
            setNotificationSeverity("error");
            setShowErrorMessage(true);
            return -1;
        }else{
            return data;
        }
    }

    const aggiungiResponsabile = async (previousReferent: any, id: string, file_uid: string) => {
        const { data }:any = await addResponsabileRequest.doFetch(true, 
        {
            id : id, 
            file_uid : file_uid, 
            previous : previousReferent 
        });
    
        if (data === 0){
            setNotificationText("Si è verificato un errore!");
            setNotificationSeverity("error");
            setShowErrorMessage(true);
        }
    }
  

    const addUtentePratica = async () => {

        const addUtenteFormData = new FormData();
        const values =  getValues();
        const utente = internals.find((utente: any) => utente.id.toString() === values.relazione);
    
        addUtenteFormData.append("id", values.relazione);
        addUtenteFormData.append("relazione", values.relazioneUtente);
        addUtenteFormData.append("file_uid", fileUniqueid);
        addUtenteFormData.append("logsData[nome_utente]",utente.nomeutente);
        addUtenteFormData.append("logsData[tipo_action]","inserito");
        addUtenteFormData.append("data_iniziomandato",moment(values.inizioMandatoUtenteInserimento).format("YYYY-MM-DD"));

        const {data}:any = await addUtentePracticaRequest.doFetch(true, addUtenteFormData);
        if (data.success === false){
            setNotificationText(data.message);
            setNotificationSeverity("error");
            setShowErrorMessage(true);
        }
    }

    const addAccessoInterno = async (file_uid: string, id: string) => {
        await reserveFileRequest.doFetch(true, 
        {
            person : id, 
            fileUniqueid : file_uid, 
            massive : true
        });
    }


    const riservaUtente = async (riserva: boolean, file_uid: string, id: string) => {
        if (riserva == true){
            addAccessoInterno(file_uid, id);
        }else{
            let data: any;
            let results;

            if (riserva) {
                await reserveFileRequest.doFetch(true, { person: id, fileUniqueid: file_uid });
            } else {
                data = await checkRemoveRequest.doFetch(true, { id, fileUniqueid: file_uid });

                 if (data.data !== null) { 

                    results = data?.data;
                    if(results === 0 || results === "0"){
                        one_drive_reserve_responses = 0;
                        setNotificationText("Non è possibile rimuovere la riserva all'intestatario se ci sono altre riserve!");
                        setNotificationSeverity("error");
                        setShowErrorMessage(true);
                        fetchAllData();
                        
                    } else if(results === 1 || results === "1"){
                        const response:any =  await getIdisevraRequest.doFetch(true, { id, file_uid: file_uid });
                        if(response.data != "-1"){
                            const formData = new FormData();
                            formData.append("id", response.data);
                            formData.append("fileUniqueid", file_uid);
                            await unreserveFileRequest.doFetch(true, formData);
                        }                                             
                        fetchAllData();

                    } else if(results === 3 || results === "3"){
                        one_drive_reserve_responses = 3;
                        setNotificationText("Attenzione! È necessario eliminare prima gli altri utenti.");
                        setNotificationSeverity("warning");
                        setShowErrorMessage(true);
                        
                        fetchAllData();
                    } else if(results === 4 || results === "4"){
                        one_drive_reserve_responses = 1;
                        fetchAllData();
                    }
                }
            }
        }
    }

    

    const handleOneDrivePermissions = async ({ id, riserva, type }: any) => {
        try {
            if (one_drive_reserve_responses.length) {
                for (const item of one_drive_reserve_responses) {
                    const data = await oneDrivePermissionsRequest
                        .doFetch(true, { id, riserva, file_uid: item.uid, type })
                        .catch(err => console.error("Error in OneDrive permissions:", err));

                    if (data === 0) {
                        console.error("An error occurred while handling permissions!");
                    }
                }
            }
        } catch (error) {
            console.error("Error handling OneDrive permissions:", error);
        }
    };


    const updateCalls = async (data: any) => {
        const utente = internals.find((utente: any) => utente.id.toString() === data.relazioneUtente);     
        if(data.relazioneUtente === "2"){
            const previousReferente:any = await removeResponsabile(fileUniqueid, 0);
            await aggiungiResponsabile(previousReferente, data.relazione, fileUniqueid);
            await addUtentePratica();
            await riservaUtente(data.riservazione === "1", fileUniqueid, data.relazione);
            handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione === "1", type: 1 });
        } else if(utente.codiceavvocato != "-1" && data.relazioneUtente === "1" && utente.tipoutente === "1") {
            const user = internals.find((user: any) => user.id === data.relazione);
            await addCointestatario(user.codiceavvocato, fileUniqueid);
            await addUtentePratica();
            await riservaUtente(data.riservazione === "1", fileUniqueid, data.relazione);
            handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione === "1", type: 1 });
        }
        else{
            await addUtentePratica();
            await riservaUtente(data.riservazione === "1", fileUniqueid, data.relazione);
            handleOneDrivePermissions({ id: data.relazione, riserva: data.riservazione === "1", type: 1 });
        }
    } 

    const relazione = watch("relazione");

    React.useEffect(() => {
        const checkboxFetch = async () => {
            const formData = new FormData();
            formData.append("id", relazione);
            formData.append("fileUniqueid",fileUniqueid);
            const response: any = await checkboxValueRequest.doFetch(true,formData)
            setValue("riservazione", response.data === 1 ? "1" : "0");
        }
        checkboxFetch();
    }, [relazione]);


    const onSubmit = async (data: any) => {
        await updateCalls(data);
        toggleUserModal(true)
    };


 


    return (
        <>
            <Dialog
                open={openModal}
                onClose={toggleUserModal}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
            >

            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity={notificationSeverity}
                text={t(notificationText)}
            />
                <DialogTitle>
                    {t("Aggiungi Utente")}

                    <Box display="flex" alignItems="right">
                        <IconButton
                            color="primary"
                            onClick={toggleUserModal}
                        >
                            <Close />
                        </IconButton>
                    </Box>
                </DialogTitle>
                <Divider className="MuiDivider-VaporLight" />
                <form onSubmit={handleSubmit(onSubmit)}>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >

                        <FormControl variant='outlined' fullWidth  >
                        <InputLabel id='select-label'>{t("Utente")}</InputLabel>
                        <CustomSelect
                            value={relazione}
                            onChange={(e: any) => setValue("relazione", e.target.value)}
                            name='relazione'
                            group='attivo'
                            dataSource={internals}
                            valueKey='id'
                            
                        >
                           <MenuItem value='-1'>{t("Seleziona un utente")}</MenuItem>
                            {internals
                            ?.filter((internal: any) => internal.attivo === "1")
                            .map((internal: any) => (
                                <MenuItem key={internal.id} value={internal.id}>
                                {internal.nomeutente}
                                </MenuItem>
                            ))}
                        
                        </CustomSelect>
                        </FormControl>
                        </Box>
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                            <FormGroup>
                                <FormInput
                                    control={control}
                                    name="relazioneUtente"
                                    type="select"
                                    label={t("Relazione")}
                                    options={relazioniutenti.map((relazioniutent: any) => ({
                                        value: relazioniutent.id,
                                        label: relazioniutent.nome,
                                    }))}
                                    sx={{ width: 1 }}
                                />
                            </FormGroup>  
                        </Box>
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                        <Controller
                            name="inizioMandatoUtenteInserimento"
                            control={control}
                            render={({ field: { value, onChange } }) => (
                                <DatePicker
                                    label={t("Inizio mandato")}
                                    value={value}
                                    onChange={onChange}
                                />
                            )}
                        /></Box>
                        <Box
                            display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}
                        >
                         <FormGroup>
                                <FormInput
                                    name="riservazione"
                                    control={control}
                                    type="checkbox"
                                    options={[
                                        {
                                        label: t("Riserva Accesso"),
                                        value: "1",
                                        },
                                        ]}
                                    />
                                </FormGroup>
                                </Box>
                        <Box display="flex"
                            gap={1}
                            sx={{
                                mt: 2,
                                "& > :not(style)": {
                                    m: 1,
                                    width: 450,
                                },
                            }}>
                            <Typography variant="body" gutterBottom component="div">
                                <b>{t("N.B.")}</b>
                                {t("La Spunta di Riserva Accesso influenzerà ogni altra relazione della stessa utenza. In presenza di riserva l'intestatario sarà sempre presente.")}
                            </Typography>
                        </Box>
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button
                        variant="outlined"
                        onClick={toggleUserModal}
                    >
                        {t("Annulla")}
                    </Button>

                    <Button
                        variant="contained"
                        type="submit"
                        
                    >
                        {t("Conferma")}
                    </Button>
                </DialogActions>
                 </form>
            </Dialog>
        </>
    );
};

export default CreateSoggettiModal;
