import { useState, useEffect , useRef} from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    MenuItem,
    Select,
    FormControl,
    Divider,
    InputLabel,
    TextField,
    Button,
    Typography,
    IconButton,
    Grid,
} from "@vapor/react-material";
import { DatePicker } from "../../../../../../components/ui-kit/DatePicker";
import useGetCustom from "../../../../../../hooks/useGetCustom";
import { CustomDataGrid } from "../../../../../../custom-components/CustomDataGrid";
import usePostCustom from "../../../../../../hooks/usePostCustom";
import { debounce } from "lodash";
import {
    GridPaginationModel,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import { useAnagrafichePraticheHook } from "../../../../../anagrafiche/hooks/useAnagraficheHook";
import { updateParamsPratiche } from "../../../../../anagrafiche/helpers/anagraficheList";
import AddSingleAnagraficaModal from "./AddSingleAnagraficaModal";
import AddMultipleAnagrafiche from "./AddMultipleAnagrafiche";
import { AnagrafichePraticheAction } from "../../../../../../interfaces/anagrafiche.interface";
import { getAnagraficheRelationsGrid } from "../../../../../../utilities/anagrafiche/gridColumn";
import moment from "moment";
export const DEFAULT_LIST_PARAMS = {
    noTemplateVars: true,
    forceStatus: true,
    page: 0,
    pageSize: 10,
    sortColumn: "nome",
    sortOrder: "asc",
    searchField: "",
    startAnagraficheSearch: null,
    endAnagraficheSearch: null,
    categoryId: "-1",
};

const AnagraficheModal = (props: any) => {
    const {
        fileUniqueid,
        openModal,
        setOpenModal,
        roles,
        fetchSoggettiData
    } = props;

    const { t } = useTranslation();
    const [defaultParams, setDefaultParams] = useState<any>({
        ...DEFAULT_LIST_PARAMS,
        
        startAnagraficheSearch: moment().subtract(1, 'year').format("DD/MM/YYYY"),
        endAnagraficheSearch: moment().format("DD/MM/YYYY"),
    });
    const [relationsDefaultParams, setRelationsDefaultParams] = useState<any>({
        noTemplateVars: true,
        id: "",
        page: 0,
        pageSize: 10,
        sortColumn: "denominazione",
        sortOrder: "asc"   
    });

    const [data, setData] = useState<any[]>([]);
    const [totalRows, setTotalRows] = useState<number>(0);  
    const [relationsData, setRelationsData] = useState<any[]>([]);
    const [relationsTotalRows, setRelationsTotalRows] = useState<number>(0);
    const resetClicked = useRef<boolean>(false);
    const [addSingleAnagraficaModal, setAddSingleAnagraficaModal] = useState(false);
    const [addMultipleAnagraficheModal, setAddMultipleAnagraficheModal] = useState(false);
    const [rowToUpdate, setRowToUpdate] = useState<any>(null);
    const [relationsColumns, setRelationsColumns] = useState<any[]>([]);
    const [showRelationsTable, setShowRelationsTable] = useState(false);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
  
    const [date, setDate] = useState({
        startAnagraficheSearch:  new Date(new Date().setFullYear(new Date().getFullYear() - 1)),
        endAnagraficheSearch: new Date(),
    });


    const handleShowRelations = (row: any) => {
        setRelationsDefaultParams({
            ...relationsDefaultParams,
            id: row.id
        });
        
        startRelationsSearchList();
        setShowRelationsTable(true);
    };
  
    

    const actions: AnagrafichePraticheAction = {
        handleShowRelations: handleShowRelations
    } as any;

    const { columns, categorie } =
        useAnagrafichePraticheHook(actions);

    const anagraficheListRequest = useGetCustom(
        "anagrafiche/list",
        updateParamsPratiche(defaultParams, location)
    );

    const defaultAnagraficheListRequest = useGetCustom(
        "anagrafiche/list",
        updateParamsPratiche(DEFAULT_LIST_PARAMS, location)
    );

    const relationsAnagraficheListRequest = useGetCustom(
        "anagrafiche/get-people-bindings", 
        relationsDefaultParams
    );

    const preparedToAddToFileRequest = usePostCustom("anagrafiche/prepareaddtofile?noTemplateVars=true");

    const onSubmit = () => {
        startSearchList();
    };

      const updateDate = async () => {
        setDate((prevDate: any) => ({
        ...prevDate,
        startAnagraficheSearch: null,
        endAnagraficheSearch: null,
    }));
    
  };
    const onClickReset = () => {
        resetClicked.current = true;
        setQueryAndLocalStorage(DEFAULT_LIST_PARAMS);
        startSearchList(true);
        updateDate();
    };


    const openAddSingleAnagraficaModal = () => {
        setAddSingleAnagraficaModal(!addSingleAnagraficaModal)
    };

    const openAddMultipleAnagraficheModal = () => {
        setAddMultipleAnagraficheModal(!addMultipleAnagraficheModal)
    };

    useEffect(() => {
        const debouncedSearch = debounce(() => {
            startSearchList();
        }, 500);
        defaultParams.searchField && debouncedSearch();
        return () => {
            debouncedSearch.cancel();
        };
    }, [defaultParams.searchField]);


    useEffect(() => {
        if (!resetClicked.current) {
            startSearchList();
        }
    }, [
        defaultParams.page,
        defaultParams.pageSize,
        defaultParams.sortColumn,
        defaultParams.sortOrder,
        defaultParams.categoryId,
        defaultParams.startAnagraficheSearch,
        defaultParams.endAnagraficheSearch,
    ]);

      useEffect(() => {
        startRelationsSearchList();    
    }, [
        relationsDefaultParams.page,
        relationsDefaultParams.pageSize,
        relationsDefaultParams.id,
        relationsDefaultParams.sortColumn,
        relationsDefaultParams.sortOrder,
    ]);


    const startSearchList = async (reset = false) => {
        const response: any = await (reset
            ? defaultAnagraficheListRequest.doFetch(true)
            : anagraficheListRequest.doFetch(true));

        if (reset) {
            resetClicked.current = false;
        }

        const { currentPage, totalRows } = response.data;
        setData(currentPage);
        setTotalRows(totalRows);
    };

    const startRelationsSearchList = async () => {
        const response: any = await relationsAnagraficheListRequest.doFetch(true);
        const { currentPage, totalRows } = response.data;
        setRelationsData(currentPage);
        setRelationsTotalRows(totalRows);
        const finalColumns: any = await getAnagraficheRelationsGrid(t);
        setRelationsColumns(finalColumns);
    };



    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        const updated = {
        ...defaultParams,
        [name]: value,
    };
        setQueryAndLocalStorage(updated);
       
    };

    const setQueryAndLocalStorage = (query: any) => {
        setDefaultParams(query);
       
    };

    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const year = date.getFullYear().toString();
        return `${day}/${month}/${year}`;
    };

    const onDateChange = (name: string, value: Date) => {
        setDate((prevValue: any) => ({ ...prevValue, [name]: value }));
        const formatedDate = formatDate(value);
         const updated = {
        ...defaultParams,
        [name]: formatedDate,
    };
        setQueryAndLocalStorage(updated);
       
    };

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQueryAndLocalStorage({
            ...defaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onPageChangeRelationsCallback = (model: GridPaginationModel) => {
        setRelationsDefaultParams({
            ...relationsDefaultParams,
            page: model.page,
            pageSize: model.pageSize,
        });
    };
        const handleRowSelection = (
        rowSelectionModel: GridRowSelectionModel,
        
    ) => {
        setSelectedRows(rowSelectionModel as string[]);
       
    };



    const selectedMultipleRowData = relationsData.filter(item => selectedRows.includes(item.uniqueid));
   

    const handleClickCallback =  async (row: any) => {
        const formData = new FormData();
        formData.append("uId", row.uniqueid);
        formData.append("fileUniqueid",fileUniqueid);
        const response: any = await preparedToAddToFileRequest.doFetch(true,formData);
        setRowToUpdate(response.data);
        setShowRelationsTable(false);
        openAddSingleAnagraficaModal()
        setSelectedRows([])

    };

    const handleClickRelationsCallback = async(uniqueId: any) => {
        const formData = new FormData();
        formData.append("uId", uniqueId);
        formData.append("fileUniqueid",fileUniqueid);
        const response: any = await preparedToAddToFileRequest.doFetch(true,formData);
        setRowToUpdate(response.data);
        openAddSingleAnagraficaModal()
   
    };

    const handleAvantiButton = async () => {
        const formData = new FormData();
        formData.append("uId", "");
        formData.append("fileUniqueid",fileUniqueid);
        const response: any = await preparedToAddToFileRequest.doFetch(true,formData);
        setRowToUpdate(response.data);
        openAddMultipleAnagraficheModal()
       
    }

    const renderDataTable = () => {
        return (
            <CustomDataGrid
                name="peopleArchive"
                columns={columns}
                data={data}
                page={defaultParams.page}
                totalRows={totalRows}
                pageSize={defaultParams.pageSize}
                loading={
                    anagraficheListRequest.loading ||
                    defaultAnagraficheListRequest.loading
                }
                query={defaultParams}
                setQuery={setQueryAndLocalStorage}
                onPageChangeCallback={onPageChangeCallback}
                onClickCallback={handleClickCallback}
                onClickKey="row"
                onClickCheckboxKey="row"
            />
        );
    };

     const renderRelationsDataTable = () => {
        return (
            <CustomDataGrid
                name="peopleRelations"
                columns={relationsColumns}
                data={relationsData}
                page={relationsDefaultParams.page}
                totalRows={relationsTotalRows}
                pageSize={relationsDefaultParams.pageSize}
                loading={
                    relationsAnagraficheListRequest.loading
                }
                query={relationsDefaultParams}
                setQuery={setRelationsDefaultParams}
                onPageChangeCallback={onPageChangeRelationsCallback}
                onClickCallback={handleClickRelationsCallback}
                onClickKey="uniqueid"
                onClickCheckboxKey="uniqueid"
                selectableRows
                onRowSelectionModelChange={handleRowSelection}
                hasAdditionaStyles={false}
            />
        );
    };

    return (
        <>
       
            {addSingleAnagraficaModal && (
                <AddSingleAnagraficaModal
                    fileUniqueid={fileUniqueid}
                    openModal={openAddSingleAnagraficaModal}
                    setOpenModal={setAddSingleAnagraficaModal}
                    fetchAllData={() => startSearchList()}
                    rowToUpdate={rowToUpdate}
                    initialRoles={roles}
                    fetchSoggettiData={fetchSoggettiData}
                     setOpenMainModal={setOpenModal}
                />
            )}
            {addMultipleAnagraficheModal && (
                <AddMultipleAnagrafiche
                    fileUniqueid={fileUniqueid}
                    openModal={openAddMultipleAnagraficheModal}
                    setOpenModal={setAddMultipleAnagraficheModal}
                    setOpenMainModal={setOpenModal}
                    fetchAllData={() => startSearchList()}
                    selectedMultipleRowData={selectedMultipleRowData}
                    rowToUpdate={rowToUpdate}
                    initialRoles={roles}
                    fetchSoggettiData={fetchSoggettiData}
                />
            )}
            <Dialog
                open={openModal}
                onClose={() => setOpenModal(false)}
                aria-labelledby="alert-dialog-title"
                aria-describedby="alert-dialog-description"
                fullWidth
                maxWidth="lg"
            >
                <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '16px 24px' }}>
                    <Typography variant="h6">{t("Seleziona soggetti")}</Typography>
                       <Box display="flex" gap={1}>
                        {selectedRows.length > 0 && ( <Button
                            variant="outlined"
                            color="primary"
                             onClick={handleAvantiButton}
                            >
                                {t("Avanti")}
                            </Button>)}
                        
                            <IconButton
                                color="primary"
                                onClick={() => setOpenModal(false)}
                                sx={{ padding: '4px' }}
                            >
                            <Close />
                            </IconButton>
                       </Box> 
                </DialogTitle>
                <Divider />
                <DialogContent sx={{ padding: '16px 24px' }}>   
                                  
                    <Box sx={{ mb: 2 }}>             
                        <Box display="flex" alignItems="end" gap={2}>
                            <TextField
                                label={`${t("Denominazione / Codice fiscale: ")}:`}
                                variant="outlined"
                                value={defaultParams.searchField}
                                name="searchField"
                                sx={{ width: 1 / 4 }}
                                onChange={onChangeInput}
                        />
                        <div style={{ width: "25%" }}>
                            <DatePicker
                                label={`${t("Dal")}:`}
                                name="startAnagraficheSearch"
                                value={date.startAnagraficheSearch}
                                onChange={(dateValue: Date | null) => {
                                    if (dateValue) {
                                        onDateChange('startAnagraficheSearch', dateValue);
                                    }
                                }}
                            />
                        </div>
                        <div style={{ width: "25%" }}>
                            <DatePicker
                                label={`${t("Al")}:`}
                                name="endAnagraficheSearch"
                                value={date.endAnagraficheSearch}
                                onChange={(dateValue: Date | null) => {
                                    if (dateValue) {
                                        onDateChange('endAnagraficheSearch', dateValue);
                                    }
                                }}
                            />
                        </div>
                    </Box>
                        
                    <Box display="flex" alignItems="end" gap={1} sx={{ mt:2}}>
                        <Box display="flex" alignItems="end"  sx={{ width: 1 / 4}} >
                            <FormControl variant="outlined" sx={{ width: 1 }}>
                                <InputLabel id="select-label">
                                    {t("Categorie")}:
                                </InputLabel>
                                <Select
                                    labelId="select-label"
                                    value={defaultParams.categoryId}
                                    label={t("Tutte le categorie")}
                                    onChange={onChangeInput}
                                    name="categoryId"
                                >
                                    <MenuItem value={-1}>
                                        {t("Tutte le categorie")}
                                    </MenuItem>
                                    {categorie?.map((category: any) => (
                                        <MenuItem key={category.id} value={category.id}>
                                            {category.nome}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>
                        <Box display="flex" gap={1} >
                            <Button
                                variant="contained"
                                color="primary"
                                type="submit"
                                onClick={onSubmit}
                            >
                            {t("Ricerca")}
                            </Button>

                            <Button variant="outlined" color="primary" onClick={onClickReset}>
                            {t("Mostra tutti")}
                            </Button>
                        </Box>
                    </Box>
                    </Box>
                {showRelationsTable ? (
                   <Grid container spacing={3} >
                    <Grid item xs={12} md={6}>
                        {renderDataTable()}
                    </Grid>
                     <Grid item xs={12} md={6} sx={{ minHeight: '40px' }}>
                        {renderRelationsDataTable()}
                    </Grid>
                 </Grid>
                 )
                 :(
                 <Grid container spacing={2}>
                    <Grid item xs={12} md={12}>
                        {renderDataTable()}
                    </Grid>
                 </Grid>
                )}
                    
                </DialogContent>
            </Dialog>
        </>
    );
};

export default AnagraficheModal; 