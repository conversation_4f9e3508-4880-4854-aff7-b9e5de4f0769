import { useTranslation } from "@1f/react-sdk";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { Typography } from "@vapor/react-extended";
import { useNavigate, useSearchParams } from "react-router-dom";
import { format } from "date-fns";
import useGetCustom from "../../../../hooks/useGetCustom";

export const ToggleArchiveSummary = ({
    showArchiveModal,
    setShowArchiveModal,
    is_archived,
}: {
    showArchiveModal: boolean;
    is_archived: string | undefined;
    setShowArchiveModal: Dispatch<SetStateAction<boolean>>;
}) => {
    const { t } = useTranslation();
    const navigate = useNavigate();

    const [searchParams] = useSearchParams();

    const uid = searchParams.get("uid");

    const [date, setDate] = useState(new Date());
    const [showConfirm, setShowConfirm] = useState(false);
    const [save, setSave] = useState(false);

    const toggleArchiveResponse = useGetCustom("archive/toggleisarchived");

    useEffect(() => {
        if (save && uid) {
            toggleArchiveResponse.doFetch(true, {
                uid: uid,
                archiveDate:
                    is_archived === "1"
                        ? format(date, "dd/MM/yyyy")
                        : format(new Date(), "dd/MM/yyyy"),
                flag: is_archived === "1" ? "0" : "1",
            });
        }
    }, [save, uid, date, is_archived]);

    useEffect(() => {
        if (toggleArchiveResponse.hasLoaded) {
            navigate("/archive/archive");
        }
    }, [toggleArchiveResponse.hasLoaded]);

    const handleDecline = () => {
        setShowArchiveModal(false);
        setShowConfirm(false);
        setSave(false);
    };

    const handleChangeDate = (_name: string, date: Date) => {
        setDate(date);
    };

    const handleCancelConfirm = () => {
        setShowConfirm(false);
        setShowArchiveModal(false);
        setSave(false);
    };

    const handleShowConfirm = () => {
        if (is_archived === "1") {
            setSave(true);
        } else {
            setShowConfirm(true);
        }
        setShowArchiveModal(false);
    };

    const handleConfirm = () => {
        setSave(true);
    };

    return showConfirm ? (
        <ConfirmModal
            agree={t("Conferma")}
            decline={t("Annulla")}
            handleAgree={handleConfirm}
            handleDecline={handleCancelConfirm}
            open={showConfirm}
            title="Data Archiviazione"
            loading={toggleArchiveResponse.loading}>
            <Typography>
                {is_archived === "1" 
                    ? t("Ripristinare la pratica? Sara' di nuovo visualizzata nella lista delle pratiche.")
                    : t("Procedere all'archiviazione della pratica?")}
            </Typography>
            {is_archived === "0" && (
                <Typography>
                    {t(
                        "Sarà possibile visualizzarla nuovamente utilizzando la ricerca avanzata."
                    )}
                </Typography>
            )}
        </ConfirmModal>
    ) : (
        <ConfirmModal
            agree={t("Conferma")}
            decline={t("Annulla")}
            handleAgree={handleShowConfirm}
            handleDecline={handleDecline}
            dividerVariant="fullWidth"
            open={showArchiveModal}
            title={t("Data Archiviazione")}>
            {is_archived === "0" && (
                <DatePicker
                    label={t("Seleziona data")}
                    name=""
                    onChange={handleChangeDate}
                    value={date}
                />
            )}
            {is_archived === "1" && (
                <Typography>
                    {t("Ripristinare la pratica? Sara' di nuovo visualizzata nella lista delle pratiche.")}
                </Typography>
            )}
        </ConfirmModal>
    );
};
