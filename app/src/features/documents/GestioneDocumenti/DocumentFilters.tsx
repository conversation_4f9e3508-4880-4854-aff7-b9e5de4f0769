import { <PERSON>ack, TextField, FormControl, InputLabel, Button, Select, MenuItem } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faXmark } from "@fortawesome/pro-regular-svg-icons";
import { DocumentFiltersProps } from "../../../interfaces/documents.interface";
import { useTranslation } from "@1f/react-sdk";
import { SetStateAction, useEffect, useState } from "react";
import { useConfigs } from "../../../store/ConfigStore";
import { PracticeSearch } from "../../../custom-components/PracticeSearch";
import { useSearchContracts } from "./hooks/SearchContracts";
import { PRACTICE_SUBJECT_ROLES, SIGNATURE_STATUS, VISIBILITY_OPTIONS } from "./Constants";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import CustomAutocomplete from "../../../custom-components/CustomAutocomplete";

export const DocumentFilters = ({ handleDateChange, onSelectChange, onTextFieldChange, resetSearchParams, searchParams, setSearchParams, data, hasLoaded }: DocumentFiltersProps) => {
    const { t } = useTranslation();

    const [fileSearchInput, setFileSearchInput] = useState(searchParams.fileSearch);

    const [permissions, setPermissions] = useState({
        practices_bool: false,
        contracts_bool: false,
        customer_contrattualistica_bool: false
    });

    const contractSearchResponse = useSearchContracts({
        from: "",
        query: ""
    });

    const { configs }: any = useConfigs();

    useEffect(() => {
        if (configs.data.app) {
            setPermissions({
                practices_bool: configs.data.app.practices_bool,
                contracts_bool: false,
                customer_contrattualistica_bool: configs.data.app.customer_contrattualistica_bool
            });
        }
    }, [configs]);

    const handlePracticeSearchChange = (_e: any, value: any) =>
        setSearchParams({
            ...searchParams,
            fileSearch: value
        });

    const handleSearch = () => {
        setSearchParams({
            ...searchParams,
            search: true
        });
    };

    const handlePracticeInputChange = (_e: any, value: SetStateAction<string>) => setFileSearchInput(value);

    return (
        <Stack rowGap={2} columnGap={3} direction="row" flexWrap="wrap">
            <TextField sx={{ width: 300 }} id="titolodocumento" label={t("Descrizione / Nome file")} onChange={onTextFieldChange} value={searchParams.titolodocumento}></TextField>
            <TextField sx={{ width: 300 }} label={t("Note")} id="note" onChange={onTextFieldChange} value={searchParams.note}></TextField>
            <FormControl>
                <InputLabel>{t("Categoria")}</InputLabel>
                <Select sx={{ width: 300 }} value={searchParams.searchCategories} name="searchCategories" onChange={onSelectChange}>
                    {hasLoaded && data.documentcategories.map((category: any) => <MenuItem value={category.id}>{category.nome}</MenuItem>)}
                </Select>
            </FormControl>
            {permissions.practices_bool && (
                <PracticeSearch
                    width={300}
                    onChange={handlePracticeSearchChange}
                    onInputChange={handlePracticeInputChange}
                    inputValue={fileSearchInput}
                    id="fileSearch"
                    query={fileSearchInput}
                    value={searchParams.fileSearch}
                    // target=""
                ></PracticeSearch>
            )}
            {permissions.contracts_bool && (
                <FormControl>
                    <InputLabel>{t("Contrato")}</InputLabel>
                    <CustomAutocomplete sx={{ width: 300 }} loading={contractSearchResponse.loading} options={contractSearchResponse.hasLoaded ? contractSearchResponse.data : []} renderInput={(params: any) => <TextField placeholder={t("Cerca contratto per codice, identificativo, titolo")} {...params}></TextField>} getOptionLabel={(option: any) => option.headerArchive} renderOption={(_params: any, option: any) => <MenuItem>{option.headerArchive}</MenuItem>}></CustomAutocomplete>
                </FormControl>
            )}

            <FormControl>
                <InputLabel>{t("Documento di")}</InputLabel>
                <Select sx={{ width: 300 }} value={searchParams.documentoDi} name="documentoDi" onChange={onSelectChange}>
                    {hasLoaded && data.lawyers.map((lawyer: any) => <MenuItem value={lawyer.id}>{lawyer.nomeutente}</MenuItem>)}
                </Select>
            </FormControl>
            <FormControl>
                <InputLabel>{t("Visibile")}</InputLabel>
                <Select sx={{ width: 300 }} value={searchParams.visibility} name="visibility" onChange={onSelectChange}>
                    {VISIBILITY_OPTIONS.map((option) => (
                        <MenuItem value={option.value}>{t(option.label)}</MenuItem>
                    ))}
                </Select>
            </FormControl>
            {permissions.customer_contrattualistica_bool && (
                <FormControl>
                    <InputLabel>{t("Stato firma")}</InputLabel>
                    <Select sx={{ width: 300 }} value={searchParams.signatureStatus} name="signatureStatus" onChange={onSelectChange}>
                        {SIGNATURE_STATUS.map((option) => (
                            <MenuItem value={option.value}>{t(option.label)}</MenuItem>
                        ))}
                    </Select>
                </FormControl>
            )}
            {permissions.practices_bool && <TextField sx={{ width: 300 }} label={t("Soggetto pratica")} id="archiveSubject" onChange={onTextFieldChange} value={searchParams.archiveSubject}></TextField>}

            <FormControl disabled={searchParams.archiveSubject === ""}>
                <InputLabel>Ruolo soggetto pratica</InputLabel>
                <Select sx={{ width: 300 }} name="archiveSubjectRelation" value={searchParams.archiveSubjectRelation} onChange={onSelectChange}>
                    {PRACTICE_SUBJECT_ROLES.map((role) => (
                        <MenuItem value={role.value}>{t(role.label)}</MenuItem>
                    ))}
                </Select>
            </FormControl>

            <div style={{ width: "25%" }}>
                <DatePicker 
                    label="Dal" 
                    value={searchParams.startDate} 
                    onChange={(date: Date | null) => {
                        if (date) handleDateChange("startDate", date);
                    }} 
                />
            </div>

            <div style={{ width: "25%" }}>
                <DatePicker 
                    label="al" 
                    value={searchParams.endDate} 
                    onChange={(date: Date | null) => {
                        if (date) handleDateChange("endDate", date);
                    }} 
                />
            </div>
            <Stack direction="row" gap={2} alignItems="flex-end">
                <Button variant="contained" onClick={handleSearch} startIcon={<FontAwesomeIcon icon={faSearch} />}>
                    {t("Cerca")}
                </Button>
                <Button startIcon={<FontAwesomeIcon icon={faXmark} />} onClick={resetSearchParams}>
                    {t("Annulla")}
                </Button>
            </Stack>
        </Stack>
    );
};
