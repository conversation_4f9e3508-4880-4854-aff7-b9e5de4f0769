import { TextField, FormControl, InputLabel, Stack, Select, MenuItem, Checkbox, FormControlLabel, ListItem, Button } from "@vapor/react-material";
import { format } from "date-fns";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import { useSearchDocumentObject } from "../hooks/SearchDocumentObject";
import { useState } from "react";
import type { DocumentData, ModifyDocumentDetails } from "../../../../interfaces/documents.interface";
import { useSearchArchive } from "../hooks/useSearchArchive";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

export const DocumentTab = ({ documentDetails, documentData, handleUpdateDocumentDetails, userName, showRename, fromDatabaseFileUniqueId }: { userName: any; handleUpdateDocumentDetails: (field: keyof ModifyDocumentDetails) => (value: any) => void; documentDetails: ModifyDocumentDetails; documentData: DocumentData; showRename: boolean; fromDatabaseFileUniqueId: string }) => {
    const [objectQuery] = useState("");
    const [archiveQuery] = useState("");

    const searchDocumentObjectResponse = useSearchDocumentObject({
        q: objectQuery,
        increment: ""
    });

    const searchArchiveResponse = useSearchArchive({
        q: archiveQuery,
        increment: ""
    });

    const [showVisible, setShowVisible] = useState(false);

    const { t } = useTranslation();

    return (
        <Stack direction="column" gap={3} alignItems="flex-start" width={300}>
            <TextField sx={{ width: 300 }} id="descrizione" label={t("Descrizione")} onChange={(e: any) => handleUpdateDocumentDetails("nomefileForBtn")(e.target.value)} value={documentDetails.nomefileForBtn}></TextField>
            {fromDatabaseFileUniqueId === "" && (
                <FormControl>
                    <InputLabel>{t("Pratica")}</InputLabel>
                    <CustomAutocomplete
                        sx={{ width: 300 }}
                        loading={searchArchiveResponse.loading}
                        noOptionsText={t("Nessun Pratica documento trovato")}
                        value={documentDetails?.documentFileUniqueid}
                        onInputChange={(_: any, newInputValue: any) => handleUpdateDocumentDetails("documentFileUniqueid")(newInputValue)}
                        options={searchArchiveResponse.hasLoaded ? searchArchiveResponse.data.map((value: any) => value.headerArchive.replace(/(<([^>]+)>)/gi, "")) : []}
                        renderInput={(params: any) => <TextField placeholder={t("Cerca pratica per codice, descrizione, nominativi, RG...")} {...params} />}
                        getOptionLabel={(option: any) => option}
                        renderOption={(props: any, option: any) => (
                            <ListItem {...props} value={option}>
                                {option}
                            </ListItem>
                        )}
                    ></CustomAutocomplete>
                </FormControl>
            )}
            <FormControl sx={{ width: 300 }}>
                  <InputLabel>{t("Data")}</InputLabel>
                  <DatePicker
                    name="documentoData"
                    value={documentDetails.documentoData || null} // coerce "" to null
                    onChange={(date: Date | null) => {
                        handleUpdateDocumentDetails("documentoData")(date);
                    }}
                  />
                </FormControl>
            <TextField sx={{ width: 300 }} id="numeroprotocollo" label={t("Numero/Protocollo")} onChange={(e: any) => handleUpdateDocumentDetails("numeroprotocollo")(e.target.value)} value={documentDetails.numeroprotocollo}></TextField>
            <FormControl>
                <InputLabel>{t("Documento di")}</InputLabel>
                <Select sx={{ width: 300 }} value={documentDetails.documentodi} name="documentoDi" onChange={(e: any) => handleUpdateDocumentDetails("documentodi")(e.target.value)}>
                    {documentData.lawyers.map((lawyer: any) => (
                        <MenuItem value={lawyer.id}>{lawyer.nomeutente}</MenuItem>
                    ))}
                </Select>
            </FormControl>

            {showRename && <TextField sx={{ width: 300 }} label={t("Nuovo nome")} onChange={(e: any) => handleUpdateDocumentDetails("nuovonome")(e.target.value)} value={documentDetails?.nuovonome}></TextField>}

            <FormControl>
                <InputLabel>{t("Cartella")}</InputLabel>
                <Select sx={{ width: 300 }} value={documentDetails.folder_id} name="Cartella" onChange={(e: any) => handleUpdateDocumentDetails("folder_id")(e.target.value)}>
                    {documentDetails.folders &&
                        documentDetails.folders.map((folder, i) => (
                            <MenuItem value={folder.id} key={folder.id}>
                                {i === 0 ? t("Cartella Principale") : folder.name}
                            </MenuItem>
                        ))}
                </Select>
            </FormControl>
            <FormControl>
                <InputLabel>{t("Categoria")}</InputLabel>
                <Select sx={{ width: 300 }} value={documentDetails.category_id} name="Categoria" onChange={(e: any) => handleUpdateDocumentDetails("category_id")(e.target.value)}>
                    {documentData.documentcategories.map((category: any) => (
                        <MenuItem value={category.id}>{category.nome}</MenuItem>
                    ))}
                </Select>
            </FormControl>

            <FormControl>
                <InputLabel>{t("Oggetto")}</InputLabel>
                <CustomAutocomplete
                    sx={{ width: 300 }}
                    loading={searchDocumentObjectResponse.loading}
                    noOptionsText={t("Nessun oggetto documento trovato")}
                    options={searchDocumentObjectResponse.hasLoaded ? searchDocumentObjectResponse.data.map((value: any) => value.headerArchive) : []}
                    renderInput={(params: any) => <TextField placeholder={t("Cerca oggetto documento...")} {...params} />}
                    getOptionLabel={(option: any) => option}
                    renderOption={(props: any, option: any) => (
                        <ListItem {...props} value={option}>
                            {option}
                        </ListItem>
                    )}
                ></CustomAutocomplete>
            </FormControl>
            <Stack direction="row" alignItems="flex-end" gap={1}>
                <TextField sx={{ width: 300 }} id="Note" label={t("Note")} multiline rows={4} onChange={(e: any) => handleUpdateDocumentDetails("note")(e.target.value)} value={documentDetails.note} />
                <Button onClick={() => handleUpdateDocumentDetails("note")(`${documentDetails.note ?? ""} ${userName.current} ${format(new Date(), "dd.MM.yyyy HH:mm:ss")} \n`)}>{t("Sigla")}</Button>
            </Stack>
            <FormControlLabel control={<Checkbox checked={showVisible} onChange={() => setShowVisible(!showVisible)} />} label={t("Visibile ad esterni")} labelPlacement="start" />
            {showVisible && (
                <FormControl sx={{ width: 300 }}>
                    <InputLabel>{t("Utenti esterni")}</InputLabel>
                    <FormControlLabel control={<Checkbox checked={documentDetails.visibile === 1} onChange={(e: any) => handleUpdateDocumentDetails("visibile")(e.target.checked ? 1 : 0)} />} label={t("Tutti")} labelPlacement="end" />
                </FormControl>
            )}
        </Stack>
    );
};
