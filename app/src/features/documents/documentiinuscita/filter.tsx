import { Box, Button, TextField } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { IFilterProps } from "./interfaces/simpleList.interface";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import moment from "moment";
import { useUser } from "../../../store/UserStore";
import useGetCustom from "../../../hooks/useGetCustom";
import ToastNotification from "../../../custom-components/ToastNotification";
import { useState } from "react";

export default function Filters(props: IFilterProps) {
    const { defaultQuery, query, setQuery, filterData } = props;
    const { t } = useTranslation();

    const onChangeFilterInputs = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery((listItem: any) => ({
            ...listItem,
            [e.target.name]: e.target.value,
            page: 0,
        }));
    };

    const onDateChange = (name: string, value: Date) => {
        let formattedDate = moment(value).format("DD/MM/YYYY");

        setQuery({
            ...query,
            [name]: value,
            ["date"]: formattedDate,
        });
    };

    const { modules }: any = useUser();

    const impegniMultiutente = modules.provisioningRow?.documentale_id == 1;

    const uploadAll = useGetCustom("documentiinuscita/upload-all");
    const [showErrorMessage, setShowErrorMessage] = useState(false);
    const handleUploadApi = async () => {
        // default set to true as api fails - need to check with live api json
        setShowErrorMessage(true);
        await uploadAll.doFetch(true);
    };

    return (
        <Box component="form" display="flex" alignItems="end" gap={2}>
            <ToastNotification
                showNotification={showErrorMessage}
                setShowNotification={setShowErrorMessage}
                severity="error"
                text={t("API failed")}
            />
            <TextField
                label={t("Protocollo")}
                variant="outlined"
                name={"searchProtocollo"}
                sx={{ width: 1 / 3 }}
                onChange={onChangeFilterInputs}
                value={query["searchProtocollo"]}
            />
            <div style={{ width: "25%" }}>
                <DatePicker
                    label="Data di protocollazione"
                    value={query.date_without_format}
                    onChange={(date: Date | null) => {
                        if (date) onDateChange("date_without_format", date);
                    }}
                />
            </div>

            <Button
                onClick={() => filterData(query)}
                variant="contained"
                color="primary"
                type="button"
            >
                {t("Ricerca")}
            </Button>

            <Button
                variant="contained"
                color="primary"
                onClick={() => setQuery(defaultQuery)}
            >
                {t("Mostra tutti")}
            </Button>
            {impegniMultiutente ? (
                <Button
                    variant="contained"
                    color="primary"
                    onClick={handleUploadApi}
                >
                    {t("Carica tutti")}
                </Button>
            ) : (
                ""
            )}
        </Box>
    );
}
