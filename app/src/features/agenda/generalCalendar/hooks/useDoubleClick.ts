import { useCallback, useRef, useEffect } from 'react';

export const useDoubleClick = (onSingleClick: any, onDoubleClick: any, delay = 300) => {
    const clickCountRef: any = useRef(0);
    const clickTimerRef = useRef<NodeJS.Timeout | null>(null);

    const handleClick = useCallback((arg: any) => {
        clickCountRef.current += 1;

        if (clickCountRef.current === 1) {
            // First click - start timer for single click
            clickTimerRef.current = setTimeout(() => {
                onSingleClick(arg);
                clickCountRef.current = 0;
            }, delay);
        } else if (clickCountRef.current === 2) {
            // Second click - clear timer and execute double click
            if (clickTimerRef.current) {
                clearTimeout(clickTimerRef.current);
            }
            clickCountRef.current = 0;
            onDoubleClick(arg);
        }
    }, [onSingleClick, onDoubleClick, delay]);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (clickTimerRef.current) {
                clearTimeout(clickTimerRef.current);
            }
        };
    }, []);

    return handleClick;
};