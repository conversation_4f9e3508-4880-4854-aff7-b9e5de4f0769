import React from "react";
import {
    Box,
    Button,
    FormGroup,
    FormControlLabel,
    Checkbox,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Paper,
    TableHead,
    Stack,
} from "@vapor/react-material";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import { useTranslation } from "@1f/react-sdk";
import { useForm, useFieldArray, Controller } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import FormInput from "../../../../custom-components/FormInput";
import { useParams } from "react-router-dom";
import usePostCustom from "../../../../hooks/usePostCustom";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { useAgendaHooks } from "../hooks/useAgendaHooks";
import { useUser } from "../../../../store/UserStore";
import moment from "moment";
import { SelectMultiple } from "../../../../custom-components/SelectMultiple";

const getSchemaValidation = () => {
    return yup.object().shape({
        uniqueid: yup.string(),
        macroId: yup.string(),
        date: yup.date(),
        workingSuspension: yup.boolean(),
        holiday: yup.boolean(),
        weekend: yup.boolean(),
        details: yup.array(),
        owner: yup.array(),
        graph: yup.string(),
        macrocategoryList: yup.string(),
    });
};

const ControlledCheckbox = ({ name, label, control, ...props }: any) => {
    return (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <FormControlLabel
                    control={
                        <Checkbox
                            {...field}
                            checked={!!field.value}
                            {...props}
                        />
                    }
                    label={label}
                />
            )}
        />
    );
};

export const Macro = ({ rowData, changeView }: any) => {
    const { t } = useTranslation();
    const { macro, macroCategories, users, request, currentUser, people } =
        useAgendaHooks();

    const [macroData, setMacroData] = React.useState({
        deadlineTypes: [],
        details: [],
        graph: "",
        macro: {},
    });

    const params = useParams<{ uniqueId: string }>();

    const { control, handleSubmit, setValue, watch } = useForm<any>({
        resolver: yupResolver(getSchemaValidation()),
        defaultValues: {
            owner: [],
            uniqueid: "",
            macroId: "",
            date: rowData.date,
            workingSuspension: false,
            holiday: false,
            weekend: false,
            details: [],
            graph: "",
            macrocategoryList: "-1",
        },
    });

    const detailsValue = useFieldArray({
        control,
        name: "details",
    });

    const loadMacroRequest = usePostCustom(
        `macro/loadmacro?noTemplateVars=true`
    );
    const saveMacroReq = usePostCustom(`macro/savedetails?noTemplateVars=true`);

    const { user }: any = useUser();

    const macroItem = watch("macroId");
    const weekend = watch("weekend");
    const holiday = watch("holiday");
    const date = watch("date");
    const workingSuspension = watch("workingSuspension");

    const values = watch();

    React.useEffect(() => {
        loadMacroItems();
    }, [macroItem, weekend, holiday, date, workingSuspension]);

    React.useEffect(() => {
        if (users?.length > 0) {
            setValue("owner", [
                {
                    label: users[0].nomeutente,
                    value: users[0].id,
                },
            ]);
        }
    }, [users?.length]);

    function convertToISO(dateString: string): string {
        const date = new Date(dateString);
        const timezoneOffset = date.getTimezoneOffset();
        date.setMinutes(date.getMinutes() - timezoneOffset);

        return date.toISOString();
    }

    const loadMacroItems = async () => {
        const formData = new FormData();

        const getSelectedMacro = macro.find(
            (item: any) => item.id === values.macroId
        );

        getSelectedMacro && formData.append("type", getSelectedMacro.tipo);
        formData.append("date", convertToISO(values.date));
        formData.append("holiday", values.holiday);
        formData.append("macroId", values.macroId);
        formData.append("weekend", values.weekend);
        formData.append("workingSuspension", values.workingSuspension);
        const response: any = await loadMacroRequest.doFetch(true, formData);
        const details = response.data.details;
        setMacroData(response.data);
        setValue(
            "details",
            details.map((item: any) => {
                return {
                    ...item,
                    macroChecked: true,
                    deadlineType: response.data.deadlineTypes[0]?.id,
                };
            })
        );

        setValue("graph", response.data.graph);
    };

    const formatDateToItalian = (dateString: string) => {
        const date = new Date(dateString);
        const formatter = new Intl.DateTimeFormat("it-IT", {
            weekday: "short",
            day: "numeric",
            month: "long",
            year: "numeric",
        });
        const formattedDate = formatter.format(date);

        return `${formattedDate} ore 9:00`;
    };

    const isWeekend = (dateString: string): boolean => {
        const date = new Date(dateString);
        const dayOfWeek = date.getDay();
        return dayOfWeek === 0 || dayOfWeek === 6;
    };

    const convertToFormData = (data: any) => {
        const formData = new FormData();

        formData.append("hearingUniqueid", params.uniqueId ?? "");

        // Add basic fields
        formData.append("macrocategory", "-1");
        formData.append("macro", data.macroId);
        formData.append(
            "effectiveDate",
            moment(data.date).format("DD/MM/YYYY")
        ); // Format to DD/MM/YYYY
        formData.append(
            "workingSuspension",
            data.workingSuspension ? "1" : "0"
        );

        formData.append("graph", values.graph);
        formData.append("macrocategory", "-1");
        formData.append("weekend", values.weekend);
        formData.append("holiday", values.holiday);
        formData.append("dynamic", "0");

        // Add details
        data.details.forEach((detail: any, index: number) => {
            formData.append(`details[${index}][macro]`, "");
            formData.append(`details[${index}][templates]`, "");
            formData.append(`details[${index}][uniqueid]`, detail.uniqueid);
            formData.append(`details[${index}][visible]`, detail.visible);
            formData.append(
                `details[${index}][description]`,
                detail.description
            );
            formData.append(`details[${index}][reference]`, detail.reference);
            formData.append(`details[${index}][order]`, detail.detailOrder);
            formData.append(`details[${index}][days]`, detail.days);
            formData.append(
                `details[${index}][important]`,
                detail.importante || "0"
            );
            formData.append(
                `details[${index}][fatturabile]`,
                detail.fatturabile || "0"
            );
            formData.append(`details[${index}][status_id]`, detail.status_id);
            formData.append(
                `details[${index}][category_id]`,
                detail.category_id
            );
            formData.append(
                `details[${index}][deadlineType]`,
                detail.deadlineType
            );
            formData.append(
                `details[${index}][date]`,
                detail.date.split(" ")[0]
            ); // YYYY-MM-DD
            formData.append(`details[${index}][warning]`, detail.warningDays);
        });

        data.owner.forEach((owner: any) => {
            formData.append(`owner[]`, owner.value);
        });

        return formData;
    };

    const submit = async (values: any) => {
        const formData = convertToFormData(values);
        await saveMacroReq.doFetch(true, formData);

        changeView("scheda");
    };

    const onChangeMacro = async () => {
        console.log("test");
    };

    const adjustDates = (index: number, hours: number) => {
        const updatedData = values.details.map((item: any, i: number) => {
            if (i >= index) {
                const newDate = moment(item.date).add(hours, "day");
                return { ...item, date: newDate.format("YYYY-MM-DD HH:mm:ss") };
            }
            return item;
        });
        setValue("details", updatedData);
    };

    const macroDataMapped =
        macroData.deadlineTypes.map((deadline: any) => ({
            label: deadline.nome,
            value: deadline.id,
        })) || [];

    let macroCategoriesData = [{ value: "-1", label: t("Tutte") }];

    if (macroCategories) {
        macroCategoriesData = [
            ...macroCategoriesData,
            ...macroCategories?.map((macroCategory: any) => ({
                label: macroCategory.nome,
                value: macroCategory.id,
            })),
        ];
    }

    return (
        <>
            <Box component="form" onSubmit={handleSubmit(submit)}>
                {request?.controller != "macro" && (
                    <Box
                        sx={{
                            "& > :not(style)": {
                                m: 1,
                                width: 350,
                            },
                        }}
                    >
                        {user?.configs?.app?.impegni_multiutente_bool ? (
                            <FormInput
                                control={control}
                                name="owner"
                                label={t("Intestatari di default")}
                                type="multiselect"
                                variant="outlined"
                                style={{ width: 350 }}
                                options={users?.map((user: any) => ({
                                    label: user.nomeutente,
                                    value: user.id,
                                    selected: user.id === currentUser,
                                }))}
                            />
                        ) : (
                            <FormInput
                                control={control}
                                name="owner"
                                label={t("Intestatari di default")}
                                type="select"
                                variant="outlined"
                                style={{ width: 350 }}
                                options={people?.map((people: any) => ({
                                    label: people.nomeutente,
                                    value: people.id,
                                    selected: people.id === currentUser,
                                }))}
                            />
                        )}
                    </Box>
                )}

                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 350,
                        },
                    }}
                >
                    <FormInput
                        control={control}
                        name="macrocategoryList"
                        label={t("Categoria")}
                        type="select"
                        variant="outlined"
                        options={macroCategoriesData}
                        style={{ width: 350 }}
                    />
                </Box>
                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 350,
                        },
                    }}
                >
                    <FormInput
                        control={control}
                        name="macroId"
                        label={t("Macro")}
                        type="select"
                        variant="outlined"
                        options={macro?.map((macro: any) => ({
                            label: macro.nome,
                            value: macro.id,
                        }))}
                        onChange={onChangeMacro}
                        setValue={setValue}
                        style={{ width: 350 }}
                    />
                </Box>
                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 350,
                        },
                    }}
                >
                    <Controller
                        name="date"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                            <DatePicker
                                label={t("Data decorrenza")}
                                value={value}
                                onChange={onChange}
                            />
                        )}
                    />
                </Box>
                {values.macroId && (
                    <>
                        {" "}
                        <Box
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: 350,
                                },
                            }}
                        >
                            {" "}
                            <FormGroup>
                                <ControlledCheckbox
                                    control={control}
                                    name="workingSuspension"
                                    label={t(
                                        "Applicare sospensione feriale. Legge 742/1969"
                                    )}
                                />
                            </FormGroup>
                        </Box>
                        <Box
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: 350,
                                },
                            }}
                        >
                            <FormGroup>
                                <ControlledCheckbox
                                    control={control}
                                    name="weekend"
                                    label={t("Salta weekend.")}
                                />
                            </FormGroup>
                        </Box>
                        <Box
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: 350,
                                },
                            }}
                        >
                            <FormGroup>
                                <ControlledCheckbox
                                    control={control}
                                    name="holiday"
                                    label={t("Includi i festivi.")}
                                />
                            </FormGroup>

                            <Typography
                                variant="bodySmall"
                                gutterBottom
                                component="div"
                            >
                                {t(
                                    "Se selezionate le opzioni e la scadenza calcolata cade di sabato, di domenica o in un giorno festivo viene indicato come termine ultimo il primo giorno non festivo"
                                )}
                                <b>{t(" successivo")}</b> {t(", oppure")}{" "}
                                <b>{t("precedente")}</b>{" "}
                                {t("se il termine è a ritroso.")}
                            </Typography>
                        </Box>
                        <Box
                            sx={{
                                "& > :not(style)": {
                                    m: 1,
                                    width: "100%",
                                },
                            }}
                        >
                            <TableContainer
                                component={Paper}
                                sx={{ mt: 2, width: 1 / 3 }}
                            >
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell>
                                                {t("Oggetto")}
                                            </TableCell>
                                            <TableCell>
                                                {t("Tipologia")}
                                            </TableCell>
                                            <TableCell>
                                                {t("Data ora")}
                                            </TableCell>
                                            <TableCell>{t("Avvisa")}</TableCell>
                                            <TableCell>
                                                {t("Intestatari")}
                                            </TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {detailsValue.fields.map(
                                            (item: any, index: number) => {
                                                return (
                                                    <TableRow key={item.id}>
                                                        <TableCell>
                                                            {
                                                                values?.details[
                                                                    index
                                                                ].description
                                                            }
                                                        </TableCell>
                                                        <TableCell>
                                                            <FormInput
                                                                control={
                                                                    control
                                                                }
                                                                name={`details.${index}.deadlineType`}
                                                                type="select"
                                                                variant="outlined"
                                                                options={
                                                                    macroDataMapped
                                                                }
                                                                style={{
                                                                    width: 350,
                                                                }}
                                                            />
                                                        </TableCell>
                                                        <TableCell>
                                                            <Typography
                                                                variant="bodySmall"
                                                                gutterBottom
                                                                color={
                                                                    isWeekend(
                                                                        values
                                                                            .details[
                                                                            index
                                                                        ].date
                                                                    )
                                                                        ? "primary.error"
                                                                        : "primary.main"
                                                                }
                                                                component="div"
                                                            >
                                                                {formatDateToItalian(
                                                                    values
                                                                        .details[
                                                                        index
                                                                    ].date
                                                                )}
                                                            </Typography>
                                                            <Stack
                                                                direction="row"
                                                                spacing={2}
                                                                sx={{
                                                                    marginTop:
                                                                        "10px",
                                                                }}
                                                            >
                                                                <Button
                                                                    size="small"
                                                                    variant="contained"
                                                                    sx={{
                                                                        width: 10,
                                                                        height: 10,
                                                                    }}
                                                                    onClick={() =>
                                                                        adjustDates(
                                                                            index,
                                                                            -1
                                                                        )
                                                                    }
                                                                >
                                                                    <RemoveIcon />
                                                                </Button>
                                                                <Button
                                                                    size="small"
                                                                    variant="contained"
                                                                    sx={{
                                                                        width: 10,
                                                                        height: 10,
                                                                    }}
                                                                    onClick={() =>
                                                                        adjustDates(
                                                                            index,
                                                                            1
                                                                        )
                                                                    }
                                                                >
                                                                    <AddIcon />
                                                                </Button>
                                                            </Stack>
                                                        </TableCell>
                                                        <TableCell>
                                                            <FormInput
                                                                control={
                                                                    control
                                                                }
                                                                name={`details.${index}.warningDays`}
                                                                type="number"
                                                                variant="outlined"
                                                                sx={{
                                                                    maxWidth:
                                                                        "50px",
                                                                }}
                                                            />
                                                            {t(" giorni prima")}
                                                        </TableCell>
                                                        <TableCell>
                                                            <FormGroup>
                                                                <FormControlLabel
                                                                    control={
                                                                        <Checkbox
                                                                            name={`details.${index}.macroChecked`}
                                                                            //checked={values.details[index].macroChecked}
                                                                            disabled={
                                                                                values
                                                                                    .details[
                                                                                    index
                                                                                ]
                                                                                    .macroChecked
                                                                            }
                                                                            onChange={(
                                                                                _event: any,
                                                                                checked: boolean
                                                                            ) => {
                                                                                setValue(
                                                                                    `details.${index}.macroChecked`,
                                                                                    checked
                                                                                );
                                                                                if (
                                                                                    checked
                                                                                ) {
                                                                                    setValue(
                                                                                        `details.${index}.macroList`,
                                                                                        []
                                                                                    );
                                                                                }
                                                                            }}
                                                                        />
                                                                    }
                                                                    label={t(
                                                                        "usa intestatari di default"
                                                                    )}
                                                                />
                                                            </FormGroup>

                                                            <SelectMultiple
                                                                name={`details.${index}.macroList`}
                                                                options={users?.map(
                                                                    (
                                                                        user: any
                                                                    ) => ({
                                                                        label: user.nomeutente,
                                                                        value: user.id,
                                                                    })
                                                                )}
                                                                selectedValues={
                                                                    values
                                                                        ?.details[
                                                                        index
                                                                    ].macroList
                                                                }
                                                                onChange={(
                                                                    _name: string,
                                                                    value: any[]
                                                                ) => {
                                                                    setValue(
                                                                        `details.${index}.macroList`,
                                                                        value
                                                                    );

                                                                    setValue(
                                                                        `details.${index}.macroChecked`,
                                                                        value.length >
                                                                            0
                                                                            ? false
                                                                            : true
                                                                    );
                                                                }}
                                                            />
                                                        </TableCell>
                                                    </TableRow>
                                                );
                                            }
                                        )}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Box>
                    </>
                )}

                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 1,
                        },
                    }}
                >
                    <Typography
                        variant="bodySmall500"
                        gutterBottom
                        component="div"
                    >
                        <b> {t("N.B. ")}</b>
                        {t("L'utente è tenuto a")}{" "}
                        <b>
                            <u>{t("VERIFICARE")}</u>
                        </b>{" "}
                        {t("L'esattezza delle informazioni riportate")}
                    </Typography>
                </Box>
                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 150,
                        },
                    }}
                >
                    <Button
                        type="button"
                        variant="outlined"
                        onClick={() => changeView("scheda")}
                        sx={{ mr: 1 }}
                    >
                        {t("Indietro")}
                    </Button>

                    <Button type="submit" variant="contained" sx={{ mr: 1 }}>
                        {t("Conferma")}
                    </Button>
                </Box>
            </Box>
        </>
    );
};
