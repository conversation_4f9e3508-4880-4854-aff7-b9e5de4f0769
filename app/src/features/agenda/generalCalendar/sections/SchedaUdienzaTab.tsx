import { Box, FormGroup, FormControlLabel, Checkbox, Stack, Button, TextField, Menu, MenuItem, Typography } from "@vapor/react-material";
import { Controller } from "react-hook-form";
import { useTranslation } from "@1f/react-sdk";
import FormInput from "../../../../custom-components/FormInput";
import { useNavigate } from "react-router-dom";
import React, { useState } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import useGetCustom from "../../../../hooks/useGetCustom";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { TimePickerUi } from "../../../../custom-components/react-hook-components/Timepicker";
import KeyboardDoubleArrowLeftIcon from "@mui/icons-material/KeyboardDoubleArrowLeft";
import KeyboardDoubleArrowRightIcon from "@mui/icons-material/KeyboardDoubleArrowRight";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import AddIcon from "@mui/icons-material/Add";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { useUser } from "../../../../store/UserStore";
import moment from "moment";
import { debounce } from "lodash";
import { mapKeys, mapValues } from "../utils";
import { useAgendaProvider } from "../providers/AgendaUpdateProvider";
import Spinner from "../../../../custom-components/Spinner";
import SpinnerButton from "../../../../custom-components/SpinnerButton";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

const StyledBox = (props: any) => {
    return (
        <Box
            sx={{
                "& > :not(style)": {
                    m: 1,
                    width: 350
                }
            }}
        >
            {props.children}
        </Box>
    );
};

const ControlledCheckbox = ({ name, label, control, ...props }: any) => {
    return <Controller name={name} control={control} render={({ field }) => <FormControlLabel control={<Checkbox {...field} checked={!!field.value} {...props} />} label={label} />} />;
};

export const SchedaUdienzaTab = ({ rowData, changeView, method, onSubmit, saveLoading, setValidation, prevPath = "/agenda/agenda", items, defaultParams }: { rowData: any; method: any; onSubmit: any; saveLoading: boolean; changeView?: any; setValidation: any; prevPath: string; rowDataUrl: string;  items: any; defaultParams: any; fileHeaderData: any }) => {
    const { t } = useTranslation();

    const { agenda, rowValues, isInitialized, fetchAgenda, setRowValues } = useAgendaProvider();

    const [attivitaSearch, setAttivitaSearch] = React.useState("");
    const [anchorEl, setAnchorEl] = useState(null);
    const [attivitaNewField, setAttivitaNewField] = useState(false);
    const [guidiceNewField, setGuidiceNewField] = useState(false);
    const [evasaActive, setEvasaActive] = useState(false);
    const [attivita, setAttivita] = React.useState([]);
    const navigate = useNavigate();
    const [showModal, setShowModal] = useState<boolean>(false);
    const [showModalDelete, setShowModalDelete] = useState<boolean>(false);

    const { control, handleSubmit, setValue, watch, reset } = method;

    React.useEffect(() => {
        if (rowValues && Object.keys(rowValues).length > 0 && rowValues.hasOwnProperty("ora") && rowValues.hasOwnProperty("date")) {
            reset(rowValues);
            return;
        }
        if (rowData?.form) {
            Object.keys(rowData.form).forEach((key: string) => {
                setValue(mapKeys(key), mapValues(key, rowData.form[key], rowData.form["agendaMinuti"]));
            });
        }

        setValue("agendaEvasa", rowData?.stato_evasa === "1");
        setValue("agendaNonEvadere", rowData?.stato_evasa === "2");
        setValue("agendaBillable", rowData?.form?.agendaBillable === "1");
        setValue("referente", rowData?.form?.referente?.length > 0 ? rowData?.form?.referente : [{ id: rowData.form.avvocato }]);
        setEvasaActive(values.agendaEvasa);
    }, [rowData]);

    const { user, modules }: any = useUser();

    const handleClickMenu = (event: any) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const values: any = watch();

    React.useEffect(() => {
        const subscription = watch((data: any) => setRowValues(data));
        return () => subscription.unsubscribe();
    }, [values]);

    React.useEffect(() => {
        const handleBeforeUnload = () => {
            localStorage.removeItem("cleared");
        };
        window.addEventListener("beforeunload", handleBeforeUnload);

        return () => window.removeEventListener("beforeunload", handleBeforeUnload);
    }, []);

    React.useEffect(() => {
        if (values.agendaEvasa) {
            setEvasaActive(true);
        } else {
            setEvasaActive(false);
        }
    }, [values]);

    const getSearchArchiveDataReq = useGetCustom(attivitaSearch && `agenda/search-attivita?q=${attivitaSearch}&increment=0&noTemplateVars=true`);

    const remoteGuidiceRequest = usePostCustom(`instructors/remotesave?noTemplateVars=true`);
    const remoteAttivitaRequest = usePostCustom(`attivitaudienze/remotesavetypeahead?noTemplateVars=true`);

    const usePostTimesheeRequest = usePostCustom(`timesheet/create-from-agenda?noTemplateVars=true`);
    const annotationsValues = watch("agendaAnnotazioni");

    const addSiggla = () => {
        const currentDate = moment();
        const formattedDateTime = currentDate.format("DD.MM.YYYY HH:mm:ss");

        if (!annotationsValues) {
            setValue("agendaAnnotazioni", loggedUser.nomepersonale + " " + formattedDateTime);
        } else {
            const val = annotationsValues + "\n";
            setValue("agendaAnnotazioni", val + loggedUser.nomepersonale + " " + formattedDateTime);
        }
    };

    const adjustAgendaDaysBefore = (action: string) => {
        const currentValue = Number(values.agendaDaysBefore) || 0;
        if (action === "+1") {
            setValue("agendaDaysBefore", currentValue + 1);
        } else if (action === "-1") {
            if (currentValue > 0) {
                setValue("agendaDaysBefore", currentValue - 1);
            }
        }
    };

    const getAttivitaData = React.useCallback(async () => {
        const response: any = await getSearchArchiveDataReq.doFetch(true);

        setAttivita(
            response.data.map((item: any) => ({
                value: item.id,
                label: `${item.attivita}`
            }))
        );
    }, [attivitaSearch]);

    const debouncedGetAttivitaData = React.useCallback(debounce(getAttivitaData, 500), [getAttivitaData]);

    React.useEffect(() => {
        if (attivitaSearch) debouncedGetAttivitaData();

        return () => {
            debouncedGetAttivitaData.cancel();
        };
    }, [attivitaSearch, debouncedGetAttivitaData]);

    const addAttivittaField = () => {
        setAttivitaNewField(!attivitaNewField);
    };

    const saveNewAttivita = async () => {
        const insertAgendaAttivita = watch("insertAgendaAttivita");
        const sendParams = {
            insertItem: insertAgendaAttivita,
            additionalValues: "tipo : u",
            searchMainElement: "attivita"
        };
        await remoteAttivitaRequest.doFetch(true, sendParams);
        setAttivitaNewField(!attivitaNewField);
    };

    const addGuidiceField = () => {
        setValue("insertAgendaInstructor", "");
        setTimeout(() => {
            setGuidiceNewField(!guidiceNewField);
        }, 200);
    };

    const saveNewGuidice = async () => {
        const sendParams = {
            insertItem: values.insertAgendaInstructor
        };
        await remoteGuidiceRequest.doFetch(true, sendParams);
        setValue("insertAgendaInstructor", "");
        setGuidiceNewField(!guidiceNewField);
        fetchAgenda();
    };

    const createTimesheet = async () => {
        if (values.agendaPeriod > 0) {
            handleSubmit(onSubmit)("timesheet");
            const sendParams = {
                agendaUniqueid: values.agendaUniqueid
            };
            const response: any = await usePostTimesheeRequest.doFetch(true, sendParams);

            if (response.data.responseCode === 1) {
                navigate(`/legacy/archivetimesheet/timesheet?fileUniqueid=${values.agendaArchiveuid}&taskId=${response.data.timesheetId}`);
            }
        } else {
            setValidation("timesheet");
            setTimeout(() => {
                handleSubmit(onSubmit)("timesheet");
            }, 500);
        }
    };

    const deleteButton = async () => {
        setShowModalDelete(true);
    };

    const rinviaButton = async () => {
        if (!values.agendaEvasa) {
            setShowModal(true);
        } else {
            onSubmit(values, "rinvia", true);
            setRowValues(null);
        }
    };

    const deleteRequest = usePostCustom("agenda/delete?noTemplateVars=true");

    const handleModalDelete = async (confirm: boolean) => {
        if (confirm) {
            const sendParams = {
                uniqueid: values.agendaUniqueid,
                updateActivePage: true
            };
            await deleteRequest.doFetch(true, sendParams);

            setShowModalDelete(false);
            navigate(prevPath);
        }
        setShowModalDelete(true);
    };

    const handleModalConfirm = (confirm: boolean) => {
        if (confirm) {
            onSubmit(values, "rinvia", true);
            setRowValues(null);
        } else {
            onSubmit(values, "rinvia", true, false, true);
            setRowValues(null);
        }
        setShowModal(false);
    };

    const handleMacro = async () => {
        changeView && changeView("macro");
        setRowValues(null);
    };

    const handleGoToPractica = async () => {
        navigate(`/legacy/archive/summary?uid=${values.agendaArchiveuid}`);
    };

    const exportPdfFile = useGetCustom(
        "archiveagenda/printbyid?noTemplateVars=true",
        {
            uniqueid: values.agendaUniqueid,
            archiveuid: values.agendaArchiveuid
        },
        null,
        true
    );

    const handleStampa = async () => {
        const response: any = await exportPdfFile.doFetch(true);
        const blob = new Blob([response.data], { type: "text/pdf" });
        const url = URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", "Udienza_pratica.pdf");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const referenteIds = values?.referente?.map(({ id }: any) => id) || [];

    const isOnUpdate = location.pathname.includes("/update");

    if (!isInitialized) {
        return <Spinner />;
    }

    const { fileStatus, loggedUser, searchInstructors } = agenda as any;
    const closeDeleteConfirmation = async () => {
        setShowModalDelete(false);
    };

    return (
        <>
            <ConfirmModal open={showModal} handleDecline={() => handleModalConfirm(false)} handleAgree={() => handleModalConfirm(true)} decline={t("Annulla")} agree={t("Conferma")} confirmText={t("Si desidera impostare l'udienza corrente come evasa?")} title={t("Vuoi continuare?")} />

            <ConfirmModal open={showModalDelete} handleDecline={() => closeDeleteConfirmation()} handleAgree={() => handleModalDelete(true)} decline={t("Annulla")} agree={t("Conferma")} confirmText={t("Eliminare definitivamente l'udienza e tutti i suoi impegni correlati?")} title={t("Vuoi continuare?")} />
            <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 550
                        }
                    }}
                >
                    <Stack
                        alignItems="end"
                        autoComplete="off"
                        component="form"
                        direction="row"
                        gap={1}
                    >
                        {" "}
                        {rowData?.prevHearing && (
                            <>
                              <Stack direction="row" spacing={1} alignItems="center" sx={{paddingRight: '5px'}}>
                                <Typography 
                                    variant="bodySmall" 
                                    component="span"
                                    color="primary.main" 
                                    sx={{ mr: 0.5  }}
                                >   
                                    {t("Ud. precedente")}:{" "}
                                </Typography>
                                <Typography
                                    variant="bodySmall" 
                                    component="a"
                                    color="primary.interactiveDefault"
                                    onClick={() => navigate(`/agenda/agenda/update/${rowData.prevHearing.uniqueid.replace('?', '')}`,{
                                        state: {
                                            origin: "agenda",
                                            type: "update",
                                            uniqueId: rowData.prevHearing.uniqueid,
                                            rowDataUrl: "agenda",
                                            items: items,
                                            prevPath: prevPath,
                                            defaultParams: defaultParams,
                                        }
                                    })}
                                    sx={{ 
                                        cursor: 'pointer',
                                        mr: 1 , textDecoration:"underline"
                                    }}
                                >
                                     {rowData?.prevHearing?.data}
                                </Typography>
                                </Stack>
                            </>
                        )}
                        {rowData?.nextHearing && (
                            <>
                                <Stack direction="row" spacing={1} alignItems="center">
                                    <Typography variant="bodySmall" component="div" color="primary.main" gutterBottom sx={{ mr: 1 }}>
                                        {t("Ud. successiva")}:{" "}
                                    </Typography>
                              
                                <Typography
                                    variant="bodySmall" 
                                    component="a"
                                    color="primary.interactiveDefault" onClick={() => navigate(`/agenda/agenda/update/${rowData.nextHearing.uniqueid.replace('?', '')}`,{
                                    state: {
                                        origin: "agenda",
                                        type: "update",
                                        uniqueId: rowData.nextHearing.uniqueid,
                                        rowDataUrl: "agenda",
                                        items: items,
                                        prevPath: prevPath,
                                        defaultParams: defaultParams,
                                    }
                                })}
                                 sx={{ 
                                        cursor: 'pointer',
                                        mr: 1 ,
                                        ml: 0.5,
                                        textDecoration:"underline"
                                    }}
                                >
                                     {rowData?.nextHearing?.data}
                                    </Typography>
                                </Stack>
                            </>
                        )}
                    </Stack>
                </Box>
                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 350
                        }
                    }}
                >
                    <Stack
                        alignItems="end"
                        autoComplete="off"
                        component="form"
                        direction="row"
                        gap={1}
                        sx={{
                            width: 350
                        }}
                    >
                        <Controller
                            name="date"
                            control={control}
                            render={({ field, fieldState: { error } }) => (
                                <DatePicker
                                    label={t("Data")}
                                    value={field.value || null}
                                    onChange={field.onChange}
                                    slotProps={{
                                        textField: {
                                            error: !!error,
                                            helperText: error?.message,
                                        },
                                    }}
                                />
                            )}
                        />
                        <TimePickerUi name="ora" label={t("Ora")} control={control} ampm={false} />
                    </Stack>
                </Box>
                <StyledBox>
                    <Box sx={{ marginTop: "28px !important" }}>
                        <Stack
                            alignItems="end"
                            autoComplete="off"
                            component="form"
                            direction="row"
                            sx={{
                                width: 350
                            }}
                        >
                            {" "}
                            <Typography variant="body500" component="div" color="primary.main" gutterBottom style={{ paddingRight: "10px" }}>
                                {t("Avvisa ")}
                            </Typography>
                            <Button variant="contained" size="small" onClick={() => adjustAgendaDaysBefore("-1")}>
                                <KeyboardDoubleArrowLeftIcon />
                            </Button>
                            <TextField value={values.agendaDaysBefore} size="small" type="number" />
                            <Button variant="contained" size="small" onClick={() => adjustAgendaDaysBefore("+1")}>
                                <KeyboardDoubleArrowRightIcon />
                            </Button>
                            <Typography
                                variant="body500"
                                component="div"
                                color="primary.main"
                                gutterBottom
                                style={{
                                    paddingLeft: "10px",
                                    whiteSpace: "nowrap"
                                }}
                            >
                                {t("Giorni Prima")}
                            </Typography>
                        </Stack>
                    </Box>
                </StyledBox>
                <StyledBox>
                    <FormInput control={control} name="agendaPeriod" label={t("Durata")} type="number" variant="outlined" fullWidth />
                </StyledBox>
                <StyledBox>
                    <FormInput control={control} name="agendaAvvocato" label={t("Avvocato")} type="text" variant="outlined" disabled={true} />
                </StyledBox>
                <StyledBox>
                    <FormInput
                        control={control}
                        name="referente"
                        label={t("Referente")}
                        type="multiselect"
                        variant="outlined"
                        placeholder={t("Seleziona referente")}
                        fullWidth={true}
                        options={rowData?.users?.map((user: any) => {
                            return {
                                label: user.nomeutente,
                                value: user.id,
                                selected: referenteIds.includes(user.id)
                            };
                        })}
                    />
                </StyledBox>
                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: "auto"
                        },
                        display: "flex",
                        alignItems: "center",
                        gap: 1
                    }}
                >
                    {attivitaNewField ? (
                        <>
                            <Stack direction="row" spacing={2} sx={{ width: 450 }}>
                                <FormInput control={control} name="insertAgendaAttivita" label={t("Attività")} type="text" variant="outlined" style={{ width: 350 }} />
                                <Stack direction="row" spacing={1} justifyContent="flex-start" alignItems="center" sx={{ paddingTop: "20px" }}>
                                    {" "}
                                    <Button size="small" variant="contained" onClick={() => saveNewAttivita()}>
                                        {t("Salva")}
                                    </Button>
                                    <Button type="button" size="small" variant="outlined" onClick={() => addAttivittaField()}>
                                        {t("Annulla")}
                                    </Button>
                                </Stack>
                            </Stack>
                        </>
                    ) : (
                        <>
                            <CustomAutocomplete
                                options={attivita || []}
                                isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                                value={attivitaSearch}
                                loadingText={t("Caricamento…")}
                                noOptionsText={t("Nessuna opzione")}
                                renderInput={(params: any) => <TextField style={{ width: 350 }} {...params} placeholder={t("Cerca attività per nome")} />}
                                onInputChange={(_event: any, value: any, reason: string) => {
                                    if (reason === "input") {
                                        setAttivitaSearch(value);
                                    } else {
                                        setAttivitaSearch("");
                                    }
                                }}
                                onChange={(_event: any, item: any) => {
                                    setValue("attivita", values.attivita ? values.attivita + item.label : item.label);
                                }}
                            />

                            <Button
                                size="small"
                                variant="contained"
                                sx={{
                                    minWidth: "auto",
                                    marginTop: "25px"
                                }}
                                onClick={() => addAttivittaField()}
                            >
                                <AddIcon />
                            </Button>
                        </>
                    )}
                </Box>
                {!attivitaNewField && (
                    <StyledBox>
                        <FormInput control={control} name="attivita" type="textarea" variant="outlined" />
                    </StyledBox>
                )}
                <StyledBox>
                    <FormInput control={control} name="agendaSezione" label={t("Sezione")} type="text" variant="outlined" fullWidth />
                </StyledBox>
                <StyledBox>
                    <FormInput control={control} name="agendaAutoritaDescrizione" label={t("Autorità")} type="text" variant="outlined" disabled={true} fullWidth />
                </StyledBox>
                <StyledBox>
                    <FormInput control={control} name="agendaCittaDescrizione" label={t("Città")} type="text" disabled={true} variant="outlined" setValue={setValue} />
                </StyledBox>

                {guidiceNewField ? (
                    <Stack direction="row" spacing={2} sx={{ width: 450, m: 1 }}>
                        <FormInput control={control} name="insertAgendaInstructor" label={t("Guidice")} type="text" variant="outlined" style={{ width: 350 }} />
                        <Stack direction="row" spacing={1} justifyContent="flex-start" alignItems="center" sx={{ paddingTop: "20px" }}>
                            <Button
                                size="small"
                                variant="contained"
                                sx={{
                                    minWidth: "auto",
                                    marginTop: "30px"
                                }}
                                onClick={() => saveNewGuidice()}
                            >
                                {t("Salva")}
                            </Button>
                            <Button type="button" size="small" variant="outlined" onClick={() => addGuidiceField()}>
                                {t("Annulla")}
                            </Button>
                        </Stack>
                    </Stack>
                ) : (
                    <Box display="flex" alignItems="flex-start" gap={1} sx={{ m: 1 }}>
                        <Box sx={{ width: 350 }}>
                            <FormInput
                                control={control}
                                name="agendaIstruttore"
                                label={t("Guidice")}
                                type="select"
                                options={[
                                    {
                                        label: t("Seleziona un giudice..."),
                                        value: ""
                                    },
                                    ...searchInstructors?.map((instructor: any) => ({
                                        label: instructor.nome,
                                        value: instructor.id
                                    }))
                                ]}
                                fullWidth
                            />
                        </Box>
                        <Button size="small" variant="contained" sx={{ minWidth: "auto", marginTop: "28px" }} onClick={() => addGuidiceField()}>
                            <AddIcon />
                        </Button>
                    </Box>
                )}

                <Box display="flex" alignItems="flex-start" gap={1} sx={{ m: 1 }}>
                    <FormInput control={control} name="agendaAnnotazioni" type="textarea" label={t("Annotazioni")} row="5" style={{ width: 350 }} />
                    <Button size="small" variant="contained" sx={{ minWidth: "auto", marginTop: "28px" }} onClick={() => addSiggla()}>
                        {t("Sigla")}
                    </Button>
                </Box>
                <StyledBox>
                    <FormGroup>
                        <ControlledCheckbox control={control} name="agendaEvasa" label={t("Evasa")} disabled={values.agendaNonEvadere || false} />
                    </FormGroup>

                    <FormGroup>
                        <ControlledCheckbox control={control} disabled={values.agendaEvasa} name="agendaNonEvadere" label={t("Da non evadere")} />
                    </FormGroup>
                </StyledBox>
                <StyledBox>
                    <FormGroup>
                        <ControlledCheckbox control={control} name="agendaBillable" label={t("Fatturabile")} />
                    </FormGroup>
                </StyledBox>
                <StyledBox>
                    <FormInput
                        control={control}
                        name="agendaStatopratica"
                        type="select"
                        label={t("Stato pratica")}
                        options={fileStatus?.map((status: any) => ({
                            label: status.nome,
                            value: status.id
                        }))}
                        style={{ width: 350 }}
                    />
                </StyledBox>
                <Box
                    sx={{
                        "& > :not(style)": {
                            m: 1,
                            width: 150
                        }
                    }}
                >
                    <Button type="button" variant="outlined" onClick={() => navigate(prevPath)} sx={{ mr: 1 }}>
                        {t("Annulla")}
                    </Button>
                    {isOnUpdate && (
                        <>
                            {modules && !modules.isExternal && modules.canAccessDeleteAgendaUdienza() && (
                                <Button color="error" variant="outlined" onClick={() => deleteButton()} sx={{ mr: 1 }}>
                                    {t("Elimina")}
                                </Button>
                            )}
                            {modules && !modules.isExternal && modules.canAccessUpdateAgendaUdienza() && rowData.isLastHearing && (
                                <Button type="button" variant="outlined" onClick={() => rinviaButton()} sx={{ mr: 1 }}>
                                    {t("Rinvia")}
                                </Button>
                            )}
                            <Button type="button" variant="outlined" aria-haspopup="true" id="basic-button" onClick={handleClickMenu} endIcon={<ArrowDropDownIcon />}>
                                {t("Strumenti")}
                            </Button>
                            <Menu
                                open={Boolean(anchorEl)}
                                anchorEl={anchorEl}
                                id="basic-menu"
                                onClose={handleClose}
                                anchorOrigin={{
                                    vertical: "bottom",
                                    horizontal: "left"
                                }}
                                transformOrigin={{
                                    vertical: "top",
                                    horizontal: "left"
                                }}
                                PaperProps={{
                                    style: {
                                        transform: "translateX(-150px)",
                                        width: "200px"
                                    }
                                }}
                                MenuListProps={{
                                    "aria-labelledby": "basic-button"
                                }}
                            >
                                {modules && !modules.isExternal && modules.canAccessUpdateAgendaUdienza(user) && <MenuItem onClick={handleMacro}>{t("Macro")}</MenuItem>}
                                <MenuItem onClick={handleStampa}>{t("Stampa")}</MenuItem>
                                {modules && !modules.isExternal && modules?.canAccessGoToPractichaAgendaUdienza(user) && <MenuItem onClick={handleGoToPractica}>{t("Vai alla pratica")}</MenuItem>}
                            </Menu>
                        </>
                    )}

                    {evasaActive && (
                        <Button type="button" variant="outlined" onClick={() => createTimesheet()} sx={{ mr: 1 }}>
                            {t("Crea Timesheet")}
                        </Button>
                    )}
                    <SpinnerButton type="submit" variant="contained" sx={{ mr: 1 }} label={t("Conferma")} isLoading={saveLoading} />
                </Box>
            </Box>
        </>
    );
};
