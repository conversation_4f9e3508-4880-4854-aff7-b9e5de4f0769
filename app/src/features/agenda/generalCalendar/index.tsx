import { useTranslation } from "@1f/react-sdk";
import { useState } from "react";
import VaporPage from "@vapor/react-custom/VaporPage";
import Calendar from "./calendar";
import useGetEvents from "./hooks/useGetEventsHooks";
import ToastNotification from "../../../custom-components/ToastNotification";

export default function AgendaIndex() {
    const { t } = useTranslation();
    const {
        calendarRef,
        query,
        setQuery,
        eventData,
        eventResponse,
        fetchEventData,
        DEFAULT_QUERY,
        calendarData,
        monthTitle,
        items,
        setMonthTitle,
    } = useGetEvents();
    const [showSuccessMessage, setShowSuccessMessage] = useState(false);

    return (
        <VaporPage sx={{ height: "90vh" }}>
            <ToastNotification
                showNotification={showSuccessMessage}
                setShowNotification={setShowSuccessMessage}
                severity="success"
                text={t("Sincronizzazione calendario completata")}
            />
            <VaporPage.Section>
                <Calendar
                    DEFAULT_QUERY={DEFAULT_QUERY}
                    calendarRef={calendarRef}
                    query={query}
                    setQuery={setQuery}
                    eventData={eventData}
                    eventResponse={eventResponse}
                    fetchEventData={fetchEventData}
                    monthTitle={monthTitle}
                    setMonthTitle={setMonthTitle}
                    items={items}
                    t={t}
                    calendarData={calendarData}
                />
            </VaporPage.Section>
        </VaporPage>
    );
}
