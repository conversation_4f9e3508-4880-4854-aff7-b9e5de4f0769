import {
    Button,
    FormControl,
    FormControlLabel,
    Grid,
    IconButton,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
    Divider,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    FormGroup,
    Toggle,
    Popover,
    Checkbox,
    FormHelperText,
    Autocomplete,
    VaporTag,
} from "@vapor/v3-components";
import { Close } from "@mui/icons-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBell } from "@fortawesome/pro-regular-svg-icons";
import DatePicker from "../../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import {
    TimePicker,
    AdapterDateFns,
    LocalizationProvider,
} from "@vapor/v3-components";
import { useEffect, useState, useCallback } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import useGetCustom from "../../../../hooks/useGetCustom";
import { parseDate } from "../../../../helpers/parseDataFormat";
import ConfirmModal from "../../../../custom-components/ConfirmModal";
import { useNavigate } from "react-router-dom";

interface IProps {
    open: boolean;
    selectedDate: any;
    handleModal: (event: any, reason: any, formData?: any) => void;
    view: string;
    savedFormData?: any;
    onFormDataChange?: (formData: any) => void;
    handleProperClose: () => void;
}

function parseTimeStringToDate(timeString: string) {
    if (!timeString) return null;
    const [hh, mm] = timeString.split(":").map((n) => parseInt(n, 10));
    const d = new Date();
    d.setHours(hh);
    d.setMinutes(mm);
    d.setSeconds(0);
    d.setMilliseconds(0);
    return d;
}

export const formatDateDDMMYYYY = (date: Date): string => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
};

export const formatTimeHH = (date: Date): string =>
    String(date.getHours()).padStart(2, "0");

export const formatTimeMM = (date: Date): string =>
    String(date.getMinutes()).padStart(2, "0");

export const formatTimeHHMM = (
    date: Date
): { hours: string; minutes: string } => ({
    hours: formatTimeHH(date),
    minutes: formatTimeMM(date),
});

const defaultParams = {
    deadlineText: "",
    deadlineType: "-1",
    deadlineDate: "",
    deadlineHours: "",
    deadlineMinutes: "",
    deadlinePeriod: "",
    deadlineUser: [],
    deadLinesGroups: "-1",
};

export default function NouvoImpegnoModal(props: IProps) {
    const {
        open,
        selectedDate,
        handleModal,
        view,
        savedFormData,
        onFormDataChange,
        handleProperClose,
    } = props;
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [params, setParams] = useState<any>(savedFormData || defaultParams);
    const [anchorElGruppi, setAnchorElGruppi] = useState(null);
    const [anchorElIntestatari, setAnchorElIntestatari] = useState(null);
    const openGruppiPopover = Boolean(anchorElGruppi);
    const openIntestatariPopover = Boolean(anchorElIntestatari);
    const [tipologiaData, setTipologiaData] = useState([]);
    const [groupsOptions, setGroupsOptions] = useState<any>([]);
    const [userOptions, setUserOptions] = useState<any>([]);
    const [giornataSwitch, setGiornataSwitch] = useState(false);
    const [showModal, setShowModal] = useState<boolean>(false);
    const [tempUsersSelected, setTempUsersSelected] = useState<any>([]);
    const [loggedUserName, setLoggedUserDataName] = useState<string>("");
    const [requiredFields, setRequiredFields] = useState({
        deadlineText: false,
        deadlineDate: false,
        deadlineType: false,
    });
    const inputWidth = 250;

    const getCalendarDataRequest = useGetCustom("calendar/calendar");
    const getRowDataRequest = useGetCustom(
        "deadlines/getrowdata?noTemplateVars=true"
    );
    const getUsersGroupRequest = useGetCustom(
        "deadlines/getusersgroup?noTemplateVars=true"
    );

    const saveDeadlineRequest = usePostCustom(
        "deadlines/save?noTemplateVars=true"
    );

    const updateParams = (newParams: any) => {
        setParams(newParams);
        if (onFormDataChange) {
            onFormDataChange(newParams);
        }
    };

    useEffect(() => {
        const fetch = async () => {
            const [response, rowDataResponse]: any = await Promise.all([
                getCalendarDataRequest.doFetch(true),
                getRowDataRequest.doFetch(true, {
                    uniqueId: "",
                    fileUniqueid: "",
                }),
            ]);
            const { deadlineTypes, loggedUser } = response.data;
            const { groups, users } = rowDataResponse.data;
            setTipologiaData(deadlineTypes);
            setGroupsOptions(groups);
            setUserOptions(users);
            setLoggedUserDataName(loggedUser?.nomeutente);
        };

        fetch();
    }, []);

    const onDateChange = (name: string, value: Date) => {
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB");
        updateParams((prevValue: any) => ({
            ...prevValue,
            [name]: formattedDate,
        }));
    };

    const onChangeInputs = (e: any) => {
        const { name, value } = e.target;

        if (value && value.length > 0 && requiredFields.deadlineText) {
            setRequiredFields({
                ...requiredFields,
                deadlineText: false,
            });
        }

        updateParams((prev: any) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleTimeChange = (newValue: any | null) => {
        if (!newValue) {
            updateParams((prev: any) => ({
                ...prev,
                deadlineHours: "",
                deadlineMinutes: "",
            }));
            return;
        }
        const hh = String(newValue.getHours()).padStart(2, "0");
        const mm = String(newValue.getMinutes()).padStart(2, "0");

        updateParams((prev: any) => ({
            ...prev,
            deadlineHours: hh,
            deadlineMinutes: mm,
        }));
    };

    const updateDeadlineUsers = (users: any) => {
        const uniqueUsers = Array.from(
            new Map(users.map((user: any) => [user.id, user])).values()
        );

        // Directly update the `deadlineUser` with the unique list
        updateParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: uniqueUsers,
        }));
    };

    const fetchUserGroupById = async (groupId: string) => {
        const response: any = await getUsersGroupRequest.doFetch(true, {
            idGruppo: groupId,
        });
        return response.data;
    };

    const setCheckedUsersFromGroup = (groupData: any) => {
        const checkedUsers = groupData?.map((checkedUser: any) =>
            userOptions?.find((user: any) => user.id === checkedUser.userId)
        );
        updateParams((prevParams: any) => ({
            ...prevParams,
            deadlineUser: checkedUsers,
        }));
    };

    const handleUsersGroup = async (_: any, newValue: any) => {
        const { id } = newValue;

        if (id !== "-1") {
            try {
                const groups = await fetchUserGroupById(id);

                updateParams((prevParams: any) => ({
                    ...prevParams,
                    deadLinesGroups: newValue,
                    deadlineUser: Array.isArray(groups) ? groups : [], // Ensure groups is an array
                }));

                if (Array.isArray(groups)) {
                    setCheckedUsersFromGroup(groups);
                }
            } catch (error) {
                console.error("Error fetching user group:", error);
                // Handle error case
            }
        } else {
            updateParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: {
                    id: "-1",
                    name: t("Seleziona il gruppo di utenti..."),
                },
                deadlineUser: id === "-1" ? [] : prevParams.deadlineUser, // Keep existing users if not resetting
            }));
        }
    };

    const handleUserData = (_: any, newValue: any) => {
        if (
            params.deadLinesGroups &&
            (params.deadLinesGroups === "-1" ||
                params.deadLinesGroups.id === "-1")
        ) {
            // Ensure uniqueness by `id` only
            updateDeadlineUsers(newValue);
        } else {
            setTempUsersSelected(newValue);
            setShowModal(true);
        }
    };

    const handleModalConfirm = (confirm: boolean) => {
        if (confirm) {
            updateParams((prevParams: any) => ({
                ...prevParams,
                deadLinesGroups: {
                    id: "-1",
                    name: t("Seleziona il gruppo di utenti..."),
                },
            }));

            if (tempUsersSelected.length > 0) {
                updateDeadlineUsers(tempUsersSelected);
            }
        }
        setShowModal(false);
    };

    const updateDefaultDeadlineUser = useCallback(
        (data: any, loggedUserName: string) => {
            // For new impegno, use the current logged-in user
            const loggedUser = data?.find(
                (user: any) => user.nomeutente === loggedUserName
            );

            if (loggedUser) {
                updateParams((prevParams: any) => ({
                    ...prevParams,
                    deadlineUser: [
                        {
                            id: loggedUser.id,
                            nomeutente: loggedUser.nomeutente,
                        },
                    ],
                }));
            }
        },
        []
    );

    const handleGiornataSwitch = (event: any) => {
        const { checked } = event.target;
        setGiornataSwitch(checked);
        if (checked) {
            updateParams((prev: any) => ({
                ...prev,
                deadlinePeriod: "00:00",
                deadlineHours: "",
                deadlineMinutes: "",
            }));
        }
    };

    useEffect(() => {
        if (loggedUserName) {
            updateDefaultDeadlineUser(userOptions, loggedUserName);
        }
    }, [loggedUserName]);

    useEffect(() => {
        const { hours, minutes } = formatTimeHHMM(
            view === "dayGridMonth" ? new Date() : selectedDate
        );

        updateParams((prev: any) => ({
            ...prev,
            deadlineDate: formatDateDDMMYYYY(selectedDate),
            deadlineHours: hours,
            deadlineMinutes: minutes,
        }));
    }, [selectedDate]);

    const saveDeadline = async (deadlineSaveParams: any) => {
        if (deadlineSaveParams.deadlineText === "") {
            setRequiredFields({
                ...requiredFields,
                deadlineText: true,
            });
            return;
        }

        if (deadlineSaveParams.deadlineDate === "") {
            setRequiredFields({
                ...requiredFields,
                deadlineDate: true,
            });
            return;
        }

        if (deadlineSaveParams.deadlineType === "") {
            setRequiredFields({
                ...requiredFields,
                deadlineType: true,
            });
            return;
        }

        const formData = new FormData();
        const deadlineUser: any = [];

        Object.keys(deadlineSaveParams).forEach((key) => {
            const value = deadlineSaveParams[key as keyof any];
            if (key === "deadlineUser" && Array.isArray(value)) {
                value.forEach((user: any) => {
                    formData.append("deadlineUser[]", user.id);
                    deadlineUser.push(user.id);
                });
                const userIds = value.map((user: any) => user.id).join(",");
                formData.append("deadlineUserData", userIds);
                return;
            }

            if (key === "deadLinesGroups" && value?.id) {
                formData.append("deadLinesGroups", value.id);
                return;
            }

            formData.append(key, value);
        });
        await saveDeadlineRequest.doFetch(true, formData);
    };

    const handleClose = (event: any, reason: any) => {
        if (reason === "backdropClick") {
            console.log("is this called");
            handleModal(event, reason, params);
        } else {
            handleModal(event, reason);
        }
    };

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            aria-describedby="alert-dialog-description"
            aria-labelledby="alert-dialog-title"
            // sx={{ margin: 0, py: 0.5 }}
        >
            <DialogContent>
                <DialogTitle>
                    {t("Nuovo impegno")}
                    <IconButton
                        color="primary"
                        onClick={handleProperClose}
                        variant="text"
                    >
                        <Close />
                    </IconButton>
                </DialogTitle>
                <Divider variant="fullWidth" />{" "}
                <TextField
                    fullWidth
                    label={t("Titolo *")}
                    variant="outlined"
                    margin="normal"
                    name="deadlineText"
                    value={params.deadlineText}
                    onChange={onChangeInputs}
                    error={!!requiredFields.deadlineText}
                    helperText={
                        requiredFields.deadlineText
                            ? t("Titolo obbligatorio")
                            : ""
                    }
                />
                <FormControl
                    fullWidth
                    margin="normal"
                    error={!!requiredFields.deadlineType} // 1. Add error prop to FormControl
                >
                    <InputLabel>{t("Tipologia *")}</InputLabel>
                    <Select
                        name="deadlineType"
                        value={params.deadlineType}
                        onChange={onChangeInputs}
                    >
                        <MenuItem value="-1">Generica</MenuItem>
                        {tipologiaData.map((type: any, index: number) => (
                            <MenuItem value={type.id} key={index}>
                                {type.nome}
                            </MenuItem>
                        ))}
                    </Select>
                    <FormHelperText>
                        {requiredFields.deadlineType
                            ? t("Tipologia obbligatoria")
                            : ""}
                    </FormHelperText>
                </FormControl>
                <Grid
                    container
                    spacing={2}
                    sx={{
                        width: "100%",
                        mb: 2,
                        mt: 1,
                        flexWrap: "nowrap",
                        alignItems: "center",
                    }}
                >
                    <Grid item xs="auto">
                        <DatePicker
                            label=""
                            name="deadlineDate"
                            value={parseDate(params?.deadlineDate)}
                            onChange={onDateChange}
                            sx={{ width: inputWidth }}
                        />
                    </Grid>

                    <Grid item xs="auto">
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <TimePicker
                                value={parseTimeStringToDate(
                                    `${params.deadlineHours}:${params.deadlineMinutes}`
                                )}
                                onChange={handleTimeChange}
                                disabled={giornataSwitch}
                                ampm={false}
                                slotProps={{
                                    textField: {
                                        placeholder: "HH:MM",
                                        sx: { width: inputWidth },
                                    },
                                }}
                            />
                        </LocalizationProvider>
                    </Grid>
                </Grid>
                <Grid container spacing={2} alignItems="center" sx={{ mb: 2 }}>
                    <Grid item>
                        <LocalizationProvider dateAdapter={AdapterDateFns}>
                            <TimePicker
                                label={t("Durata")}
                                disabled={giornataSwitch}
                                value={parseTimeStringToDate(
                                    params.deadlinePeriod
                                )}
                                onChange={() => ""}
                                ampm={false}
                                views={["hours", "minutes"]}
                                slotProps={{
                                    textField: {
                                        name: "",
                                        placeholder: "HH:MM",
                                        sx: { width: inputWidth },
                                    },
                                }}
                            />
                        </LocalizationProvider>
                    </Grid>

                    <Grid item>
                        <FormControl component="fieldset" variant="standard">
                            <FormGroup>
                                <FormControlLabel
                                    control={
                                        <Toggle
                                            size="medium"
                                            checked={giornataSwitch}
                                            onChange={handleGiornataSwitch}
                                        />
                                    }
                                    label={t("Giornata intera")}
                                    sx={{ ml: 2, mt: 2.5 }}
                                />
                            </FormGroup>
                        </FormControl>
                    </Grid>
                </Grid>
                <Divider light sx={{ mt: 3 }} />
                <Grid container spacing={1} alignItems="center">
                    <Grid item>
                        <FormControl sx={{ width: 450, mt: 2 }}>
                            <Autocomplete
                                size="small"
                                options={[
                                    {
                                        id: "-1",
                                        name: t(
                                            "Seleziona il gruppo di utenti..."
                                        ),
                                    },
                                    ...(groupsOptions || []),
                                ]}
                                value={
                                    params.deadLinesGroups === "-1"
                                        ? {
                                              id: "-1",
                                              name: t(
                                                  "Seleziona il gruppo di utenti..."
                                              ),
                                          }
                                        : params.deadLinesGroups
                                }
                                isOptionEqualToValue={(
                                    option: any,
                                    value: any
                                ) => {
                                    return option.id === value.id;
                                }}
                                limitTags={1}
                                onChange={handleUsersGroup}
                                getOptionLabel={(option: any) => option?.name}
                                renderInput={(params: any) => (
                                    <TextField
                                        {...params}
                                        size="medium"
                                        label={t("Gruppo Utenti")}
                                        sx={{ width: 450 }}
                                    />
                                )}
                            />
                        </FormControl>
                    </Grid>
                    <Grid item>
                        <IconButton
                            onClick={(event: any) =>
                                setAnchorElGruppi(event.currentTarget)
                            }
                            sx={{ marginTop: "40px" }}
                            variant="outlined"
                        >
                            <FontAwesomeIcon
                                icon={faBell}
                                fontSize="15px"
                                size="sm"
                            />{" "}
                        </IconButton>
                        <Popover
                            open={openGruppiPopover}
                            anchorEl={anchorElGruppi}
                            onClose={() => setAnchorElGruppi(null)}
                            anchorOrigin={{
                                vertical: "bottom",
                                horizontal: "left",
                            }}
                            PaperProps={{
                                sx: { p: 2, borderRadius: 2 },
                            }}
                        >
                            <FormGroup sx={{ p: 1 }}>
                                <FormControlLabel
                                    sx={{ mb: 1 }}
                                    control={
                                        <Checkbox
                                            onChange={() => ""}
                                            name="notify"
                                            color="primary"
                                        />
                                    }
                                    label={t("Invia notifica")}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            onChange={() => ""}
                                            name="email"
                                            color="primary"
                                        />
                                    }
                                    label={t("Invia email")}
                                />
                            </FormGroup>
                        </Popover>
                    </Grid>
                </Grid>
                <Grid container spacing={1} alignItems="center">
                    <Grid item>
                        <FormControl sx={{ width: 450, mt: 2 }}>
                            <Autocomplete
                                disableCloseOnSelect
                                multiple
                                size="small"
                                options={userOptions || []}
                                value={params.deadlineUser}
                                onChange={handleUserData}
                                isOptionEqualToValue={(
                                    option: any,
                                    value: any
                                ) => {
                                    return option.id === value.id;
                                }}
                                getOptionLabel={(option: any) =>
                                    option.nomeutente
                                }
                                renderInput={(params: any) => (
                                    <TextField
                                        {...params}
                                        size="medium"
                                        label={t("Intestatari")}
                                        sx={{ width: 450 }}
                                    />
                                )}
                                noOptionsText={t("Nessuna opzione")}
                                renderTags={(
                                    value: any[],
                                    getTagProps: any
                                ) => {
                                    return value.map((option, index) => {
                                        const { key, ...tagProps } =
                                            getTagProps({
                                                index,
                                            });
                                        return (
                                            <div
                                                key={key}
                                                style={{
                                                    marginRight: "2px",
                                                    marginBottom: "2px",
                                                    marginTop: "2px",
                                                }}
                                            >
                                                <VaporTag
                                                    key={key}
                                                    label={option.nomeutente}
                                                    variant="filter"
                                                    {...tagProps}
                                                />
                                            </div>
                                        );
                                    });
                                }}
                            />
                        </FormControl>
                    </Grid>
                    <Grid item>
                        <IconButton
                            onClick={(event: any) =>
                                setAnchorElIntestatari(event.currentTarget)
                            }
                            sx={{ marginTop: "40px" }}
                            variant="outlined"
                        >
                            <FontAwesomeIcon
                                icon={faBell}
                                fontSize="15px"
                                size="sm"
                            />{" "}
                        </IconButton>
                        <Popover
                            open={openIntestatariPopover}
                            anchorEl={anchorElIntestatari}
                            onClose={() => setAnchorElIntestatari(null)}
                            anchorOrigin={{
                                vertical: "bottom",
                                horizontal: "left",
                            }}
                            PaperProps={{
                                sx: { p: 2, borderRadius: 2 },
                            }}
                        >
                            <FormGroup sx={{ p: 1 }}>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            onChange={() => ""}
                                            name="email"
                                            color="primary"
                                        />
                                    }
                                    label={t("Invia email")}
                                />
                            </FormGroup>
                        </Popover>
                    </Grid>
                </Grid>
            </DialogContent>

            <DialogActions
                sx={{
                    display: "flex",
                    alignItems: "end",
                    justifyContent: "end",
                }}
            >
                <Stack direction="row">
                    <Button
                        onClick={() => navigate("/impegno/update")}
                        sx={{ mr: 2 }}
                    >
                        {t("Altre opzioni")}
                    </Button>
                    <Button
                        variant="contained"
                        sx={{
                            ml: 1,
                        }}
                        onClick={() => saveDeadline(params)}
                    >
                        {t("Salva")}
                    </Button>
                </Stack>
            </DialogActions>
            <ConfirmModal
                open={showModal}
                handleDecline={() => handleModalConfirm(false)}
                handleAgree={() => handleModalConfirm(true)}
                decline={t("Annulla")}
                agree={t("Conferma")}
                confirmText={t(
                    "Stai associando manualmente le persone al gruppo. L'associazione di gruppo verra tolta."
                )}
                title={t("Vuoi continuare?")}
            />
        </Dialog>
    );
}
