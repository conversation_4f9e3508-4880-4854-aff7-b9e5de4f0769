import { Box, FormControl, InputLabel, TextField, FormControlLabel, Checkbox, FormGroup, Divider } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../../../../../../components/ui-kit/DatePicker";
import List from "./list";
import Tariff from "./tariff";
import Expense from "./expense";
import Timesheet from "./timesheet";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { TimePicker } from "@vapor/v3-components";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";import { it } from "date-fns/locale"; // Import Italian locale for DD/MM/YYYY format
import { useCalendarData } from "../../../context/CalendarDataContext";
import CustomAutocomplete from "../../../../../../../../custom-components/CustomAutocomplete";

interface IProps {
    showPerformanceForm: any;
    setShowPerformanceForm: any;
    savePerformanceParams: any;
    setSavePerformanceParams: any;
    closeForm: () => void;
}

const ICON = <CheckBoxOutlineBlankIcon fontSize="small" />;
const CHECKED_ICON = <CheckBoxIcon fontSize="small" />;

const parseDate = (dateString: string) => {
    if (!dateString) return null; // Check for empty value
    const [day, month, year] = dateString.split("/").map(Number);
    return new Date(year, month - 1, day);
};

export default function NewPerformanceForm(props: IProps) {
    const { showPerformanceForm, savePerformanceParams, setSavePerformanceParams } = props;
    const { t } = useTranslation();
    const { data } = useCalendarData();

    const handleUserData = (_e: any, newInputValue: any) => {
        setSavePerformanceParams({
            ...savePerformanceParams,
            prestazioneUser: newInputValue,
            prestazioneSelectedUsers: newInputValue
        });
    };

    const handleCheckboxChanges = (event: any) => {
        const { name, checked } = event.target;
        setSavePerformanceParams({ ...savePerformanceParams, [name]: checked });
    };

    const handleInputChange = (event: any) => {
        const { name, value } = event.target;
        setSavePerformanceParams({ ...savePerformanceParams, [name]: value });
    };

    const onDateChange = (name: string, value: Date) => {
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB");
        setSavePerformanceParams((prevValue: any) => ({
            ...prevValue,
            [name]: formattedDate
        }));
    };

    const onTimeChange = (time: any, _errors: any) => {
        const date = new Date(time);

        const formattedTime = date.toLocaleTimeString("it-IT", {
            hour: "2-digit",
            minute: "2-digit"
        });

        setSavePerformanceParams((prevParams: any) => ({
            ...prevParams,
            duration: formattedTime
        }));
    };

    function convertToDate(timeString: string) {
        const [hours, minutes] = timeString.split(":").map(Number);
        const date = new Date();
        date.setHours(hours);
        date.setMinutes(minutes);
        date.setSeconds(0);
        date.setMilliseconds(0);
        return date;
    }

    return (
        <div style={{ display: "flex", gap: "100px", marginLeft: "10px" }}>
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    "& .MuiTextField-root": {
                        m: 1,
                        width: 500
                    }
                }}
            >
                {/* <Typography sx={{ mb: 5 }}>{t("Nuova prestazione")}</Typography> */}

                <FormControl sx={{ width: 500 }}>
                    <InputLabel>{t("Data")}</InputLabel>
                    <DatePicker 
                        label="" 
                        value={parseDate(savePerformanceParams.externalDate)} 
                        onChange={(value: Date | null) => {
                            if (value) onDateChange("externalDate", value);
                        }} 
                    />
                </FormControl>

                <FormControl sx={{ width: 500 }}>
                    <InputLabel>{t("Utenti")}</InputLabel>
                    <CustomAutocomplete
                        disableCloseOnSelect
                        clearOnEscape
                        multiple
                        options={data?.usersData || []}
                        value={savePerformanceParams.prestazioneUser}
                        onChange={handleUserData}
                        getOptionLabel={(option: any) => option.nomeutente}
                        renderInput={(params: any) => <TextField {...params} />}
                        isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
                        renderOption={(props: any, option: any) => {
                            const isChecked = savePerformanceParams?.prestazioneUser.some((user: any) => user?.id === option.id);

                            return (
                                <li {...props} key={option.nomeutente}>
                                    <Checkbox icon={ICON} checkedIcon={CHECKED_ICON} style={{ marginRight: 8 }} checked={isChecked} />
                                    {option.nomeutente}
                                </li>
                            );
                        }}
                    />
                </FormControl>
                {showPerformanceForm.expense || (showPerformanceForm.timesheet && <TextField label={t("Descrizione")} name="nome" value={savePerformanceParams.nome} onChange={handleInputChange} multiline rows={2} />)}
                <FormControl sx={{ ml: 1, mb: 1, mt: 2 }}>
                    <FormGroup row>
                        <FormControlLabel
                            value="left"
                            control={<Checkbox name="addebitabile" checked={savePerformanceParams.addebitabile} onChange={handleCheckboxChanges} />}
                            label="Addebitabile"
                            labelPlacement="end"
                            sx={{
                                width: "160px" // Set a fixed width to align labels
                            }}
                        />
                    </FormGroup>
                </FormControl>

                <FormControl sx={{ ml: 1 }}>
                    <FormGroup row>
                        <FormControlLabel
                            value="left"
                            control={<Checkbox name="fatturabile" checked={savePerformanceParams.fatturabile} onChange={handleCheckboxChanges} />}
                            label="Fatturabile"
                            labelPlacement="end"
                            sx={{
                                width: "160px" // Same width for both FormControlLabels
                            }}
                        />
                    </FormGroup>
                </FormControl>

                {!showPerformanceForm.timesheet && (
                    <TextField
                        label={t("Quantità")}
                        name="quantita"
                        value={savePerformanceParams.quantita}
                        onChange={handleInputChange}
                        onKeyDown={(e) => {
                            if (e.key === "-") {
                                e.preventDefault();
                            }
                        }}
                        type="number"
                    />
                )}

                {showPerformanceForm.timesheet && (
                    <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={it}>
                        <TimePicker label={t("Quantità")} name="duration" onChange={onTimeChange} value={convertToDate(savePerformanceParams.duration)} sx={{ width: "250px !important" }} />
                    </LocalizationProvider>
                )}

                {showPerformanceForm.expense && (
                    <TextField
                        label={t("Valore")}
                        name="valore"
                        value={savePerformanceParams.valore}
                        onChange={handleInputChange}
                        onKeyDown={(e) => {
                            if (e.key === "-") {
                                e.preventDefault();
                            }
                        }}
                        type="number"
                    />
                )}

                <Divider light sx={{ mt: 1 }} />

                {showPerformanceForm.list && <List savePerformanceParams={savePerformanceParams} setSavePerformanceParams={setSavePerformanceParams} />}
                {showPerformanceForm.tariff && <Tariff savePerformanceParams={savePerformanceParams} setSavePerformanceParams={setSavePerformanceParams} />}
                {showPerformanceForm.expense && <Expense savePerformanceParams={savePerformanceParams} setSavePerformanceParams={setSavePerformanceParams} />}
                {showPerformanceForm.timesheet && <Timesheet savePerformanceParams={savePerformanceParams} setSavePerformanceParams={setSavePerformanceParams} />}
            </Box>
        </div>
    );
}
