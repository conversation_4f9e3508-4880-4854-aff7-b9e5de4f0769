import {
    FormControl,
    InputLabel,
    TextField,
    Box,
    MenuItem,
    Select,
    Radio,
    RadioGroup,
    FormControlLabel,
    Checkbox,
} from "@vapor/react-material";
import { DatePicker } from "../../../../../../components/ui-kit/DatePicker";
import {
    RIRTEMPLATE_DATA,
    WEEK_OPTIONS,
    MONTH_OPTIONS,
} from "../constants/constant";
import { IDeadlineParams } from "../interfaces/impegno.interface";
import { useEffect } from "react";
import { useTranslation } from "@1f/react-sdk";

interface InstrumentsProps {
    deadlineSaveParams: IDeadlineParams;
    setDeadlineSaveParams: React.Dispatch<
        React.SetStateAction<IDeadlineParams>
    >;
}

const parseDate = (dateString: string) => {
    if (!dateString) return null; // Check for empty value
    const [day, month, year] = dateString.split("/").map(Number);
    return new Date(year, month - 1, day);
};

export default function Instruments(props: InstrumentsProps) {
    const { deadlineSaveParams, setDeadlineSaveParams } = props;
    const { t } = useTranslation();

    const onChangeInput = (event: any) => {
        const { name, value } = event.target;
        setDeadlineSaveParams({ ...deadlineSaveParams, [name]: value });
    };

    const onChangeTemplate = (event: any) => {
        const { name, value } = event.target;
        setDeadlineSaveParams({
            ...deadlineSaveParams,
            [name]: value,
        });
    };

    const onChangeCheck = (event: any) => {
        const { name, value, checked } = event.target;

        setDeadlineSaveParams((prevParams: any) => {
            if (checked) {
                return { ...prevParams, [name]: value };
            } else {
                const { [name]: _, ...rest } = prevParams; // Remove the property using destructuring
                return rest;
            }
        });
    };

    useEffect(() => {
        if (
            !deadlineSaveParams ||
            Object.keys(deadlineSaveParams).length === 0
        ) {
            setDeadlineSaveParams((prev: any) => ({
                ...prev,
                rirtemplate: RIRTEMPLATE_DATA[0].value,
                riweeklyweekdaysSU: WEEK_OPTIONS[0].value,
                riyearlydayofmonthmonth: MONTH_OPTIONS[0].value,
            }));
        }
    }, [deadlineSaveParams, setDeadlineSaveParams]);

    // Split into two rows
    const firstRow = WEEK_OPTIONS.slice(0, 4); // First 4 days
    const secondRow = WEEK_OPTIONS.slice(4); // Remaining 3 days

    return (
        <Box
            display="flex"
            flexDirection="column"
            gap="30px"
            sx={{ ml: "10px" }}
        >
            <FormControl sx={{ width: 400 }}>
                <InputLabel>{t("Ripetizione")}</InputLabel>
                <Select
                    label="Ripetizione"
                    name="rirtemplate"
                    value={deadlineSaveParams.rirtemplate}
                    onChange={onChangeTemplate}
                >
                    {RIRTEMPLATE_DATA.map((data: any, index: number) => (
                        <MenuItem value={data.value} key={index}>
                            {t(data.name)}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>

            {/* Ripeti ogni */}
            {deadlineSaveParams.rirtemplate === "daily" && (
                <Box display="flex" alignItems="center" gap="20px">
                    <InputLabel>{t("Ripeti ogni")}</InputLabel>
                    <TextField
                        name="ridailyinterval"
                        type="number"
                        value={deadlineSaveParams.ridailyinterval}
                        onChange={onChangeInput}
                        sx={{ width: 100 }}
                    />
                    <InputLabel>{t("giorno(i)")}</InputLabel>
                </Box>
            )}

            {deadlineSaveParams.rirtemplate === "weekly" && (
                <>
                    <Box display="flex" alignItems="center" gap="20px">
                        <InputLabel>{t("Ripeti ogni")}</InputLabel>
                        <TextField
                            name="riweeklyinterval"
                            type="number"
                            value={deadlineSaveParams.riweeklyinterval}
                            onChange={onChangeInput}
                            sx={{ width: 100 }}
                        />
                        <InputLabel>{t("settimana(e)")}</InputLabel>
                    </Box>
                    <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="flex-start"
                        gap="20px"
                    >
                        <InputLabel>{t("Ripeti ogni")}</InputLabel>
                        <Box display="flex" flexDirection="column" gap={2}>
                            <Box display="flex" gap={2}>
                                {firstRow.map((day, index) => (
                                    <FormControlLabel
                                        key={index}
                                        control={
                                            <Checkbox
                                                checked={
                                                    deadlineSaveParams[
                                                        day.name
                                                    ] === day.value
                                                }
                                                onChange={onChangeCheck}
                                                name={day.name}
                                                value={day.value}
                                            />
                                        }
                                        label={t(day.label)}
                                    />
                                ))}
                            </Box>
                            <Box display="flex" gap={2}>
                                {secondRow.map((day, index) => (
                                    <FormControlLabel
                                        key={index}
                                        control={
                                            <Checkbox
                                                checked={
                                                    deadlineSaveParams[
                                                        day.name
                                                    ] === day.value
                                                }
                                                onChange={onChangeCheck}
                                                name={day.name}
                                                value={day.value}
                                            />
                                        }
                                        label={t(day.label)}
                                    />
                                ))}
                            </Box>
                        </Box>
                    </Box>
                </>
            )}

            {deadlineSaveParams.rirtemplate === "monthly" && (
                <>
                    <Box display="flex" alignItems="center" gap="20px">
                        <InputLabel>{t("Ripeti ogni")}</InputLabel>
                        <TextField
                            name="rimonthlyinterval"
                            type="number"
                            value={deadlineSaveParams.rimonthlyinterval}
                            sx={{ width: 100 }}
                            onChange={onChangeInput}
                        />
                        <InputLabel>{t("mese(i)")}</InputLabel>
                    </Box>
                    <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="flex-start"
                        gap="20px"
                    >
                        <Box display="flex" alignItems="center" gap="20px">
                            <InputLabel>
                                {t("Ripeti ogni giorno(i)")}
                            </InputLabel>
                            <FormControl sx={{ width: 150 }}>
                                <Select
                                    name="rimonthlydayofmonthday"
                                    value={
                                        deadlineSaveParams.rimonthlydayofmonthday
                                    }
                                    onChange={onChangeInput}
                                >
                                    {[...Array(31)].map((_, index) => (
                                        <MenuItem
                                            key={index + 1}
                                            value={index + 1}
                                        >
                                            {index + 1}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                            <InputLabel>{t("Del mese")}</InputLabel>
                        </Box>
                    </Box>
                </>
            )}

            {deadlineSaveParams.rirtemplate === "yearly" && (
                <>
                    <Box display="flex" alignItems="center" gap="20px">
                        <InputLabel>{t("Ripeti ogni")}</InputLabel>
                        <TextField
                            name="riyearlyinterval"
                            type="number"
                            value={deadlineSaveParams.riyearlyinterval}
                            sx={{ width: 100 }}
                            onChange={onChangeInput}
                        />
                        <InputLabel>{t("anno(i)")}</InputLabel>
                    </Box>
                    <Box
                        display="flex"
                        flexDirection="column"
                        alignItems="flex-start"
                        gap="20px"
                    >
                        <Box display="flex" alignItems="center" gap="20px">
                            <InputLabel>
                                {t("Ripeti ogni giorno(i)")}
                            </InputLabel>
                            <FormControl sx={{ width: 150 }}>
                                <Select
                                    name="riyearlydayofmonthmonth"
                                    value={
                                        deadlineSaveParams.riyearlydayofmonthmonth
                                    }
                                    onChange={onChangeInput}
                                >
                                    {MONTH_OPTIONS.map(
                                        (month: any, index: number) => (
                                            <MenuItem
                                                key={index}
                                                value={month.value}
                                            >
                                                {month.label}
                                            </MenuItem>
                                        )
                                    )}
                                </Select>
                            </FormControl>
                            <InputLabel>{t("Del mese")}</InputLabel>
                        </Box>
                    </Box>
                </>
            )}

            {/* Fine ripetizione */}
            <Box display="flex" flexDirection="column" gap="20px">
                <InputLabel>{t("Fine ripetizione")}</InputLabel>
                <RadioGroup>
                    <FormControlLabel
                        value="dopo"
                        control={
                            <Radio
                                name="endType"
                                value="BYOCCURRENCES"
                                checked={
                                    deadlineSaveParams.endType ===
                                    "BYOCCURRENCES"
                                }
                                onChange={() =>
                                    setDeadlineSaveParams({
                                        ...deadlineSaveParams,
                                        endType: "BYOCCURRENCES",
                                    })
                                }
                            />
                        }
                        label={
                            <Box display="flex" alignItems="center" gap="10px">
                                {t("Dopo")}
                                <TextField
                                    name="occurrencesN"
                                    type="number"
                                    value={deadlineSaveParams.occurrencesN}
                                    sx={{ width: 100, ml: 1.5 }}
                                    onChange={onChangeInput}
                                />
                                {t("Ripetizioni")}
                            </Box>
                        }
                        sx={{ marginBottom: 2 }} // Adds space below this FormControlLabel
                    />
                    <FormControlLabel
                        value="inData"
                        control={
                            <Radio
                                name="endType"
                                value="BYENDDATE"
                                checked={
                                    deadlineSaveParams.endType === "BYENDDATE"
                                } // Check if selected
                                onChange={() =>
                                    setDeadlineSaveParams({
                                        ...deadlineSaveParams,
                                        endType: "BYENDDATE",
                                    })
                                }
                            />
                        }
                        label={
                            <Box display="flex" alignItems="center" gap="10px">
                                {t("In data")}
                                <DatePicker
                                    label=""
                                    name="rirangebyenddatecalendar"
                                    value={parseDate(
                                        deadlineSaveParams.rirangebyenddatecalendar
                                    )}
                                    onChange={onChangeInput}
                                />
                            </Box>
                        }
                        sx={{ marginBottom: 2 }} // Adds space below this FormControlLabel
                    />
                </RadioGroup>
            </Box>
        </Box>
    );
}
