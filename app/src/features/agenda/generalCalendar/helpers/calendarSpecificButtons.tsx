import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>, <PERSON>ton, <PERSON>rid, <PERSON><PERSON>, Typography, Menu, MenuItem } from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import ErrorIcon from "@mui/icons-material/Error";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
    faListCheck,
    faEllipsisVertical,
    faChevronDown,
} from "@fortawesome/pro-regular-svg-icons";
import { faCalendar } from "@fortawesome/free-regular-svg-icons";
import moment from "moment";
import { ICalendarButtonsProps } from "../typings/generalCalendar.interface";
import { gettingCalendarViewName } from "./gettingCalendarViewName";
import { updateCalendar } from "./calendarHelper";

export default function CalendarSpecificButtons(props: ICalendarButtonsProps) {
    const {
        query,
        setQuery,
        calendarRef,
        set<PERSON>onth<PERSON><PERSON><PERSON>,
        t,
        selectedView,
        onViewChange
    } = props;
    const [openDatePicker, setOpenDatePicker] = useState(false);
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

    const getCurrentCalendarDate = () => {
        const calendarAPI = calendarRef?.current?.getApi();
        if (calendarAPI) {
            return calendarAPI.getDate();
        }
        if (query.date && query.date !== 0) {
            if (query.date > 1000000000000) {
                return moment(query.date).toDate();
            } else {
                return moment.unix(query.date).toDate();
            }
        }
        return moment().toDate();
    };
    const open = Boolean(anchorEl);

    const goToDate = (date: any) => {
        const calendarAPI = calendarRef?.current?.getApi();
        if (!calendarAPI) return;

        const dateMoment = moment(date);
        const isWeekend = dateMoment.isoWeekday() >= 6;
        const currentView = calendarAPI.view?.type || selectedView || 'dayGridMonth';

        if (currentView === 'dayGridMonth') {
            calendarAPI.gotoDate(dateMoment.toDate());
        } else if (currentView === 'timeGridWeek') {
            calendarAPI.gotoDate(dateMoment.toDate());
        } else if (currentView === 'timeGridDay') {
            calendarAPI.gotoDate(dateMoment.toDate());
        } else {
            calendarAPI.gotoDate(dateMoment.toDate());
        }

        let updatedQuery;
        if (currentView === 'dayGridMonth') {
            const monthStart = dateMoment.clone().startOf('month');
            const monthEnd = dateMoment.clone().endOf('month');
            updatedQuery = {
                ...query,
                start: monthStart.unix(),
                end: monthEnd.unix(),
                date: dateMoment.unix(),
                calendarWeekends: isWeekend,
                viewName: gettingCalendarViewName(currentView)
            };
        } else if (currentView === 'timeGridWeek' || selectedView === 'timeGridWorkWeek') {
            const weekStart = dateMoment.clone().startOf('week');
            const weekEnd = dateMoment.clone().endOf('week');
            updatedQuery = {
                ...query,
                start: weekStart.unix(),
                end: weekEnd.unix(),
                date: dateMoment.unix(),
                calendarWeekends: selectedView === 'timeGridWorkWeek' ? false : isWeekend,
                viewName: selectedView === 'timeGridWorkWeek' ? 'timeGridWorkWeek' : gettingCalendarViewName(currentView)
            };
        } else {
            const dayStart = dateMoment.clone().startOf('day');
            const dayEnd = dateMoment.clone().endOf('day');
            updatedQuery = {
                ...query,
                start: dayStart.unix(),
                end: dayEnd.unix(),
                date: dateMoment.unix(),
                calendarWeekends: isWeekend,
                viewName: gettingCalendarViewName(currentView)
            };
        }

        updateCalendar(
            calendarAPI,
            updatedQuery,
            setQuery,
            setMonthTitle,
            t,
            currentView
        );

        if (onViewChange && calendarAPI.view?.type !== currentView) {
            onViewChange(currentView);
        }
    };

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <Grid
            container
            spacing={2}
            justifyContent="flex-end"
            alignItems="center"
            sx={{ flexGrow: 1 }}
        >
            <Grid item md={1}>
                <Badge
                    badgeContent={
                        <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            p: 0.5
                        }}>
                            <ErrorIcon
                                fontSize="small"
                            />
                            <Typography variant="body2" sx={{ m: 1 }}>99+</Typography>
                        </Box>
                    }
                >
                </Badge>
            </Grid>
            <Grid item md={2.5}>
                <Stack direction="row" spacing={0.3}>
                    <Button
                        variant="contained"
                        color="info"
                        sx={{
                            width: 80,
                            borderRadius: '8px 0 0 8px'
                        }}
                    >
                        Crea
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleClick}
                        sx={{
                            width: 10,
                            borderRadius: '0 8px 8px 0'
                        }}
                    >
                        <FontAwesomeIcon icon={faChevronDown} size="lg" />
                    </Button>
                    <Menu
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                    >
                        <MenuItem onClick={handleClose}>Opzione 1</MenuItem>
                        <MenuItem onClick={handleClose}>Opzione 2</MenuItem>
                        <MenuItem onClick={handleClose}>Opzione 3</MenuItem>
                    </Menu>
                </Stack>
            </Grid>
            <Grid item>
                <Button
                    onClick={() => setOpenDatePicker(true)}
                >
                    <FontAwesomeIcon icon={faCalendar} size="lg" />
                </Button>
                <Button
                    disabled={true}
                >
                    <FontAwesomeIcon icon={faListCheck} size="lg" />
                </Button>
                <Button
                    disabled={true}
                >
                    <FontAwesomeIcon icon={faEllipsisVertical} size="lg" />
                </Button>
            </Grid>
            {openDatePicker && (
                <DatePicker
                    open={openDatePicker}
                    onClose={() => setOpenDatePicker(false)}
                    value={getCurrentCalendarDate()}
                    onChange={(date: any) => goToDate(date)}
                    slotProps={{
                        textField: {
                            error: false,
                            sx: { visibility: "hidden", width: 0 },
                        },
                    }}
                />
            )}
        </Grid>
    );
}
