import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>rid, <PERSON><PERSON>, <PERSON>u, MenuItem } from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { Chip } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faListCheck } from "@fortawesome/pro-regular-svg-icons";
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import moment from "moment";
import { ICalendarButtonsProps } from "../typings/generalCalendar.interface";
import { updateCalendar } from "./calendarHelper";
import ErrorIcon from '@mui/icons-material/Error';

export default function CalendarCustomButtons(props: ICalendarButtonsProps) {
    const { query, setQuery, calendarRef, setMonthTitle, t } = props;
    const [openDatePicker, setOpenDatePicker] = useState(false);
    const [currentDate] = useState<Date>(() => {
        const savedDate = localStorage.getItem("calendarCurrentDate");
        return savedDate ? new Date(savedDate) : new Date();
    });
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const open = Boolean(anchorEl);

    useEffect(() => {
        const calendarApi = calendarRef.current?.getApi();
        if (calendarApi) {
            calendarApi.gotoDate(currentDate);
        }
    }, [currentDate]);

    const goToDate = (date: any) => {
        const calendarAPI = calendarRef?.current?.getApi();
        if (calendarAPI) {
            const isWeekend = moment(date).isoWeekday() >= 6;
            calendarAPI.setOption("weekends", isWeekend);

            const currentView = query.viewName || "month";

            calendarAPI.gotoDate(new Date(date));

            let result = moment.utc(
                moment(date).format("DD-MM-YYYY"),
                "DD-MM-YYYY"
            );

            let updatedQuery;
            if (currentView === "basicDay") {
                updatedQuery = {
                    ...query,
                    start: result.unix(),
                    end: result.add(1, "days").unix(),
                    date: moment(date).unix(),
                    calendarWeekends: isWeekend,
                };
            } else {
                updatedQuery = {
                    ...query,
                    date: moment(date).unix(),
                    calendarWeekends: isWeekend,
                };
            }

            updateCalendar(
                calendarAPI,
                updatedQuery,
                setQuery,
                setMonthTitle,
                t,
                currentView
            );
        }
    };

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <Grid
            container
            spacing={2}
            justifyContent="flex-end"
            alignItems="center"
            sx={{ flexGrow: 1 }}
        >
            <Grid item md={1}>
                <Chip
                    icon={<ErrorIcon fontSize="small" />}
                    label="99+"
                    size="small"
                    sx={{
                        bgcolor: 'red',
                        color: 'white',
                        px: 0.5,
                        '& .MuiChip-icon': {
                            color: 'white'
                        },
                        '& .MuiChip-label': {
                            fontSize: '0.875rem',
                            fontWeight: 500
                        }
                    }}
                />
            </Grid>
            <Grid item md={2.5}>
                <Stack direction="row" spacing={0.3}>
                    <Button
                        variant="contained"
                        color="info"
                        sx={{
                            width: 80,
                            borderRadius: '8px 0 0 8px'
                        }}
                    >
                        Crea
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleClick}
                        sx={{
                            width: 10,
                            borderRadius: '0 8px 8px 0'
                        }}
                    >
                        <KeyboardArrowDownIcon />
                    </Button>
                    <Menu
                        anchorEl={anchorEl}
                        open={open}
                        onClose={handleClose}
                        anchorOrigin={{
                            vertical: 'bottom',
                            horizontal: 'right',
                        }}
                        transformOrigin={{
                            vertical: 'top',
                            horizontal: 'right',
                        }}
                    >
                        <MenuItem onClick={handleClose}>Opzione 1</MenuItem>
                        <MenuItem onClick={handleClose}>Opzione 2</MenuItem>
                        <MenuItem onClick={handleClose}>Opzione 3</MenuItem>
                    </Menu>
                </Stack>
            </Grid>
            <Grid item>
                <Button
                    onClick={() => setOpenDatePicker(true)}
                    disabled={false}
                >
                    <CalendarTodayIcon />
                </Button> <Button
                    disabled={true}
                >
                    <FontAwesomeIcon icon={faListCheck} />
                </Button>
                <Button
                    disabled={true}
                >
                    <MoreVertIcon />
                </Button>
            </Grid>
            {openDatePicker && (
                <DatePicker
                    open={openDatePicker}
                    onClose={() => setOpenDatePicker(false)}
                    value={null}
                    onChange={(date: any) => goToDate(date)}
                    slotProps={{
                        textField: {
                            error: false,
                            sx: { visibility: "hidden", width: 0 },
                        },
                    }}
                />
            )}
        </Grid>
    );
}
