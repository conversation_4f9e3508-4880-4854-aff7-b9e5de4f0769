import React from "react";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { TimePickerUi } from "../../../../custom-components/react-hook-components/Timepicker";
import { Box, Button, InputAdornment, Checkbox, FormGroup, FormControlLabel, TextField, Typography } from "@vapor/react-material";
import { Controller } from "react-hook-form";
import dayjs from "dayjs";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { useParams } from "react-router-dom";
import useGetCustom from "../../../../hooks/useGetCustom";
import { parse } from "date-fns";
import { convertMinutesToDate, convertMinutesToTime } from "../utils";
import { debounce } from "lodash";
import { removeLinks } from "../../../../utilities/utils";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

const CreateAttivita = (props: any) => {
    const { method, state } = props;
    const [search, setSearch] = React.useState("");
    const [attivitaSearch, setAttivitaSearch] = React.useState("");
    const [attivita, setAttivita] = React.useState([]);
    const [taskFiles, setTaskFiles] = React.useState([]);
    const [selectedFile, setSelectedFile] = React.useState({
        value: "",
        label: ""
    });

    const params = useParams<{ id: string }>();

    const { t } = useTranslation();

    const getTaskFileDataReq = useGetCustom(search ? `default/archive/search?from=calendar&noTemplateVars=true&q=${search}` : "default/archive/search?from=calendar&noTemplateVars=true");

    const getSearchArchiveDataReq = useGetCustom(attivitaSearch ? `default/archivetimesheet/search-attivita?q=${attivitaSearch}&noTemplateVars=true` : `default/archivetimesheet/search-attivita?&noTemplateVars=true`);

    const { control, setValue, watch } = method;

    const { taskData, activityData } = state || {};

    const values = watch();

    const users = activityData?.people?.map((item: any) => {
        return {
            label: item.nome,
            value: item.id
        };
    });

    React.useEffect(() => {
        if (state) {
            if (taskData && Object.keys(taskData).length > 0) {
                fillFormValues(taskData);
            }
        }
    }, [state]);

    const fillFormValues = (taskData: any) => {
        setValue("ended_at", params.id ? dayjs(convertMinutesToDate(taskData.taskEndedAt)) : dayjs(taskData.taskEndedAt));

        setValue("started_at", params.id ? dayjs(convertMinutesToDate(taskData.taskStartedAt)) : dayjs(taskData.taskStartedAt));

        setValue("duration", params.id ? dayjs(`1970-01-01T${convertMinutesToTime(taskData?.taskDuration)}`) : dayjs(taskData.taskDuration));
        setValue("date", taskData.taskDate && parse(taskData.taskDate, "dd/MM/yyyy", new Date()));
        setValue("timesheetRate", taskData.timesheetRate);
        setValue("addebitabile", taskData.taskAddebitabile === "1");
        setValue("user", taskData.taskUser);
        setValue("billable", taskData.taskBillable === "1");
        setValue("processed", taskData.taskProcessed === "1");
        setValue("visible", taskData.taskVisible === "1");
        setValue("task", taskData.taskActivity);
        setValue("annotations", taskData.annotations);
        setValue("id", params.id);
        if (taskData.file) {
            setValue("file_id", taskData.file.id);
            setSelectedFile({
                value: taskData.file.id,
                label: `${taskData.file.codicearchivio}:${taskData.file.listaclienti}`
            });
        }
    };

    const getAttivitaData = React.useCallback(async () => {
        const response: any = await getSearchArchiveDataReq.doFetch(true);
        setAttivita(
            response.data.map((item: any) => ({
                value: item.attivita,
                label: `${item.nome}`
            }))
        );
    }, [attivitaSearch]);

    const getTaskFileData = React.useCallback(async () => {
        const response: any = await getTaskFileDataReq.doFetch(true);
        setTaskFiles(
            response.data.map((item: any) => ({
                value: item.id,
                label: item.codicearchivio ? `${item.codicearchivio}:${removeLinks(item.headerArchive)}` : removeLinks(item.headerArchive)
            }))
        );
    }, [search]);

    const debouncedGetTaskFileData = React.useCallback(debounce(getTaskFileData, 500), [getTaskFileData]);

    const debouncedGetAttivitaData = React.useCallback(debounce(getAttivitaData, 500), [getAttivitaData]);

    React.useEffect(() => {
        debouncedGetTaskFileData();

        return () => {
            debouncedGetTaskFileData.cancel();
        };
    }, [search, debouncedGetTaskFileData]);

    React.useEffect(() => {
        if (attivitaSearch || params.id) debouncedGetAttivitaData();

        return () => {
            debouncedGetAttivitaData.cancel();
        };
    }, [attivitaSearch, debouncedGetAttivitaData]);

    let started_at = watch("started_at");
    let ended_at = watch("ended_at");
    let duration = watch("duration");

    const isDurationChange = React.useRef(false);

    React.useEffect(() => {
        if (started_at && ended_at && !isDurationChange.current) {
            if (started_at.isAfter(ended_at)) {
                ended_at = ended_at.add(1, "hour");
                setValue("ended_at", ended_at);
                return;
            }
            const durationMinutes = dayjs(ended_at).diff(dayjs(started_at), "minute");
            const durationTime = dayjs().hour(0).minute(durationMinutes);
            setValue("duration", durationTime, { shouldValidate: true });
        }
        isDurationChange.current = false;
    }, [started_at, ended_at]);

    React.useEffect(() => {
        if (duration && started_at) {
            const durationMinutes = duration.hour() * 60 + duration.minute();
            const newEndedAt = dayjs(started_at).add(durationMinutes, "minute");

            isDurationChange.current = true;

            if (started_at.isAfter(ended_at)) {
                ended_at = ended_at.add(1, "hour");
                setValue("ended_at", ended_at);
                return;
            }

            setValue("ended_at", newEndedAt, { shouldValidate: false });
        }
    }, [duration]);

    return (
        <>
            <Box display="flex" gap={1}>
                <FormInput style={{ width: 350 }} control={control} name="taskCategory" type="select" label={t("Categoria")} options={[{ value: "-1", label: "-" }]} />
            </Box>

            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <CustomAutocomplete
                    size="small"
                    options={taskFiles}
                    isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                    renderInput={(params: any) => <TextField {...params} placeholder={t("Cerca pratica per codice, descrizione, nominativi, RG…")} />}
                    onInputChange={(_event: any, value: any, reason: string) => {
                        if (reason === "input") {
                            setSearch(value);
                        }
                    }}
                    value={selectedFile}
                    onChange={(_event: any, item: any) => {
                        setValue("file_id", item.value);
                        setSelectedFile(item);
                    }}
                />
                <Button
                    type="button"
                    variant="outlined"
                    size="small"
                    onClick={() => {
                        setValue("file_id", "");
                        setSelectedFile({ value: "", label: "" });
                    }}
                >
                    {t("Cambia")}
                </Button>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="archiviate"
                                checked={values.archiviate}
                                onChange={() => {
                                    setValue("archiviate", values.archiviate ? false : true);
                                }}
                            />
                        }
                        label={t("Archiviate")}
                    />
                </FormGroup>
            </Box>

            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <div style={{ width: 250 }}>
                    <Controller
                        name="date"
                        control={control}
                        render={({ field, fieldState: { error } }) => (
                            <DatePicker
                                label={t("Data")}
                                value={field.value || null}
                                onChange={field.onChange}
                                slotProps={{
                                    textField: {
                                        error: !!error,
                                        helperText: error?.message,
                                    },
                                }}
                            />
                        )}
                    />
                </div>
                <TimePickerUi label={t("Ora inizio")} name="started_at" control={control} />
                <TimePickerUi label={t("Ora fine")} name="ended_at" control={control} />
                <TimePickerUi label={t("Durata")} name="duration" control={control} ampm={false} />
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormInput control={control} name="user" type="select" label={t("Utente")} options={users || []} style={{ width: 350 }} />
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormInput
                    control={control}
                    label={t("Tariffa")}
                    type="number"
                    name="timesheetRate"
                    style={{ width: 150 }}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start" variant="standard" sx={{ color: "red" }}>
                                {t("€")}
                            </InputAdornment>
                        )
                    }}
                />
                <Typography variant="caption" gutterBottom component="div" sx={{ mt: 5 }}>
                    {t(" compresa tra 10.00 € e 10.00 €")}
                </Typography>
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <CustomAutocomplete
                    size="small"
                    options={attivita || []}
                    isOptionEqualToValue={(option: any, value: any) => option.value === value.value}
                    renderInput={(params: any) => <TextField style={{ width: 350 }} {...params} placeholder={t("Cerca attività per nome")} />}
                    onInputChange={(_event: any, value: any, reason: string) => {
                        if (reason === "input") {
                            setAttivitaSearch(value);
                        }
                    }}
                    onChange={(_event: any, item: any) => {
                        setValue("task", item.value);
                    }}
                    value={attivitaSearch}
                />
            </Box>

            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormInput control={control} name="task" type="textarea" label={t("Attività")} row="5" style={{ width: 350 }} />
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="processed"
                                checked={values.processed}
                                onChange={() => {
                                    setValue("processed", values.processed ? false : true);
                                }}
                            />
                        }
                        label={t("Evasa")}
                    />
                </FormGroup>
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="addebitabile"
                                checked={values.addebitabile}
                                onChange={() => {
                                    setValue("addebitabile", values.addebitabile ? false : true);
                                }}
                            />
                        }
                        label={t("Addebitabile")}
                    />
                </FormGroup>
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="billable"
                                checked={values.billable}
                                onChange={() => {
                                    setValue("billable", values.billable ? false : true);
                                }}
                            />
                        }
                        label={t("Fatturabile")}
                    />
                </FormGroup>
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="visible"
                                checked={values.visible}
                                onChange={() => {
                                    setValue("visible", values.visible ? false : true);
                                }}
                            />
                        }
                        label={t("Visibile")}
                    />
                    <Typography variant="captionSmall" gutterBottom component="div">
                        {t(" Rendi l'attività visibile agli utenti esterni")}
                    </Typography>
                </FormGroup>
            </Box>
            <Box display="flex" gap={1} sx={{ mt: 2 }}>
                <FormGroup>
                    <FormControlLabel
                        control={
                            <Checkbox
                                name="definito"
                                checked={values.definito}
                                onChange={() => {
                                    setValue("definito", values.definito ? false : true);
                                }}
                                disabled={!taskData?.permissions?.definito}
                            />
                        }
                        label={t("Definito")}
                    />
                    <Typography variant="captionSmall" gutterBottom component="div">
                        {t(" Rendi l'attività non modificabile")}
                    </Typography>
                </FormGroup>
            </Box>
        </>
    );
};

export default CreateAttivita;
