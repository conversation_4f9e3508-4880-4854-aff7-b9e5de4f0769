import {
    Box,
    Button,
    TextField,
    FormControl,
    Select,
    MenuItem,
    Typography,
    InputLabel,
} from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";

export default function Filters(props: any) {
    const { t } = useTranslation();

    const {
        params: defaultParams,
        setDefaultParams,
        feeTypes,
        users,
        clearAll,
        dateRange,
        setDateRange,

        onSubmit,
    } = props;

    const onChangeInput = (e: any) => {
        const { name, value } = e.target;
        setDefaultParams({
            ...defaultParams,
            [name]: value,
        });
    };

    const formatDate = (date: Date): string => {
        const day = date.getDate().toString().padStart(2, "0");
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const year = date.getFullYear().toString();
        return `${day}/${month}/${year}`;
    };

    const onDateChange = (name: string, value: Date) => {
        const formatedDate = formatDate(value);
        setDateRange((prevDate: any) => ({ ...prevDate, [name]: value }));

        setDefaultParams({
            ...defaultParams,
            [name]: formatedDate,
        });
    };

    return (
        <Box
            component="form"
            display="flex"
            flexDirection="column"
            alignItems="start"
            gap={2}
        >
            <Box display="flex" alignItems="end" gap={2}>
                <div style={{ width: "25%" }}>
                    <InputLabel>{t("Dal")}</InputLabel>
                    <DatePicker
                        label={t("Dal:")}
                        value={dateRange.date}
                        onChange={(date: Date | null) => {
                            if (date) onDateChange("date", date);
                        }}
                    />
                </div>
                <div style={{ width: "25%" }}>
                    <InputLabel>{t("Al")}</InputLabel>
                    <DatePicker
                        label={t("Al:")}
                        value={dateRange.endDate}
                        onChange={(date: Date | null) => {
                            if (date) onDateChange("endDate", date);
                        }}
                    />
                </div>
                <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                    <InputLabel id="select-label"></InputLabel>
                    <Select
                        id="user"
                        name="user"
                        value={defaultParams.user}
                        onChange={onChangeInput}
                        labelId="select-label"
                    >
                        <MenuItem key={0} value="-1">
                            {t("Tutti gli utenti")}
                        </MenuItem>
                        {users?.map((user: any, index: number) => {
                            return (
                                <MenuItem key={index} value={user.id}>
                                    {user.nomeutente}
                                </MenuItem>
                            );
                        })}
                    </Select>
                </FormControl>
                <TextField
                    variant="outlined"
                    value={defaultParams.client}
                    placeholder={t("Cerca Cliente...")}
                    name="client"
                    sx={{ width: 1 / 4 }}
                    onChange={onChangeInput}
                />
                <TextField
                    variant="outlined"
                    value={defaultParams.task}
                    placeholder={t("Cerca Attività...")}
                    name="task"
                    sx={{ width: 1 / 4 }}
                    onChange={onChangeInput}
                />

                <FormControl
                    sx={{
                        width: 250,
                    }}
                >
                    <Select
                        id="codeType"
                        name="codeType"
                        value={defaultParams.codeType}
                        onChange={onChangeInput}
                    >
                        <MenuItem key={0} value="-1">
                            {t("Tutti i codici")}
                        </MenuItem>
                        <MenuItem key={0} value="0">
                            {t("Codice pratica")}
                        </MenuItem>
                        <MenuItem key={0} value="1">
                            {t("Codice archivio")}
                        </MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    variant="outlined"
                    value={defaultParams.file_id}
                    placeholder={t("Codice...")}
                    name="file_id"
                    sx={{ width: 1 / 4 }}
                    onChange={onChangeInput}
                />
            </Box>
            <Box display="flex" alignItems="end" gap={2}>
                <FormControl variant="outlined" sx={{ width: 250 }}>
                    <InputLabel id="select-label">{t("Ordina Per")}</InputLabel>
                    <Select
                        id="order"
                        name="order"
                        value={defaultParams.order}
                        onChange={onChangeInput}
                    >
                        <MenuItem key={0} value="data">
                            {t("Ord. Griglia")}
                        </MenuItem>
                        <MenuItem key={0} value="date">
                            {t("Data")}
                        </MenuItem>
                        <MenuItem key={0} value="file">
                            {t("Pratica")}
                        </MenuItem>
                        <MenuItem key={0} value="user">
                            {t("Utente")}
                        </MenuItem>
                    </Select>
                </FormControl>

                <FormControl sx={{ width: 250 }}>
                    <Select
                        id="processed"
                        name="processed"
                        value={defaultParams.processed}
                        onChange={onChangeInput}
                    >
                        <MenuItem key={0} value="-1">
                            {t("Stati Evasa")}
                        </MenuItem>
                        <MenuItem key={0} value="1">
                            {t("Evasa")}
                        </MenuItem>
                        <MenuItem key={0} value="0">
                            {t("Non Evasa")}
                        </MenuItem>
                    </Select>
                </FormControl>
                <FormControl sx={{ width: 250 }}>
                    <Select
                        id="fattura"
                        name="fattura"
                        value={defaultParams.fattura}
                        onChange={onChangeInput}
                    >
                        <MenuItem key={0} value="-1">
                            {t("Stati Fattura")}
                        </MenuItem>
                        <MenuItem key={0} value="1">
                            {t("Fatturata")}
                        </MenuItem>
                        <MenuItem key={0} value="0">
                            {t("Non Fatturata")}
                        </MenuItem>
                    </Select>
                </FormControl>

                <FormControl sx={{ width: 250 }}>
                    <Select
                        id="visible"
                        name="visible"
                        value={defaultParams.visible}
                        onChange={onChangeInput}
                    >
                        <MenuItem key={0} value="-1">
                            {t("Stati Visibilità")}
                        </MenuItem>
                        <MenuItem key={0} value="1">
                            {t("Visibile a tutti")}
                        </MenuItem>
                        <MenuItem key={0} value="0">
                            {t("Non Visibile")}
                        </MenuItem>
                    </Select>
                </FormControl>
            </Box>

            <Box display="flex" alignItems="end" gap={2}>
                <FormControl sx={{ width: 1 / 3 }}>
                    <InputLabel id="select-label">{t("Situazione")}</InputLabel>
                    <Select
                        id="feeType"
                        name="feeType"
                        value={defaultParams.feeType}
                        onChange={onChangeInput}
                    >
                        <MenuItem key={0} value="-1">
                            -
                        </MenuItem>
                        {feeTypes?.map((fee: any, index: number) => {
                            return (
                                <MenuItem key={index} value={fee.id}>
                                    {fee.name}
                                </MenuItem>
                            );
                        })}
                    </Select>
                </FormControl>
                <FormControl
                    sx={{
                        width: 250,
                    }}
                >
                    <Select
                        id="billable"
                        name="billable"
                        value={defaultParams.billable}
                        onChange={onChangeInput}
                    >
                        <MenuItem key={0} value="-1">
                            {t("Tutti")}
                        </MenuItem>
                        <MenuItem key={0} value="1">
                            {t("Non Addebitabile")}
                        </MenuItem>
                        <MenuItem key={0} value="2">
                            {t("Addebitabile")}
                        </MenuItem>
                        <MenuItem key={0} value="3">
                            {t("Addebitabile Non Fatturabile")}
                        </MenuItem>
                        <MenuItem key={0} value="4">
                            {t("Addebitabile Fatturabile")}
                        </MenuItem>
                        <MenuItem key={0} value="5">
                            {t("Da Fatturare")}
                        </MenuItem>
                        <MenuItem key={0} value="6">
                            {t("Fatturate")}
                        </MenuItem>
                    </Select>
                </FormControl>
                <TextField
                    variant="outlined"
                    value={defaultParams.parcella}
                    placeholder={t("N° Fattura")}
                    name="parcella"
                    sx={{ width: 1 / 4 }}
                    onChange={onChangeInput}
                />
                <Box component="form" display="flex" alignItems="end" gap={2}>
                    <Button
                        variant="contained"
                        color="primary"
                        onClick={onSubmit}
                    >
                        {t("Cerca")}
                    </Button>

                    <Button
                        variant="contained"
                        color="primary"
                        onClick={clearAll}
                    >
                        {t("Mostra tutti")}
                    </Button>
                </Box>
            </Box>
            <Box
                component="form"
                display="flex"
                alignItems="start"
                gap={2}
                sx={{ mt: 2 }}
            >
                <Typography variant="body" gutterBottom component="div">
                    <b>{t("N.B. ")}</b>
                    {t(
                        "Se è stata indicata una tariffa oraria nella pratica, questa verrà utilizzata al posto della tariffa oraria personale"
                    )}
                </Typography>
            </Box>
        </Box>
    );
}
