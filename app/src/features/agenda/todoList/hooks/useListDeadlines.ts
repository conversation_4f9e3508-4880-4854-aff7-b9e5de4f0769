import useGetCustom from "../../../../hooks/useGetCustom";
import { getDeadlinesGrid } from "../customGrid/grids";
import { useTranslation } from "@1f/react-sdk";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { IList } from "../../../../interfaces/general.interfaces";
import { CURRENT_DATE_FORMATTED } from "../../generalCalendar/addCalendar/impegno/constants/constant";
import { IDeadlineQuery } from "../interfaces/interfaces";

const INITIAL_QUERY: IDeadlineQuery = {
    page: 0,
    pageSize: 10,
    sortColumn: "data",
    sortOrder: "desc",
    oldDeadlines: "",
    startDate: CURRENT_DATE_FORMATTED(),
    endDate: CURRENT_DATE_FORMATTED(),
    person: "-1",
    deadlinesTypeSearch: "-1",
    deadlinesCategorySearch: "-1",
    object: "",
    UdienzeRitardo: "",
    processed: "-1",
    notProcessed: "0",
    close: true,
    isArchived: false,
    poliswebFilter: false,
    importantOnly: false,
};

export default function useListDeadlines() {
    const { t } = useTranslation();
    const [searchParams] = useSearchParams();
    const listDeadlinesRequest = useGetCustom(
        "deadlines/list?noTemplateVars=true"
    );

    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });

    // Initialize query with URL parameters if available
    const getInitialQuery = (): IDeadlineQuery => {
        const urlOldDeadlines = searchParams.get('oldDeadlines');
        const urlStartDate = searchParams.get('startDate');
        const urlEndDate = searchParams.get('endDate');
        const urlIntestratario = searchParams.get('intestatario');

        return {
            ...INITIAL_QUERY,
            oldDeadlines: urlOldDeadlines || INITIAL_QUERY.oldDeadlines,
            startDate: urlStartDate || INITIAL_QUERY.startDate,
            endDate: urlEndDate || INITIAL_QUERY.endDate,
            person: urlIntestratario || INITIAL_QUERY.person,
            // Set processed to "0" (Non evaso) when oldDeadlines=true
            processed: urlOldDeadlines === 'true' ? "0" : INITIAL_QUERY.processed,
        };
    };

    const [deadlineQuery, setDeadlineQuery] = useState<IDeadlineQuery>(getInitialQuery());

    const cleanQuery = (query: any) =>
        Object.fromEntries(
            Object.entries(query).filter(([_, value]) => value !== false)
        );

    const fetchData = async () => {
        const cleanedQuery = cleanQuery(deadlineQuery);

        const [columns, response]: any = await Promise.all([
            getDeadlinesGrid(t),
            listDeadlinesRequest.doFetch(true, cleanedQuery),
        ]);

        const { currentPage, totalRows } = response.data;

        const formattedColumns = columns.map((col: any) => 
            Array.isArray(col) ? { ...col } : col
        );
        
        setList({
            ...list,
            rows: currentPage,
            columns: formattedColumns,
            totalRows: parseInt(totalRows),
            page: deadlineQuery?.page,
            pageSize: deadlineQuery?.pageSize,
        });
    };

    useEffect(() => {
        fetchData();
    }, [deadlineQuery, t]);

    return {
        deadlineQuery,
        setDeadlineQuery,
        list,
        loading: listDeadlinesRequest.loading,
        INITIAL_QUERY,
        fetchData,
    };
}
