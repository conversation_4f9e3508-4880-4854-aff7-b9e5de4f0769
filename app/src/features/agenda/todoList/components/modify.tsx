import {
    Dialog,
    DialogTitle,
    IconButton,
    Divider,
    DialogContent,
    Box,
    TextField,
    DialogActions,
    Button,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
} from "@vapor/react-material";
import CloseIcon from "@mui/icons-material/Close";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import {
    MINUTES,
    HOURS,
} from "../../generalCalendar/addCalendar/impegno/constants/constant";
import { parseDate } from "../../../../helpers/parseDataFormat";

interface IModalProps {
    open: boolean;
    handleDialog: () => void;
    updateDeadlineParams: any;
    setUpdateDeadlineParams: React.Dispatch<React.SetStateAction<any>>;
    handleUpdateDeadlineObject: (params: any) => void;
    fetchData: () => void;
}

export default function ModifySubjectModal(props: IModalProps) {
    const {
        open,
        handleDialog,
        updateDeadlineParams,
        setUpdateDeadlineParams,
        handleUpdateDeadlineObject,
        fetchData,
    } = props;
    const { t } = useTranslation();

    const onDateChange = (name: string, value: Date) => {
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB");
        setUpdateDeadlineParams((prevValue: any) => ({
            ...prevValue,
            [name]: formattedDate,
        }));
    };

    const handleInputChanges = (event: any) => {
        const { name, value } = event.target;
        setUpdateDeadlineParams({ ...updateDeadlineParams, [name]: value });
    };

    const hanldeUpdateDeadlineConfirm = () => {
        handleUpdateDeadlineObject(updateDeadlineParams);
        fetchData();
        handleDialog();
    };

    return (
        <Dialog
            open={open}
            onClose={handleDialog}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            <DialogTitle>
                {t("Modifica")}
                <IconButton color="primary" onClick={handleDialog}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <Divider variant="fullWidth" />
            <DialogContent>
                <Box
                    autoComplete="off"
                    component="form"
                    noValidate
                    sx={{
                        "& > :not(style)": {
                            mt: 2,
                            mb: 4,
                            ml: 0,
                            width: 410,
                        },
                    }}
                >
                    <TextField
                        label={t("Oggetto")}
                        name="oggetto"
                        value={updateDeadlineParams.oggetto}
                        onChange={handleInputChanges}
                        variant="outlined"
                        placeholder={t("Nuovo oggetto")}
                    />
                    <Box
                        sx={{
                            display: "flex",
                            flexDirection: "row",
                            gap: 2,
                        }}
                    >
                        <DatePicker
                            label={t("Data")}
                            value={parseDate(updateDeadlineParams.data)}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("data", date);
                            }}
                            sx={{
                                "& .MuiTextField-root": {
                                    width: "300px !important",
                                },
                            }}
                        />
                        <FormControl sx={{ width: 80 }}>
                            <InputLabel>{t("Ora")}</InputLabel>
                            <Select
                                name="ora"
                                value={updateDeadlineParams.ora}
                                onChange={handleInputChanges}
                            >
                                {HOURS.map((hour: any) => (
                                    <MenuItem key={hour} value={hour}>
                                        {hour}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                        <FormControl sx={{ width: 80 }}>
                            <InputLabel>{t("Min")}</InputLabel>
                            <Select
                                name="minuti"
                                value={String(
                                    updateDeadlineParams?.minuti
                                ).padStart(2, "0")} // Format to two digits
                                onChange={handleInputChanges}
                            >
                                {MINUTES.map((min: any) => (
                                    <MenuItem key={min} value={min}>
                                        {min}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Box>
                </Box>
            </DialogContent>
            <DialogActions>
                <Button variant="outlined" onClick={handleDialog}>
                    {t("Annulla")}
                </Button>
                <Button
                    variant="contained"
                    onClick={hanldeUpdateDeadlineConfirm}
                    sx={{
                        mr: 1,
                    }}
                >
                    {t("Conferma")}
                </Button>
            </DialogActions>
        </Dialog>
    );
}
