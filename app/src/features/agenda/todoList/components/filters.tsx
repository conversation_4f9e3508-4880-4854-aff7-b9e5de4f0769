import React from "react";
import {
    Box,
    Button,
    TextField,
    Checkbox,
    FormControlLabel,
    Select,
    MenuItem,
    Typography,
    Grid,
    IconButton,
} from "@vapor/react-material";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { PROCESSED_DATA, NOT_PROCESSED_DATA } from "../constants/data";
import { useTranslation } from "@1f/react-sdk";
import { parseDate } from "../../../../helpers/parseDataFormat";
import LegendToggleIcon from "@mui/icons-material/LegendToggle";
import { LegendTooltip } from "../../generalCalendar/helpers/tooltipCustomHelper";
import SquareIcon from "@mui/icons-material/Square";
import PriorityHighIcon from "@mui/icons-material/PriorityHigh";
import { IDeadlineQuery } from "../interfaces/interfaces";

interface IDeadlineData {
    deadlinePeople: any;
    deadlineTypes: any;
    deadlineCategories: any;
    deadlineQuery: IDeadlineQuery;
    setDeadlineQuery: React.Dispatch<React.SetStateAction<IDeadlineQuery>>;
    INITIAL_QUERY: IDeadlineQuery;
}

export default function Filters(props: IDeadlineData) {
    const {
        deadlinePeople,
        deadlineCategories,
        deadlineTypes,
        deadlineQuery,
        setDeadlineQuery,
        INITIAL_QUERY,
    } = props;
    const { t } = useTranslation();

    const onChangeInputs = (event: any) => {
        const { name, value } = event.target;
        setDeadlineQuery({ ...deadlineQuery, [name]: value });
    };

    const handleChangeCheckboxes = (event: any) => {
        const { name, checked } = event.target;
        setDeadlineQuery({ ...deadlineQuery, [name]: checked });
    };

    const onDateChange = (name: string, value: Date) => {
        const date = new Date(value);
        const formattedDate = date.toLocaleDateString("en-GB");
        setDeadlineQuery((prevValue: any) => ({
            ...prevValue,
            [name]: formattedDate,
        }));
    };

    return (
        <Box>
            <Box component="form" display="flex" alignItems="end" gap={2}>
                <DatePicker
                    label="Dal"
                    value={parseDate(deadlineQuery.startDate)}
                    onChange={(date: Date | null) => {
                        if (date) onDateChange("startDate", date);
                    }}
                />
                <DatePicker
                    label="Al"
                    value={parseDate(deadlineQuery.endDate)}
                    onChange={(date: Date | null) => {
                        if (date) onDateChange("endDate", date);
                    }}
                />
                <Select
                    id="person"
                    name="person"
                    sx={{ width: 250 }}
                    value={deadlineQuery.person}
                    onChange={onChangeInputs}
                >
                    <MenuItem key="-1" value="-1">
                        {t("Tutte gli intestarari")}
                    </MenuItem>
                    {(deadlinePeople || [])?.map((people: any, index: any) => (
                        <MenuItem key={index} value={people.id}>
                            {people.nome}
                        </MenuItem>
                    ))}
                </Select>
                <Select
                    id="deadlinesTypeSearch"
                    name="deadlinesTypeSearch"
                    sx={{ width: 250 }}
                    value={deadlineQuery.deadlinesTypeSearch}
                    onChange={onChangeInputs}
                >
                    <MenuItem key="-1" value="-1">
                        {t("Tutte gli tipologie")}
                    </MenuItem>
                    {(deadlineTypes || [])?.map((type: any, index: any) => (
                        <MenuItem key={index} value={type.id}>
                            {type.nome}
                        </MenuItem>
                    ))}
                </Select>
                <Select
                    id="deadlinesCategorySearch"
                    name="deadlinesCategorySearch"
                    sx={{ width: 250 }}
                    value={deadlineQuery.deadlinesCategorySearch}
                    onChange={onChangeInputs}
                >
                    <MenuItem key="-1" value="-1">
                        {t("Tutte gli categorie")}
                    </MenuItem>
                    {(deadlineCategories || [])?.map(
                        (category: any, index: any) => (
                            <MenuItem key={index} value={category.id}>
                                {category.nome}
                            </MenuItem>
                        )
                    )}
                </Select>
                <LegendTooltip
                    title={
                        <React.Fragment>
                            <Grid container spacing={2}>
                                <Grid item xs={12}>
                                    <div
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                            marginBottom: 1,
                                        }}
                                    >
                                        <IconButton
                                            disabled
                                            sx={{
                                                mr: 1,
                                                width: 32,
                                                height: 32,
                                            }}
                                        >
                                            <SquareIcon
                                                sx={{
                                                    color: "#DFF0D8",
                                                    fontSize: "25px !important",
                                                }}
                                            />
                                        </IconButton>
                                        <Typography variant="body2">
                                            {t("Avviso d'impegno")}
                                        </Typography>
                                    </div>
                                    <div
                                        style={{
                                            display: "flex",
                                            alignItems: "center",
                                            marginBottom: 1,
                                        }}
                                    >
                                        <IconButton
                                            disabled
                                            sx={{
                                                mr: 1,
                                                width: 32,
                                                height: 32,
                                            }}
                                        >
                                            <PriorityHighIcon color="error" />
                                        </IconButton>
                                        <Typography variant="body2">
                                            {t("Impegni importanti")}
                                        </Typography>
                                    </div>
                                </Grid>
                            </Grid>
                        </React.Fragment>
                    }
                >
                    <Button
                        variant="contained"
                        endIcon={<LegendToggleIcon />}
                        color="secondary"
                    >
                        {t("Legenda")}
                    </Button>
                </LegendTooltip>
            </Box>

            <Box
                component="form"
                display="flex"
                alignItems="end"
                gap={2}
                sx={{ mt: 2 }}
            >
                <TextField sx={{ width: 250 }} name="object" label="Oggetto" />
                <Select
                    name="processed"
                    sx={{ width: 250 }}
                    value={deadlineQuery.processed}
                    onChange={onChangeInputs}
                >
                    {PROCESSED_DATA.map((data: any, index: number) => (
                        <MenuItem key={index} value={data.value}>
                            {data.name}
                        </MenuItem>
                    ))}
                </Select>
                <Select
                    name="notProcessed"
                    sx={{ width: 250 }}
                    value={deadlineQuery.notProcessed}
                    onChange={onChangeInputs}
                >
                    {NOT_PROCESSED_DATA.map((data: any, index: number) => (
                        <MenuItem key={index} value={data.value}>
                            {data.name}
                        </MenuItem>
                    ))}
                </Select>
                <FormControlLabel
                    value="right"
                    control={
                        <Checkbox
                            name="importantOnly"
                            checked={deadlineQuery.importantOnly}
                            onChange={handleChangeCheckboxes}
                        />
                    }
                    label={t("Importanti")}
                    labelPlacement="end"
                />
            </Box>

            <Box
                component="form"
                display="flex"
                alignItems="end"
                gap={2}
                sx={{ mt: 2 }}
            >
                <Typography
                    variant="bodyLarge500"
                    gutterBottom
                    component="div"
                    sx={{ mb: 1 }}
                >
                    {t("Aggiungi alla lista:")}
                </Typography>
                <FormControlLabel
                    value="right"
                    control={
                        <Checkbox
                            name="close"
                            checked={deadlineQuery.close}
                            onChange={handleChangeCheckboxes}
                        />
                    }
                    label={t("Vicini")}
                    labelPlacement="end"
                />
                <FormControlLabel
                    value="right"
                    control={
                        <Checkbox
                            name="isArchived"
                            checked={deadlineQuery.isArchived}
                            onChange={handleChangeCheckboxes}
                        />
                    }
                    label={t("Anche pratiche archiviate")}
                    labelPlacement="end"
                />
                <FormControlLabel
                    value="right"
                    control={
                        <Checkbox
                            name="poliswebFilter"
                            checked={deadlineQuery.poliswebFilter}
                            onChange={handleChangeCheckboxes}
                        />
                    }
                    label={t("Polisweb")}
                    labelPlacement="end"
                />
                <Button
                    variant="outlined"
                    onClick={() => setDeadlineQuery({ ...INITIAL_QUERY })}
                >
                    {t("Annulla ricerca")}
                </Button>
            </Box>
        </Box>
    );
}
