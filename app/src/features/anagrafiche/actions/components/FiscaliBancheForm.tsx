import { useEffect, useState } from "react";

import { <PERSON><PERSON><PERSON>, Box, Button, FormControl, InputLabel, Select, MenuItem, TextareaAutosize, TextField, Tooltip } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { ExtendedInputAdornment } from "@vapor/react-extended";
import {
    faIdCard,
    faCirclePlus,
    faTrashCan,
} from "@fortawesome/pro-regular-svg-icons";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { UseFormReturn } from "react-hook-form";
import ContentHeader from "../../components/ContentHeader";
import useSearchOffices from "../../hooks/useSearchOffices";
import { CustomSelect } from "../../../../custom-components/CustomSelect";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";
import ReportProblemIcon from '@mui/icons-material/ReportProblem';

interface IFisacliBanche {
    anagraficheData: any;
    method: UseFormReturn;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: any;
    onSubmitUpdate?: (data: any) => void;
    id?: any;
}

export default function FisacliBancheForm(props: IFisacliBanche) {


    const { anagraficheData, method, isUpdate = false, showCreateForm, setShowCreateForm, onSubmitUpdate }: any = props;
    const { control, watch, register, handleSubmit, setValue, formState: { errors } } = method;
    const [isPaymentAdded, setIsPaymentAdded] = useState<boolean>(false);
    const [isPresentationAdded, setIsPresentationAdded] = useState<boolean>(false);
    const [ibanError, setIbanError] = useState<string>('');
    const { t } = useTranslation();
    const values: any = watch();


    const [open, setOpen] = useState(false);

    const { tipiRitenuta, nature, modalitaPagamento, listini, banche } =
        anagraficheData;
    const {
        handleOfficeSearch,
        officeResult,
        officesLoading,
        officeNameInput,
        setOfficeNameInput,
        increment,
    } = useSearchOffices();

    useEffect(() => {
        const { note_pagamento, banca, iban, modalita_pagamento } = values;
        if (
            note_pagamento !== "" ||
            banca !== "0" ||
            iban !== "" ||
            modalita_pagamento !== "MP01"
        ) {
            setIsPaymentAdded(true);
        }
    }, [values.note, values.banca, values.iban, values.modalita_pagamento]);

    useEffect(() => {
        const { listino, listino_orario, tariffa_oraria } = values;
        if (
            listino !== "0" ||
            listino_orario !== "0" ||
            tariffa_oraria !== ""
        ) {
            setIsPresentationAdded(true);
        }
    }, [values.listino, values.listino_orario, values.tariffa_oraria]);

    const handleRemovePayment = () => {
        setValue("note_pagamento", "");
        setValue("banca", "0");
        setValue("iban", "");
        setValue("modalita_pagamento", "MP01");
        setIsPaymentAdded(false);
    };

    const handleRemovePresentation = () => {
        setValue("listino", "0");
        setValue("listino_orario", "0");
        setValue("tariffa_oraria", "");
        setIsPresentationAdded(false);
    };

    const handleCheckboxesValue = (event: any) => {
        const { name, checked } = event.target;
        setValue(name, checked ? "1" : null);
    };

    const handleSaveChanges = (event: any) => {
        event.preventDefault();
        handleSubmit(onSubmitUpdate)();
        setShowCreateForm({
            ...showCreateForm,
            fiscaliBanche: false,
        });
    };

    const validateIban = (value: string) => {
        if (!value) {
            setIbanError('');
            return true;
        }
        
        const cleanIban = value.replace(/\s/g, '').toUpperCase();
        
        const firstTwoLetters = /^[A-Z]{2}/.test(cleanIban);
        
        const nextTwoNumbers = /^[A-Z]{2}[0-9]{2}/.test(cleanIban);
        
       
        const validLength = cleanIban.length >= 15 && cleanIban.length <= 34;
        
       
        const remainingAlphanumeric = /^[A-Z]{2}[0-9]{2}[A-Z0-9]+$/.test(cleanIban);
        
        if (!firstTwoLetters || !nextTwoNumbers || !validLength || !remainingAlphanumeric) {
            setIbanError('Il Codice IBAN inserito potrebbe non essere corretto.');
            return false;
        }
        
        setIbanError('');
        return true;
    };

    useEffect(() => {
        if (values.iban) {
            validateIban(values.iban);
        }
    }, [values.iban]);

    return (
        <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                ml: 1,
            }}
        >
            <Box display="flex">
                <ContentHeader icon={faIdCard} title={t("Fiscali e banche")} />
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
                <FormInput
                    sx={{ width: 400 }}
                    control={control}
                    value={values.codiceb2b}
                    label={t("Codice destinatario")}
                    name={"codiceb2b"} 
                />
                <CustomAutocomplete
                    onOpen={() => setOpen(true)}
                    onClose={() => setOpen(false)}
                    componentsProps={{
                        popupIndicator: {
                            title: open ? t("Chiudi") : t("Apri"),
                        },
                    }}
                    selectOnFocus
                    clearOnBlur
                    sx={{ width: 400 }}
                    options={officeResult}
                    getOptionLabel={(option: any) => {
                        if (!option || typeof option === "string")
                            return option || "";
                        return `${option.denominazione} (${option.codice_pa})`;
                    }}
                    inputValue={officeNameInput}
                    value={null}
                    onInputChange={(_e: any, value: any) => {
                        handleOfficeSearch(value, 0);
                        setOfficeNameInput(value);
                    }}
                    onChange={(_e: any, option: any) => {
                        if (option) {
                            setValue("codiceb2b", option.codice_pa);
                            setOfficeNameInput("");
                        }
                    }}
                    loading={officesLoading}
                    renderInput={(params: any) => (
                        <TextField
                            {...params}
                            label={t("Cerca codice destinatario fra le PA")}
                            variant="outlined"
                        />
                    )}
                    renderOption={(props: any, option: any) => {
                        if (typeof option.increment === "number") {
                            return (
                                <li
                                    {...props}
                                    key="show-more"
                                    style={{
                                        display: "flex",
                                        justifyContent: "center",
                                    }}
                                >
                                    <Button
                                        size="small"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleOfficeSearch(
                                                officeNameInput,
                                                increment + 1
                                            );
                                        }}
                                    >
                                        {t("Mostra Altri")}
                                    </Button>
                                </li>
                            );
                        }

                        return (
                            <li {...props} key={option.id}>
                                {`${option.denominazione} (${option.codice_pa})`}
                            </li>
                        );
                    }}
                />
            </Box>

            <Typography variant="subtitle" color="primary.textTitleColor">
                {t("Dati fiscali")}
            </Typography>

            <FormInput
                control={control}
                name="scissione_pagamenti"
                value={values.scissione_pagamenti}
                type="checkbox"
                options={[
                    {
                        label: t('Soggetto in regime di "scissione pagamenti" (D.L. n. 50 del 24 aprile 2017)'),
                        value: "1"
                    }
                ]}
            />
            <FormInput
                sx={{ width: 400 }}
                control={control}
                label={t("Spese generali")}
                name="spese_generali"
                value={values.spese_generali}
                type="number"
                placeholder="0.00"
                InputProps={{
                    endAdornment: (
                        <ExtendedInputAdornment position="end" adornmentBg>
                            %
                        </ExtendedInputAdornment>
                    ),
                }}
                inputProps={{
                    min: 0,
                    step: "0.01", // Allows decimal input
                }}
                onInput={(e: any) => {
                    const inputValue = e.target.value;
                    if (inputValue > 100) {
                        e.target.value = 100; // Limit the value to 100
                    }
                }}
            />
            <FormInput
                sx={{ width: 400 }}
                control={control}
                label={t("Rit. acconto")}
                name="perc_ritenuta"
                value={values.perc_ritenuta}
                placeholder="0.00"
                type="number"
                InputProps={{
                    endAdornment: (
                        <ExtendedInputAdornment position="end" adornmentBg>
                            %
                        </ExtendedInputAdornment>
                    ),
                }}
                inputProps={{
                    min: 0,
                    step: "0.01", // Allows decimal input
                }}
                onInput={(e: any) => {
                    const inputValue = e.target.value;
                    if (inputValue > 100) {
                        e.target.value = 100; // Limit the value to 100
                    }
                }}
            />
            <FormInput
                control={control}
                name="rit_acconto"
                value={values.rit_acconto}
                onChange={handleCheckboxesValue}
                sx={{ ml: 1 }}
                type="checkbox"
                options={[
                    {
                        label: t("Tipologia ritenuta"),
                        value: "1",
                    },
                ]}
            />
            {values.rit_acconto === "1" && (
                <FormControl sx={{ width: 400 }}>
                    <Select
                        sx={{ ml: 1 }}
                        value={values.tipo_ritenuta}
                        {...register("tipo_ritenuta")}
                    >
                        <MenuItem disabled value="-1">
                            {t("Tipologia ritenuta")}
                        </MenuItem>
                        {tipiRitenuta?.map((tipi: any, index: number) => (
                            <MenuItem key={index} value={tipi.id}>
                                {`${tipi.codice} - ${tipi.nome}`}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            )}
            <FormInput
                control={control}
                name="esente_cassa"
                value={values.esente_cassa}
                type="checkbox"
                sx={{ ml: 1 }}
                onChange={handleCheckboxesValue}
                options={[
                    {
                        label: t("Esenzione cassa"),
                        value: "1",
                    },
                ]}
            />
            <FormInput
                control={control}
                name="orefatturabili"
                sx={{ ml: 1 }}
                value={values.orefatturabili}
                type="checkbox"
                onChange={handleCheckboxesValue}
                options={[
                    {
                        label: t("Ore fatturabili"),
                        value: "1",
                    },
                ]}
            />

            <FormInput
                control={control}
                name="spesefatturabili"
                sx={{ ml: 1 }}
                value={values.spesefatturabili}
                type="checkbox"
                onChange={handleCheckboxesValue}
                options={[
                    {
                        label: t("Spese fatturabili"),
                        value: "1",
                    },
                ]}
            />

            <FormInput
                control={control}
                name="iva_esente"
                sx={{ ml: 1 }}
                value={values.iva_esente}
                type="checkbox"
                onChange={handleCheckboxesValue}
                options={[
                    {
                        label: t("Soggetto esente da Iva"),
                        value: "1",
                    },
                ]}
            />

            {values.iva_esente === "1" && (
                <FormControl sx={{ width: 400 }}>
                    <Select
                        sx={{ ml: 1 }}
                        value={values.tipo_esenzione}
                        {...register("tipo_esenzione")}
                    >
                        <MenuItem disabled value="1">
                            {t("Tipologia esenzione")}
                        </MenuItem>
                        {nature?.map((nature: any, index: number) => (
                            <MenuItem key={index} value={nature.codice}>
                                {`${nature.codice} - ${nature.nome}`}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            )}

            <Box sx={{ marginBottom: 2, marginTop: 2 }}>
                <Box display="flex" sx={{ width: 815 }}>
                    <Typography variant="titleSmall" component="div">
                        {t("Modalità di pagamento")}
                    </Typography>
                    {isPaymentAdded && (
                        <Button
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "error",
                                    }}
                                    icon={faTrashCan}
                                />
                            }
                            sx={{ marginLeft: "auto" }}
                            color="error"
                            onClick={() => handleRemovePayment()}
                        >
                            {t("Elimina")}
                        </Button>
                    )}
                </Box>
                {!isPaymentAdded && (
                    <Button
                        startIcon={
                            <FontAwesomeIcon
                                style={{
                                    color: "#008FD6",
                                }}
                                icon={faCirclePlus}
                            />
                        }
                        sx={{ marginTop: 1 }}
                        onClick={() => setIsPaymentAdded(true)}
                    >
                        {t("Aggiungi modalità di pagamento")}
                    </Button>
                )}
                {/* {isPaymentAdded && (<PaymentType />)} */}
                {isPaymentAdded && (
                    <Box sx={{ marginY: "20px" }}>
                        <FormControl sx={{ width: 815, marginBottom: "15px" }}>
                            <CustomSelect
                                value={values.modalita_pagamento}
                                {...register("modalita_pagamento")}
                            >
                                {modalitaPagamento?.map(
                                    (modalita: any, index: number) => (
                                        <MenuItem
                                            key={index}
                                            value={modalita.codice}
                                        >
                                            {`${modalita.codice} - ${modalita.nome}`}
                                        </MenuItem>
                                    )
                                )}
                            </CustomSelect>
                        </FormControl>
                        <Box sx={{ width: 815 }}>
                            <FormControl>
                                <InputLabel>{t("Note")}</InputLabel>
                                <TextareaAutosize
                                    spellCheck="false"
                                    minRows={5}
                                    name="note_pagamento"
                                    value={values.note_pagamento}
                                    {...register("note_pagamento")}
                                    style={{
                                        width: 815,
                                        resize: "none",
                                    }}
                                />
                            </FormControl>
                            <Typography variant="body500" gutterBottom sx={{ color: "#566B76" }}>
                                {t("N.B. Le note di pagamento verranno mostrate solo nella stampa della fattura.")}
                            </Typography>
                        </Box>

                        <Box
                            sx={{
                                display: "flex",
                                gap: 2,
                                marginTop: "20px",
                            }}
                        >
                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                <TextField
                                    label={t("Iban")}
                                    sx={{ 
                                        width: ibanError ? 362 : 400,
                                        ...(ibanError && {
                                            '& .MuiOutlinedInput-root': {
                                                '& fieldset': {
                                                    borderColor: 'warning.main',
                                                    borderWidth: '2px',
                                                },
                                                '&:hover fieldset': {
                                                    borderColor: 'warning.main',
                                                },
                                                '&.Mui-focused fieldset': {
                                                    borderColor: 'warning.main',
                                                },
                                            },
                                        })
                                    }}
                                    {...register("iban", {
                                        validate: validateIban
                                    })}
                                    value={values.iban}
                                    error={errors.iban ? true : false}
                                    helperText={errors.iban?.message}
                                    onChange={(e) => {
                                        setValue("iban", e.target.value);
                                        validateIban(e.target.value);
                                    }}
                                />
                                {ibanError && (
                                    <Tooltip
                                        title='Attenzione!'
                                        arrow
                                        placement="right"
                                        description={ibanError}
                                    >
                                        <ReportProblemIcon 
                                            sx={{
                                                color: 'warning.main',
                                                cursor: "pointer",
                                                marginTop: 3,
                                            }}
                                        />
                                    </Tooltip>
                                )}
                            </Box>
                            <FormControl sx={{ width: 400 }}>
                                <InputLabel>{t("Banca")}</InputLabel>
                                <Select
                                    {...register("banca")}
                                    value={values.banca}
                                >
                                    {banche?.map((bank: any, index: number) => (
                                        <MenuItem key={index} value={bank.id}>
                                            {bank.nome}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>
                    </Box>
                )}
            </Box>
            <Box sx={{ marginBottom: 2 }}>
                <Box display="flex" sx={{ width: 815 }}>
                    <Typography variant="titleSmall" component="div">
                        {t("Prestazioni")}
                    </Typography>
                    {isPresentationAdded && (
                        <Button
                            color="error"
                            startIcon={
                                <FontAwesomeIcon
                                    style={{
                                        color: "error",
                                    }}
                                    icon={faTrashCan}
                                />
                            }
                            sx={{ marginLeft: "auto" }}
                            onClick={() => handleRemovePresentation()}
                        >
                            {t("Elimina")}
                        </Button>
                    )}
                </Box>
                {!isPresentationAdded && (
                    <Button
                        startIcon={
                            <FontAwesomeIcon
                                style={{
                                    color: "#008FD6",
                                }}
                                icon={faCirclePlus}
                            />
                        }
                        sx={{ marginTop: 1 }}
                        onClick={() => setIsPresentationAdded(true)}
                    >
                        {t("Aggiungi prestazione")}
                    </Button>
                )}

                {/* {isPresentationAdded && <Presentation />} */}
                {isPresentationAdded && (
                    <Box sx={{ marginY: "20px" }}>
                        <Box
                            sx={{
                                display: "flex",
                                gap: 2,
                                marginBottom: "20px",
                            }}
                        >
                            <FormControl sx={{ width: 400 }}>
                                <InputLabel>
                                    {t("Listino prestazioni")}
                                </InputLabel>
                                <Select
                                    value={values.listino}
                                    {...register("listino")}
                                >
                                    <MenuItem disabled value="0">
                                        {t("Listino prestazioni")}
                                    </MenuItem>
                                    {listini?.map(
                                        (list: any, index: number) =>
                                            list.orario === "0" && (
                                                <MenuItem
                                                    key={index}
                                                    value={list.id}
                                                >
                                                    {list.descrizione}
                                                </MenuItem>
                                            )
                                    )}
                                </Select>
                            </FormControl>

                            <FormControl sx={{ width: 400 }}>
                                <InputLabel>{t("Listino orari")}</InputLabel>
                                <Select
                                    {...register("listino_orario")}
                                    value={values.listino_orario}
                                >
                                    <MenuItem disabled value="0">
                                        {t("Nessuno")}
                                    </MenuItem>
                                    {listini?.map(
                                        (list: any, index: number) =>
                                            list.orario === "1" && (
                                                <MenuItem
                                                    key={index}
                                                    value={list.id}
                                                >
                                                    {list.descrizione}
                                                </MenuItem>
                                            )
                                    )}
                                </Select>
                            </FormControl>
                        </Box>
                        <FormInput
                            sx={{ width: 400 }}
                            control={control}
                            label={t("Tariffa oraria")}
                            name="tariffa_oraria"
                            value={values.tariffa_oraria}
                            type="number"
                            placeholder="0,00"
                            InputProps={{
                                endAdornment: (
                                    <ExtendedInputAdornment
                                        position="end"
                                        adornmentBg
                                    >
                                        EUR
                                    </ExtendedInputAdornment>
                                ),
                            }}
                        />
                    </Box>
                )}
            </Box>
            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815,
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                fiscaliBanche: false,
                            })
                        }
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button
                        type="submit"
                        onClick={(event: any) => handleSaveChanges(event)}
                        variant="contained"
                    >
                        {t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}
