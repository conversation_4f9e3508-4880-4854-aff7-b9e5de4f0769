import { SetStateAction, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, Box, Button, FormControl, TextField, RadioGroup, Radio, FormControlLabel, Switch, Checkbox } from "@vapor/react-material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faIdCard, faCirclePlus, faTrashCan } from "@fortawesome/pro-regular-svg-icons";
import useAnagraficheRelationships from "../../hooks/useAnagraficheRelationships";
import { ExtendedInputAdornment } from "@vapor/react-extended";
import TextInputWithClearButton from "../../../../custom-components/TextInputWithClearButton";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import usePostCustom from "../../../../hooks/usePostCustom";
import moment from "moment";
import ContentHeader from "../../components/ContentHeader";
import { createFilterOptions } from "@mui/material/Autocomplete";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

interface IContatti {
    anagraficheData: any;
    t: any;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: any;
    contactData?: any;
    setReadContactData?: SetStateAction<any>;
    setAngraficheData?: SetStateAction<any>;
    id?: any;
}

enum Type {
    UTENTE = "1",
    ANAGRAFICA = "0"
}

export default function ContattiCollegatiForm(props: IContatti) {
    const { t, showCreateForm, setShowCreateForm, anagraficheData, contactData, setReadContactData, setAngraficheData }: IContatti = props;

    const filter = createFilterOptions<any>();

    const storeRelationshipRequest = usePostCustom("anagraficherelationships/store?noTemplateVars=true");

    const saveRelationContact = usePostCustom("peoplerelationshipsdescriptions/remotesave?noTemplateVars=true");

    const { handleAnagraficheSearch, handleUsersSearch, searchAnagraficheResult, searchUsersResult, isUpdate, usersListLoading, anagraficheListLoading } = useAnagraficheRelationships();

    const [datiSocietari, setDatiSocietari] = useState<Record<number, boolean>>({});
    const [idsToDelete, setIdsToDelete] = useState<string[]>([]);
    const [initialized, setInitialized] = useState(false);
    const [utenteAutoOpen, setUtenteAutoOpen] = useState(false);

    const { relationshipsdescriptions, person } = anagraficheData;

    const [relationsContatti, setRelationsContatti] = useState<any[]>(relationshipsdescriptions || []);

    useEffect(() => {
        if (!initialized && contactData.length > 0) {
            setDatiSocietari(
                contactData.reduce((acc: any, contact: any, index: number) => {
                    acc[index] = !!(contact.dal || contact.al) || contact.socio === "1" || contact.amministratore === "1";
                    return acc;
                }, {})
            );
            setInitialized(true);
        }
    }, [contactData, initialized]);

    const handleDatiSocietariChange = (e: any, index: number) => {
        const isChecked = e.target.checked;

        setDatiSocietari((prevState: any) => ({
            ...prevState,
            [index]: isChecked
        }));

        if (!isChecked) {
            setReadContactData((prev: any) =>
                prev.map((contact: any, i: number) =>
                    i === index
                        ? {
                              ...contact,
                              dal: null,
                              al: null,
                              socio: false,
                              amministratore: false,
                              percentuale_partecipazione: null
                          }
                        : contact
                )
            );
        }
    };

    const handleAddContacts = () => {
        setReadContactData((prev: any) => [
            ...prev,
            {
                description_id: "",
                child: "",
                child_id: "",
                child_uniqueid: "",
                type: Type.UTENTE, // default to "utente"
                parent_id: person?.id,
                socio: false,
                amministratore: false,
                dal: null,
                al: null,
                percentuale_partecipazione: null
            }
        ]);
    };

    const handleRemoveContact = (indexToRemove: number) => {
        const removedContact = contactData[indexToRemove];
        const newContacts = contactData.filter((_: any, i: number) => i !== indexToRemove);
        setReadContactData(newContacts);
        setIdsToDelete((prevIds) => [...prevIds, removedContact.id]);
    };

    const handleChange = (index: number, event: any) => {
        const { name, value } = event.target;
        setReadContactData((prevContactData: any[]) => prevContactData.map((contact, i) => (i === index ? { ...contact, [name]: value } : contact)));
    };

    const handleTypeChange = (index: number, newType: string) => {
        setReadContactData((prev: any) =>
            prev.map((contact: any, i: number) =>
                i === index
                    ? {
                          ...contact,
                          type: newType, // Update selected type
                          child_uniqueid: "",
                          child: "",
                          child_id: ""
                      }
                    : contact
            )
        );
    };

    const updateContactData = (index: number, updates: Partial<any>) => {
        setReadContactData((prev: any) => prev.map((contact: any, i: number) => (i === index ? { ...contact, ...updates } : contact)));
    };

    const handleAutocompleteChanges = (_: any, newValue: any, index: number, type: string) => {
        const commonFields = {
            child_uniqueid: newValue?.uniqueid || "",
            child_id: newValue?.id || ""
        };
        const specificField = type === Type.UTENTE ? { child: newValue?.nomeutente || "" } : { child: newValue?.denominazione || "" };
        updateContactData(index, { ...commonFields, ...specificField });
    };

    const handleInputChange = (_e: any, newInputValue: any, index: number, type: string) => {
        if (type === Type.UTENTE) {
            handleUsersSearch(newInputValue);
        } else {
            handleAnagraficheSearch(newInputValue);
        }

        if (!newInputValue) {
            updateContactData(index, {
                child_uniqueid: "",
                child: "",
                child_id: ""
            });
        }
    };

    const setSelectedValue = (newValue: any, index: number, type: string) => {
        const commonFields = {
            child_uniqueid: newValue?.uniqueid || "",
            child_id: newValue?.id
        };

        const specificField = type === Type.UTENTE ? { child: newValue?.nomeutente || "" } : { child: newValue?.denominazione || "" };
        updateContactData(index, { ...commonFields, ...specificField });
    };

    const filterOptionsFunction = (options: any, params: any) => {
        const filtered = filter(options, params);
        const { inputValue } = params;
        const isExisting = options.some((option: any) => inputValue === option.nome);
        if (inputValue !== "" && !isExisting) {
            filtered.push({
                nome: `Aggiungi "${inputValue}"`,
                inputValue: inputValue // Keep the raw input value
            });
        }
        return filtered;
    };

    const getAutocompleteValue = (contact: any) => {
        return contact?.child_uniqueid
            ? {
                  uniqueid: contact.child_uniqueid,
                  nomeutente: contact.type === Type.UTENTE ? contact.child : undefined,
                  denominazione: contact.type === Type.ANAGRAFICA ? contact.child : undefined
              }
            : null;
    };

    const getSelectedValue = (contact: any, type: string) => {
        if (!contact?.child_uniqueid) return null;

        return {
            id: contact.child_uniqueid,
            nome: type === Type.UTENTE ? contact.child : contact.child // Both share the same field here
        };
    };

    const handleContactChange = (index: number, name: string, value: any) => {
        setReadContactData((prev: any) => prev.map((contact: any, i: number) => (i === index ? { ...contact, [name]: value } : contact)));
    };

    const onDateChange = (name: string, value: Date | null, index: number) => {
        if (value !== null && (!(value instanceof Date) || isNaN(value.getTime()))) return;
        const formattedDate = value ? moment(value).format("YYYY-MM-DD") : null;
        setReadContactData((prev: any) => prev.map((contact: any, i: number) => (i === index ? { ...contact, [name]: formattedDate } : contact)));
    };

    const handleSaveRelationships = async (contactData: any, idsToDelete: string[]): Promise<boolean> => {
        const payload = {
            idsToDelete,
            records: contactData
        };

        const response: any = await storeRelationshipRequest.doFetch(true, payload, "post", "json", true);
        const { success } = response.data;
        return success;
    };

    const handleSaveChanges = async (event: any, contactData: any, idsToDelete: string[]) => {
        event.preventDefault();
        await handleSaveRelationships(contactData, idsToDelete);
        setShowCreateForm({
            ...showCreateForm,
            contactiCollegati: false
        });
    };
    return (
        <Box
            component="form"
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                 ml: 1
            }}
        >
            {/* Header */}
            <Box display="flex" sx={{ marginBottom: 2 }}>
                <ContentHeader icon={faIdCard} title={t("Contatti Collegati")} />
            </Box>

            {contactData.length === 0
                ? null
                : contactData?.map((contact: any, index: number) => {
                      return (
                          <div key={index}>
                              <Box display="flex" marginBottom={2} width={815}>
                                  <Typography variant="subtitle" color="primary.textTitleColor" component="div">
                                      {t(`Contatto collegato ${index + 1}`)}
                                  </Typography>
                                  <Button color="error" startIcon={<FontAwesomeIcon style={{ color: "error" }} icon={faTrashCan} />} sx={{ marginLeft: "auto" }} onClick={() => handleRemoveContact(index)}>
                                      {t("Elimina")}
                                  </Button>
                              </Box>

                              <Typography variant="body500" component="div" sx={{ mb: 2 }}>
                                  {t("Dati generali")}
                              </Typography>

                              <FormControl fullWidth>
                                  <RadioGroup row defaultValue={Type.UTENTE} sx={{ mb: 2 }} value={contactData[index].type} onChange={(e) => handleTypeChange(index, e.target.value)}>
                                      <FormControlLabel control={<Radio value="1" />} label={t("Utente")} />
                                      <FormControlLabel control={<Radio value="0" />} label={t("Anagrafica")} />
                                  </RadioGroup>

                                  {/* Dropdowns */}
                                  <Box
                                      sx={{
                                          display: "flex",
                                          gap: 2,
                                          mb: 2,
                                          width: 815
                                      }}
                                  >
                                      <CustomAutocomplete
                                          options={relationsContatti}
                                          filterOptions={filterOptionsFunction}
                                          getOptionLabel={(option: any) => {
                                              if (typeof option === "string") {
                                                  return option;
                                              }

                                              return option.nome || "";
                                          }}
                                          value={relationsContatti.find((r) => r.id === contact.description_id) || null}
                                          onChange={(_event: any, newValue: any) => {
                                              if (newValue.nome.startsWith(t('Aggiungi "'))) {
                                                  const newInputText = newValue.inputValue;
                                                  saveRelationContact
                                                      .doFetch(true, {
                                                          insertItem: newInputText
                                                      })
                                                      .then((response: any) => {
                                                          const newRelation = response.data;
                                                          setRelationsContatti([
                                                              ...relationsContatti,
                                                              {
                                                                  ...newRelation,
                                                                  nome: newInputText
                                                              }
                                                          ]);
                                                          handleContactChange(index, "description_id", newRelation.id);
                                                          setAngraficheData((prev: any) => {
                                                              return {
                                                                  ...prev,
                                                                  relationshipsdescriptions: [
                                                                      ...prev.relationshipsdescriptions,
                                                                      {
                                                                          ...newRelation,
                                                                          nome: newInputText
                                                                      }
                                                                  ]
                                                              };
                                                          });
                                                      })
                                                      .catch((error: any) => {
                                                          console.error(error);
                                                      });

                                                  return;
                                              }
                                              const syntheticEvent = {
                                                  target: {
                                                      name: "description_id",
                                                      value: newValue ? newValue.id : ""
                                                  }
                                              };
                                              handleChange(index, syntheticEvent);
                                          }}
                                          renderInput={(params: any) => <TextField {...params} placeholder={t("Relazione")} />}
                                          sx={{ width: 815 / 2 }}
                                          isOptionEqualToValue={(option: any, value: any) => option.id === value.id}
                                      />

                                      {contactData[index].type === Type.UTENTE && (
                                          <CustomAutocomplete
                                              noOptionsText={t("Nessuna opzione")}
                                              loadingText={t("Caricamento…")}
                                              sx={{ width: 815 / 2 }}
                                              options={[
                                                  ...contactData
                                                      .map((item: any) => ({
                                                          uniqueid: item.child_uniqueid,
                                                          nomeutente: item.child,
                                                          type: item.type,
                                                          id: item.child_id
                                                      }))
                                                      .filter((item: any) => item.uniqueid && item.type === Type.UTENTE), // Only "Utente"
                                                  ...searchUsersResult
                                              ]
                                                  .filter((value) => value && Object.keys(value).length > 0)
                                                  .filter(
                                                      (value, index, self) => value && self.findIndex((v) => v.uniqueid === value.uniqueid) === index // Remove duplicates
                                                  )}
                                              loading={usersListLoading}
                                              open={utenteAutoOpen}
                                              onOpen={() => setUtenteAutoOpen(true)}
                                              onClose={() => setUtenteAutoOpen(false)}
                                              componentsProps={{
                                                  popupIndicator: {
                                                      title: utenteAutoOpen ? t("Chiudi") : t("Apri")
                                                  }
                                              }}
                                              onChange={(e: any, newValue: any) => {
                                                  handleAutocompleteChanges(e, newValue, index, Type.UTENTE);
                                              }}
                                              onInputChange={(e: any, newInputValue: any) => {
                                                  handleInputChange(e, newInputValue, index, Type.UTENTE);
                                              }}
                                              selectOnFocus
                                              clearOnBlur
                                              isOptionEqualToValue={(option: any, value: any) => option.uniqueid === value.uniqueid}
                                              getOptionLabel={(option: any) => {
                                                  return option.nomeutente;
                                              }}
                                              renderOption={(props: any, option: any) => {
                                                  return <li {...props}>{option.nomeutente}</li>;
                                              }}
                                              value={getAutocompleteValue(contactData[index])}
                                              renderInput={(params: any) => <TextInputWithClearButton params={params} selectedValue={getSelectedValue(contactData[index], Type.UTENTE)} setSelectedValue={(newValue) => setSelectedValue(newValue, index, Type.UTENTE)} disableInput={!!contactData[index]?.child_uniqueid} placeholder={t("Cerca utente per nome, cognome o nome utente")} />}
                                          />
                                      )}

                                      {contactData[index].type === Type.ANAGRAFICA && (
                                          <CustomAutocomplete
                                              sx={{ width: 815 / 2 }}
                                              options={[
                                                  ...contactData
                                                      .map((item: any) => ({
                                                          uniqueid: item.child_uniqueid,
                                                          denominazione: item.child,
                                                          type: item.type
                                                      }))
                                                      .filter((item: any) => item.uniqueid && item.type === Type.ANAGRAFICA), // Only "Utente"
                                                  ...searchAnagraficheResult
                                              ]
                                                  .filter((value) => value && Object.keys(value).length > 0) // Remove empty objects
                                                  .filter(
                                                      (value, index, self) => value && self.findIndex((v) => v.uniqueid === value.uniqueid) === index // Remove duplicates
                                                  )}
                                              loading={anagraficheListLoading}
                                              isOptionEqualToValue={(option: any, value: any) => option.uniqueid === value.uniqueid}
                                              onChange={(e: any, newValue: any) => {
                                                  handleAutocompleteChanges(e, newValue, index, Type.ANAGRAFICA);
                                              }}
                                              onInputChange={(e: any, newInputValue: any) => {
                                                  handleInputChange(e, newInputValue, index, Type.ANAGRAFICA);
                                              }}
                                              value={getAutocompleteValue(contactData[index])}
                                              getOptionLabel={(option: any) => option?.denominazione || ""}
                                              renderOption={(props: any, option: any) => <li {...props}>{option.codicefiscale ? `${option.denominazione} (CF: ${option.codicefiscale})` : option.denominazione}</li>}
                                              renderInput={(params: any) => (
                                                  <TextInputWithClearButton
                                                      params={params}
                                                      selectedValue={getSelectedValue(contactData[index], Type.ANAGRAFICA)}
                                                      setSelectedValue={(newValue) => setSelectedValue(newValue, index, Type.ANAGRAFICA)}
                                                      disableInput={!!contactData[index]?.child_uniqueid} // Disable input if value is selected
                                                      placeholder={t("Cerca anagrafica per denominazione, CF o P.IVA")}
                                                  />
                                              )}
                                          />
                                      )}
                                  </Box>

                                  <Box
                                      sx={{
                                          display: "flex",
                                          alignItems: "center",
                                          mb: 2,
                                          ml: -1.5
                                      }}
                                  >
                                      <Switch
                                          checked={!!datiSocietari[index]} // Ensure it handles undefined as false
                                          onChange={(e: any) => handleDatiSocietariChange(e, index)}
                                      />
                                      <Typography variant="body">{t("Dati societari")}</Typography>
                                  </Box>

                                  {datiSocietari[index] && (
                                      <>
                                          <Box
                                              sx={{
                                                  display: "flex",
                                                  gap: 1,
                                                  mb: 3
                                              }}
                                          >
                                              <FormControlLabel control={<Checkbox name="socio" checked={contactData[index].socio} onChange={(e) => handleContactChange(index, e.target.name, e.target.checked)} />} label={t("Socio")} />
                                              <FormControlLabel control={<Checkbox name="amministratore" checked={contactData[index].amministratore} onChange={(e) => handleContactChange(index, e.target.name, e.target.checked)} />} label={t("Amministratore")} />
                                          </Box>

                                          <Box
                                              sx={{
                                                  display: "flex",
                                                  gap: 2,
                                                  mb: 3
                                              }}
                                          >
                                              <DatePicker
                                                  label=""
                                                  value={contactData[index].dal ? new Date(contactData[index].dal) : null}
                                                  onChange={(date: Date | null) => onDateChange("dal", date, index)}
                                                  sx={{
                                                      width: 192
                                                  }}
                                              />
                                              <DatePicker
                                                  label=""
                                                  value={contactData[index].al ? new Date(contactData[index].al) : null}
                                                  onChange={(date: Date | null) => onDateChange("al", date, index)}
                                                  sx={{
                                                      width: 192
                                                  }}
                                              />
                                              <TextField
                                                  type="number"
                                                  placeholder="Percentuale partecipazione"
                                                  name="percentuale_partecipazione"
                                                  value={contactData[index].percentuale_partecipazione}
                                                  onChange={(e) => handleContactChange(index, e.target.name, e.target.value)}
                                                  InputProps={{
                                                      inputProps: {
                                                          min: 0,
                                                          max: 100
                                                      },
                                                      endAdornment: (
                                                          <ExtendedInputAdornment position="end" adornmentBg>
                                                              %
                                                          </ExtendedInputAdornment>
                                                      )
                                                  }}
                                                  sx={{ width: 405 }}
                                              />
                                          </Box>
                                      </>
                                  )}
                              </FormControl>
                          </div>
                      );
                  })}
            <Box sx={{ marginBottom: 2 }}>
                <Button
                    startIcon={
                        <FontAwesomeIcon
                            style={{
                                color: "#008FD6"
                            }}
                            icon={faCirclePlus}
                        />
                    }
                    onClick={handleAddContacts}
                    sx={{ marginTop: 1 }}
                >
                    {t("Aggiungi contatto collegato")}
                </Button>
            </Box>
            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                contactiCollegati: false
                            })
                        }
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button type="button" onClick={(event: any) => handleSaveChanges(event, contactData, idsToDelete)} variant="contained">
                        {t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}
