import { <PERSON><PERSON><PERSON>, <PERSON>, FormControl, TextField, Button } from "@vapor/v3-components";
import { createFilterOptions } from "@mui/material/Autocomplete";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faIdCard, faCirclePlus, faTrashCan } from "@fortawesome/pro-regular-svg-icons";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { UseFormReturn } from "react-hook-form";
import useGetAddressKey from "../../hooks/useGetAddressKey";
import ContentHeader from "../../components/ContentHeader";
import { useEffect, useState, useCallback } from "react";
import usePostCustom from "../../../../hooks/usePostCustom";
import useGetAnagraficheActionData from "../../hooks/useGetAnagraficheActionData";
import ToastNotification from "../../../../custom-components/ToastNotification";
import { useNavigate } from "react-router-dom";
import CustomAutocomplete from "../../../../custom-components/CustomAutocomplete";

const filter = createFilterOptions<any>();

interface IRecapiti {
    anagraficheData: any;
    method: UseFormReturn;
    onSubmitUpdate?: (data: any) => void;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: any;
    id?: any;
}

export default function RecapitiForm(props: IRecapiti) {
    const { t } = useTranslation();
    const WARNING_TEXT_EMPTY_FIELD = t("E' necessario dare un nome all'ultimo campo aggiunto prima di proseguire");
    const WARNING_TEXT_DUPLICATE_CONTACT = t("Non è possibile inserire un tipo di contatto già utilizzato");
    const navigate = useNavigate();
    const { anagraficheData, method, onSubmitUpdate, isUpdate = false, showCreateForm, setShowCreateForm } = props;
    const addNewContactRequest = usePostCustom("anagrafiche/contactremotesave?noTemplateVars=true");
    const getPhoneKeyRequest = usePostCustom("peoplecenter/getphonekey?noTemplateVars=true");
    const { fetchData } = useGetAnagraficheActionData();

    const { control, watch, setValue, handleSubmit, trigger } = method;
    const { countries, province, contacts, cities } = anagraficheData;
    const { getAddressKeyFunction, getAllAddressKeyFunction } = useGetAddressKey();
    const [addressOptions, setAddressOptions] = useState([]);
    const [localContacts, setLocalContacts] = useState(anagraficheData.contacts || []);
    const [showWarningNotification, setShowWarningNotification] = useState<boolean>(false);
    const [toastWarningText, setToastWarningText] = useState<string>(WARNING_TEXT_EMPTY_FIELD);
    const values = watch();

    const handleAddAddress = () => {
        if (values.addresses && values.addresses.some((address: any) => !address.nome_campo || address.nome_campo.trim() === "")) {
            setToastWarningText(WARNING_TEXT_EMPTY_FIELD);
            setShowWarningNotification(true);
            return;
        }

        const newAddress = {
            address_id: "0",
            cap: "",
            name: "",
            nazione: "",
            via: "",
            residenca: "",
            provincia: "",
            nome_campo: "",
            citta: "",
            regione: ""
        };
        setValue("addresses", values.addresses ? [...values.addresses, newAddress] : [newAddress]);
    };

    const handleRemoveAddress = (index: number) => {
        const newAddresses = values.addresses.filter((_: any, i: number) => i !== index);
        setValue("addresses", newAddresses);
    };

    const onBlurGetAddressId = async (event: any, inputName?: string, inputValue?: string) => {
        const name = inputName || event.target.name;
        const value = inputValue || event.target.value;
        const index = name.split(".")[1];
        const id = name.split(".")[1];

        const newId = await getAddressKeyFunction(id, value);

        const updatedAddresses = values.addresses.map((address: any, i: number) => (i === parseInt(index) ? { ...address, address_id: newId } : address));
        setValue("addresses", updatedAddresses);
    };

    const handleResidenzaInput = async (event: any, value: any, index: any) => {
        if (value) {
            let cleanedNome = value.nome;
            const aggiungiPrefix = t('Aggiungi "');
            if (cleanedNome.startsWith(aggiungiPrefix)) {
                // Remove the prefix
                cleanedNome = cleanedNome.slice(aggiungiPrefix.length);
                // Remove trailing quote if present
                if (cleanedNome.endsWith('"')) {
                    cleanedNome = cleanedNome.slice(0, -1);
                }
            }
            setValue(`addresses.${index}.nome_campo`, cleanedNome);
            await onBlurGetAddressId(event, `addresses.${index}.nome_campo`, cleanedNome);
        }
    };

    const handleSaveChanges = (event: any) => {
        event.preventDefault();

        const currentContacts = values.contacts || [];

        const names = currentContacts.map((contact: any) => (contact.nome || "").trim());

        const hasDuplicates = new Set(names).size < names.length;

        if (hasDuplicates) {
            setToastWarningText(WARNING_TEXT_DUPLICATE_CONTACT);
            setShowWarningNotification(true);
            return;
        }

        // 5. Altrimenti, prosegui col salvataggio
        if (onSubmitUpdate !== undefined) handleSubmit(onSubmitUpdate)();
        setShowCreateForm({
            ...showCreateForm,
            recapiti: false
        });
    };

    useEffect(() => {
        const fetch = async () => {
            const response = await getAllAddressKeyFunction();
            setAddressOptions(response);
        };

        fetch();
    }, []);

    useEffect(() => {
        setLocalContacts(contacts || []);
    }, [contacts]);

    //this is the function that adds a new option in the options
    const handleAddNewContact = async (insertItem: string) => {
        const response: any = await addNewContactRequest.doFetch(true, {
            insertItem
        });
        return response.data;
    };

    const handleAddContacts = () => {
        if (values.contacts && values.contacts.some((contact: any) => !contact.valore || contact.valore.trim() === "")) {
            setToastWarningText(WARNING_TEXT_EMPTY_FIELD);
            setShowWarningNotification(true);
            return;
        }
        const newContact = {
            contact_id: "0",
            nome_campo: "",
            valore: "",
            nome: ""
        };
        setValue("contacts", values.contacts ? [...values.contacts, newContact] : [newContact]);
    };

    const handleRemoveContacts = (index: number) => {
        const newContacts = values.contacts.filter((_: any, i: number) => i !== index);
        setValue("contacts", newContacts);
    };

    const getContactKey = async (value: string) => {
        const response: any = await getPhoneKeyRequest.doFetch(true, {
            value,
            id: "0"
        });
        return response.data;
    };

    const handleContactChange = async (_e: any, option: any, index: number) => {
        if (!option) return;

        try {
            if (option.inputValue) {
                // New contact creation branch.
                const newContactData = await handleAddNewContact(option.inputValue);
                if (!newContactData) return;

                const id = await getContactKey(newContactData.description);
                if (!id) return;

                // Optionally, re-fetch updated contacts if needed.
                const data = await fetchData();
                const { contacts } = data;
                setLocalContacts(contacts);

                // Update form fields.
                setValue(`contacts.${index}.nome_campo`, id);
                setValue(`contacts.${index}.nome`, newContactData.description);

                // Trigger validations if required.
                trigger(`contacts.${index}.nome_campo`);
                trigger(`contacts.${index}.nome`);
            } else {
                // Existing contact branch.
                setValue(`contacts.${index}.nome_campo`, option.value);
                setValue(`contacts.${index}.nome`, option.label);
            }
        } catch (error) {
            console.error("Error in handleContactChange:", error);
        }
    };
    const options = localContacts?.map(({ id, nome }: any) => ({
        value: id,
        label: nome
    }));

    const getSelectedOption = useCallback(
        (index: any) => {
            const formContact = values.contacts[index];
            if (formContact && formContact.nome_campo) {
                const found = options?.find((opt: any) => opt.value === formContact.nome_campo);
                return (
                    found || {
                        value: formContact.nome_campo,
                        label: formContact.nome
                    }
                );
            }
            return null;
        },
        [values.contacts, options]
    );

    const navigateToLegacy = () => {
        const isConfirm = confirm("Si verrà reindirizzati sull'inserimento città, le modifiche non salvate verranno perse. Continuare?");

        if (isConfirm) {
            navigate("/legacy/cities/update?anagrafica=-1&uid=");
        }
    };


    return (
        <Box
            component="form"
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                ml: 1
            }}
        >
            <Box>
                <Box display="flex" sx={{ marginBottom: 2 }}>
                    <ContentHeader icon={faIdCard} title={t("Recapiti")} />
                </Box>

                {values?.addresses?.map((address: any, index: number) => {
                    return (
                        <div key={index}>
                            <Box sx={{ width: 815, mt: 2, display: "flex" }}>
                                <Typography variant="titleSmall" component="div">
                                    {values.addresses[index]?.nome_campo || `${t("Indirizzo")} ${index + 1}`}
                                </Typography>

                                <Button color="error" startIcon={<FontAwesomeIcon style={{ color: "error" }} icon={faTrashCan} />} sx={{ marginLeft: "auto" }} onClick={() => handleRemoveAddress(index)}>
                                    {t("Elimina")}
                                </Button>
                            </Box>
                            <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                                <FormControl sx={{ width: 815 }}>
                                    <CustomAutocomplete
                                        sx={{ width: "100%" }}
                                        options={addressOptions || []}
                                        onChange={(event: any, value: any) => handleResidenzaInput(event, value, index)}
                                        filterOptions={(options: any, params: any) => {
                                            const filtered = filter(options, params);
                                            const { inputValue } = params;
                                            const isExisting = options.some((option: any) => inputValue === option.nome);

                                            if (inputValue !== "" && !isExisting) {
                                                filtered.push({
                                                    inputValue,
                                                    nome: `Aggiungi "${inputValue}"`
                                                });
                                            }

                                            return filtered;
                                        }}
                                        selectOnFocus
                                        clearOnBlur
                                        getOptionLabel={(option: any) => {
                                            if (typeof option === "string") {
                                                return option;
                                            }
                                            if (option.nome.startsWith(t('Aggiungi "'))) {
                                                return option.inputValue;
                                            }
                                            return option.nome;
                                        }}
                                        value={address?.nome_campo}
                                        isOptionEqualToValue={(options: any, value: any) => {
                                            return options.nome === value;
                                        }}
                                        renderOption={(props: any, option: any) => {
                                            if (option.nome && option.nome.startsWith(t('Aggiungi "'))) {
                                                return (
                                                    <li {...props}>
                                                        <Typography>{option.nome}</Typography>
                                                    </li>
                                                );
                                            }
                                            return <li {...props}>{option.nome}</li>;
                                        }}
                                        renderInput={(params: any) => <TextField {...params} placeholder={t("Residenza anagrafica")} />}
                                    />
                                </FormControl>
                            </Box>
                            <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                                <FormInput sx={{ width: 600 }} control={control} label={t("Indirizzo")} type="text" name={`addresses.${index}.via`} />

                                <FormInput sx={{ width: 200 }} control={control} label={t("CAP")} type="text" name={`addresses.${index}.cap`} />
                            </Box>

                            <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                                <FormControl sx={{ width: 400 }}>
                                    <CustomAutocomplete
                                        sx={{ width: 400 }}
                                        options={cities || []}
                                        filterOptions={(options: any, params: any) => {
                                            const filtered = filter(options, params);
                                            const { inputValue } = params;
                                            const isExisting = options.some((option: any) => inputValue === option.nome);
                                            if (inputValue !== "" && !isExisting) {
                                                filtered.push({
                                                    inputValue: inputValue,
                                                    nome: `Aggiungi "${inputValue}"`
                                                });
                                            }
                                            return filtered;
                                        }}
                                        value={(cities || [])?.find((option: any) => option.id === address?.citta) || null}
                                        onChange={(_: any, value: any) => {
                                            if (value && value.inputValue) {
                                                navigateToLegacy();
                                            } else if (value) {
                                                setValue(`addresses.${index}.citta`, value.id);
                                            } else {
                                                setValue(`addresses.${index}.citta`, "");
                                            }
                                        }}
                                        getOptionLabel={(option: any) => {
                                            if (!option) return "";
                                            if (typeof option === "string") {
                                                return option;
                                            }
                                            if (option?.nome?.startsWith(t('Aggiungi "'))) {
                                                return option.inputValue;
                                            }
                                            return option.nome;
                                        }}
                                        isOptionEqualToValue={(option: any, value: any) => {
                                            return option.id === value.id;
                                        }}
                                        renderOption={(props: any, option: any) => {
                                            if (option.nome && option.nome.startsWith(t('Aggiungi "'))) {
                                                return (
                                                    <li {...props}>
                                                        <Typography>{option.nome}</Typography>
                                                    </li>
                                                );
                                            }
                                            return <li {...props}>{option.nome}</li>;
                                        }}
                                        renderInput={(params: any) => <TextField {...params} label={t("Comune")} />}
                                    />
                                </FormControl>
                                <FormControl sx={{ width: 400 }}>
                                    <FormInput
                                        sx={{ width: "100%" }}
                                        control={control}
                                        name={`addresses.${index}.provincia`}
                                        label={t("Provincia")}
                                        type="select"
                                        variant="outlined"
                                        options={province?.map(({ id, provincia }: any) => ({
                                            value: id,
                                            label: provincia
                                        }))}
                                    />
                                </FormControl>
                            </Box>
                            {/* ---This field missing on backend--- */}
                            {/* <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                                <FormInput
                                    sx={{ width: 815 }}
                                    control={control}
                                    label={t("Frazione")}
                                    type="text"
                                    name={`addresses.${index}.nome_campo`}
                                />
                            </Box> */}

                            <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                                <FormInput sx={{ width: 815 }} control={control} value={address.altri} label={t("Altri dettagli dell’indirizzo")} type="text" name={`addresses.${index}.altri`} />
                            </Box>

                            <Box sx={{ display: "flex", gap: 2, mt: 2 }}>
                                <FormControl sx={{ width: 400 }}>
                                    <FormInput
                                        sx={{ width: "100%" }}
                                        control={control}
                                        name={`addresses.${index}.nazione`}
                                        label={t("Nazione")}
                                        type="select"
                                        variant="outlined"
                                        options={(countries || [])?.map(({ id, nome }: any) => ({
                                            value: id,
                                            label: nome
                                        }))}
                                        onChange={(_event: any, value: any) => {
                                            setValue(`addresses.${index}.nazione`, value);
                                        }}
                                    />
                                </FormControl>
                                <FormControl sx={{ width: 400 }}>
                                    <FormInput
                                        sx={{ width: "100%" }}
                                        control={control}
                                        name={`addresses.${index}.regione`}
                                        label={t("Regione")}
                                        value={address?.regione}
                                        type="text"
                                        variant="outlined"
                                        onChange={(_event: any, value: any) => {
                                            setValue(`addresses.${index}.regione`, value);
                                        }}
                                    />
                                </FormControl>
                            </Box>
                        </div>
                    );
                })}

                <Box sx={{ marginBottom: 2 }}>
                    <Button
                        startIcon={
                            <FontAwesomeIcon
                                style={{
                                    color: "#008FD6"
                                }}
                                icon={faCirclePlus}
                            />
                        }
                        onClick={() => handleAddAddress()}
                        sx={{ marginTop: 1 }}
                    >
                        {t("Aggiungi indirizzo")}
                    </Button>
                </Box>

                <Box>
                    {values?.contacts?.map((field: any, index: number) => {
                        return (
                            <div key={field.id} style={{ marginTop: 30 }}>
                                <Box sx={{ width: 815, display: "flex" }}>
                                    <Typography variant="titleSmall" component="div">
                                        {`${t("Contatto")} ${index + 1}`}
                                    </Typography>

                                    <Button
                                        color="error"
                                        startIcon={
                                            <FontAwesomeIcon
                                                style={{
                                                    color: "error"
                                                }}
                                                icon={faTrashCan}
                                            />
                                        }
                                        sx={{ marginLeft: "auto" }}
                                        onClick={() => handleRemoveContacts(index)}
                                    >
                                        {t("Elimina")}
                                    </Button>
                                </Box>

                                <Box sx={{ display: "flex", gap: 2 }}>
                                    <FormControl sx={{ width: 400, mt: 1 }}>
                                        <CustomAutocomplete
                                            sx={{ width: "100%" }}
                                            options={options}
                                            onChange={(e: any, option: any) => handleContactChange(e, option, index)}
                                            filterOptions={(options: any, params: any) => {
                                                const filtered = filter(options, params);
                                                const { inputValue } = params;
                                                const isExisting = options.some((option: any) => inputValue === option.label);
                                                if (inputValue !== "" && !isExisting) {
                                                    filtered.push({
                                                        inputValue,
                                                        label: t('Aggiungi "{{inputValue}}"', {
                                                            inputValue
                                                        })
                                                    });
                                                }
                                                return filtered;
                                            }}
                                            selectOnFocus
                                            clearOnBlur
                                            getOptionLabel={(option: any) => {
                                                if (typeof option === "string") {
                                                    return option;
                                                }
                                                if (option.inputValue) {
                                                    return option.inputValue;
                                                }
                                                return option.label;
                                            }}
                                            value={getSelectedOption(index)}
                                            renderOption={(props: any, option: any) => {
                                                if (option.inputValue) {
                                                    return (
                                                        <li {...props}>
                                                            <Typography>{option.label}</Typography>
                                                        </li>
                                                    );
                                                }
                                                return <li {...props}>{option.label}</li>;
                                            }}
                                            renderInput={(params: any) => <TextField {...params} placeholder={t("Contatti")} variant="outlined" />}
                                        />
                                    </FormControl>

                                    <FormControl sx={{ width: 400, pt: "8px" }}>
                                        <TextField
                                            value={field.valore}
                                            onChange={(e: any) => {
                                                setValue(`contacts.${index}.valore`, e.target.value || "");
                                            }}
                                        />
                                    </FormControl>
                                </Box>
                            </div>
                        );
                    })}
                    <Button
                        startIcon={
                            <FontAwesomeIcon
                                style={{
                                    color: "#008FD6"
                                }}
                                icon={faCirclePlus}
                            />
                        }
                        sx={{ marginTop: 1 }}
                        onClick={() => handleAddContacts()}
                    >
                        {t("Aggiungi contatto")}
                    </Button>
                </Box>
            </Box>
            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                recapiti: false
                            })
                        }
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button type="submit" onClick={(event: any) => handleSaveChanges(event)} variant="contained">
                        {t("Salva")}
                    </Button>
                </Box>
            )}
            <ToastNotification showNotification={showWarningNotification} setShowNotification={setShowWarningNotification} severity="warning" text={t(`${toastWarningText}`)} />
        </Box>
    );
}
