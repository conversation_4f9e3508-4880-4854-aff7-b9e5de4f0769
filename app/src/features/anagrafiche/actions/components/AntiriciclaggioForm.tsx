import { useState, useEffect } from "react";
import { Typo<PERSON>, Box, FormControl, Button } from "@vapor/react-material";
import { faIdCard } from "@fortawesome/pro-regular-svg-icons";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import { UseFormReturn } from "react-hook-form";
import moment from "moment";
import { AUTHORITY_DATA } from "../../constants/constants";
import ContentHeader from "../../components/ContentHeader";

interface IAntiriciclaggio {
    anagraficheData: any;
    method: UseFormReturn;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: React.Dispatch<React.SetStateAction<any>>;
    onSubmitUpdate?: (data: any) => void;
    id?: any;
}

const parseDateString = (dateString: string) => {
    const [day, month, year] = dateString.split("/").map(Number);
    return new Date(year, month - 1, day); // month is 0-based in JavaScript Date
};

export default function AntiriciclaggioForm(props: IAntiriciclaggio) {
    const {
        anagraficheData,
        method,
        showCreateForm,
        isUpdate = false,
        setShowCreateForm,
        onSubmitUpdate,
    }: any = props;
    const { t } = useTranslation();
    const [autoritaRilascioDate, setAutoritaRilascioDate] =
        useState<Date | null>(null);
    const { control, watch, setValue, handleSubmit } = method;
    const { auiDocumenti, auiIdentificativi, comuni, province } = anagraficheData;

  

    const onDateChange = (_name: string, value: Date) => {
        let formattedDate = moment(value).format("DD/MM/YYYY");
        setAutoritaRilascioDate(value);
        setValue("data_rilascio", formattedDate);
    };

    const autorita_rilascio = watch("autorita_rilascio");
    const data_rilascio = watch("data_rilascio");
    const autorita_comune = watch("autorita_comune");
    const autorita_provincia = watch("autorita_provincia");

    useEffect(() => {
        if (data_rilascio) {
            setAutoritaRilascioDate(parseDateString(data_rilascio));
        }
    }, [data_rilascio]);

    const handleSaveChanges = (event: any) => {
        event.preventDefault();
        handleSubmit(onSubmitUpdate)();
        setShowCreateForm({
            ...showCreateForm,
            antiriciclaggio: false,
        });
    };

    return (
        <Box
            component="form"
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                ml: 1
            }}
        >
            <Box display="flex">
                <ContentHeader icon={faIdCard} title={t("Antiriciclaggio")} />
            </Box>

            <Typography variant="subtitle" color="primary.textTitleColor">
                {t("Archivio Unico Informatico")}
            </Typography>

            <Box sx={{ display: "flex", gap: 2 }}>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{ width: "100%" }}
                        control={control}
                        name={`tipo_identificazione`}
                        label={t("Identificazione")}
                        type="select"
                        variant="outlined"
                        options={auiIdentificativi?.map(
                            ({ id, nome }: any) => ({
                                value: id,
                                label: nome,
                            })
                        )}
                    />
                </FormControl>

                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{ width: "100%" }}
                        control={control}
                        name={`tipo_documento`}
                        label={t("Tipologia documento")}
                        type="select"
                        variant="outlined"
                        options={auiDocumenti?.map(({ id, nome }: any) => ({
                            value: id,
                            label: nome,
                        }))}
                    />
                </FormControl>
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
                <FormInput
                    sx={{ width: 400 }}
                    control={control}
                    label={t("Numero documento")}
                    name={`num_documento`}
                />

                <div style={{ width: 400 }}>
                    <DatePicker
                        sx={{
                            width: "100%",
                        }}
                        label="Data di rilascio"
                        value={autoritaRilascioDate}
                        onChange={(value: Date | null) => {
                            if (value) onDateChange("data_rilascio", value);
                        }}
                    />
                </div>
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        control={control}
                        name={`autorita_rilascio`}
                        label={t("Autorità di rilascio")}
                        type="select"
                        variant="outlined"
                        options={AUTHORITY_DATA}
                    />
                </FormControl>


                {autorita_rilascio === "1" && (
                    <FormControl sx={{ width: 400, pt: "20px" }}>
                        <FormInput
                            sx={{
                                width: "100%",
                            }}
                            control={control}
                            name={`autorita_comune`}
                            value={autorita_comune}
                            type="select"
                            variant="outlined"
                            options={comuni.map(({ id, nome }: any) => ({
                                value: id,
                                label: nome,
                            }))}
                        />
                    </FormControl>
                )}
                {["3", "4"].includes(autorita_rilascio) && (
                    <FormControl sx={{ width: 400, pt: "20px" }}>
                        <FormInput
                            sx={{
                                width: "100%",
                            }}
                            control={control}
                            name={`autorita_provincia`}
                            value={autorita_provincia}
                            type="select"
                            variant="outlined"
                            options={province.map(({ id, provincia, iniziali }: any) => ({
                                value: id,
                                label: `${provincia} (${iniziali})`,
                            }))}
                        />
                    </FormControl>
                )}
            </Box>
            <Box sx={{ display: "flex", gap: 2 }}>
                <FormInput
                    sx={{ width: 400 }}
                    control={control}
                    label={t("Professione")}
                    name={`professione`}
                />
            </Box>
            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815,
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                antiriciclaggio: false,
                            })
                        }
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button
                        type="submit"
                        onClick={(event: any) => handleSaveChanges(event)}
                        variant="contained"
                    >
                        {t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}
