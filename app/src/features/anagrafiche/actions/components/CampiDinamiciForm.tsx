import { useState, useEffect } from "react";
import {
    Box, 
    FormControl, 
    Button, 
    TextField,
    InputLabel,
    TextareaAutosize,
    IconButton, Tooltip
} from "@vapor/v3-components";
import CloseIcon from '@mui/icons-material/Close';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import { faIdCard } from "@fortawesome/pro-regular-svg-icons";
import { useTranslation } from "@1f/react-sdk";
import { UseFormReturn } from "react-hook-form";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import ContentHeader from "../../components/ContentHeader";
import useGetCustom from "../../../../hooks/useGetCustom";
import Spinner from "../../../../custom-components/Spinner";
import FormInput from "../../../../custom-components/FormInput";

interface ICampiDinamici {
    anagraficheData: any;
    method: UseFormReturn;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: React.Dispatch<React.SetStateAction<any>>;
    onSubmitUpdate?: (data: any) => void;
    id?: any;
}

export default function CampiDinamiciForm(props: ICampiDinamici) {
    const {
        method,
        showCreateForm,
        isUpdate = false,
        setShowCreateForm,
        onSubmitUpdate,
    }: any = props;
    const { t } = useTranslation();
    
    const [dynamicFields, setDynamicFields] = useState<any[]>([]);
    const [dateStates, setDateStates] = useState<{[key: string]: Date | null}>({});
    const [isLoadingFields, setIsLoadingFields] = useState<boolean>(false);
    
    
    const {
        register,
        setValue,
        control,
        formState: { errors },
        watch,
        handleSubmit,
    } = method;
    const values = watch();
    

   
    const loadCampiDinamiciRequest = useGetCustom(
        "campidinamici/loaddynamicfields?noTemplateVars=true"
    );

    useEffect(() => {
        loadCampiDinamici();
    }, [values.id, values.categoria]);

   
    const parseDateString = (dateString: string) => {
        if (!dateString) return null;
        
        if (dateString.includes('-') && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = dateString.split("-").map(Number);
            return new Date(year, month - 1, day);
        }
        
        if (dateString.includes('/')) {
            const [day, month, year] = dateString.split("/").map(Number);
            return new Date(year, month - 1, day);
        }
        
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
    };


    const loadCampiDinamici = async () => {
        try {
            setIsLoadingFields(true);
            const response: any = await loadCampiDinamiciRequest.doFetch(true, {
                categoryID: values.categoria,
                eventID: values.id,
                table: "anagrafiche",
            });
      
    
            if (response?.data) {
                setDynamicFields(response.data);
                               
                const initialDateStates: {[key: string]: Date | null} = {};
                response.data.forEach((field: any) => {
                    const fieldName = `field_${field.id}`;
                    
                    if (field.tipo === 'date' && field.restore) {
                     
                        const parsedDate = parseDateString(field.restore);
                        initialDateStates[fieldName] = parsedDate;
                        setValue(fieldName, field.restore);
                    } else {
                        setValue(fieldName, field.restore || '');
                    }
                });
                
                setDateStates(initialDateStates);
            }
        } catch (error) {
            console.error(error);
        } finally {
            setIsLoadingFields(false);
        }
    };

    const parseSelectOptions = (optString: string) => {
    if (!optString) return [];

    try {
        const parsed = JSON.parse(optString);

   
        if (parsed && typeof parsed === 'object' && !Array.isArray(parsed)) {
            return Object.entries(parsed).map(([key, value]) => ({
                label: key.trim(),
                value,
            }));
        }

 
        if (Array.isArray(parsed)) {
            return parsed.map((item) => ({
                value: typeof item === 'object' ? item.value || item.id : item,
                label: typeof item === 'object' ? item.label || item.name || item.text : item,
            }));
        }
    } catch (e) {
        console.log({ e });
    }


    if (optString.includes(',')) {
        return optString.split(',').map((item) => {
            const trimmed = item.trim();
            if (trimmed.includes(':')) {
                const [value, label] = trimmed.split(':');
                return { value: value.trim(), label: label.trim() };
            }
            return { label: trimmed, value: trimmed };
        });
    }

   
    if (optString.includes('|')) {
        return optString.split('|').map((item) => {
            const trimmed = item.trim();
            if (trimmed.includes(':')) {
                const [value, label] = trimmed.split(':');
                return { value: value.trim(), label: label.trim() };
            }
            return { value: trimmed, label: trimmed };
        });
    }

 
    return [{ value: optString, label: optString }];
};

    const validateRegex = (value: string, regexPattern: string) => {
        if (!regexPattern || !value) return true;
        try {
          
            let cleanPattern = regexPattern
                .replace(/&amp;/g, '&')           
                .replace(/&lt;/g, '<')            
                .replace(/&gt;/g, '>')            
                .replace(/&quot;/g, '"')          
                .replace(/&#39;/g, "'")           
                .replace(/\\\\\./g, '\\.')        
                .replace(/\\\'/g, "'");           
            
            const regex = new RegExp(cleanPattern);
            return regex.test(value);
        } catch (error) {
            console.error('Invalid regex pattern:', regexPattern, error);
            return true; 
        }
    };

    const renderDynamicField = (field: any) => {
        const fieldName = `field_${field.id}`;
        const fieldValue = values[fieldName] || field.restore || '';
        const regexError = field.opt;
        
        const isRegexValid = field.tipo === 'regex' && regexError 
            ? validateRegex(fieldValue, regexError) 
            : true;
        
        switch (field.tipo) {
            case 'regex':
            case 'text':
                return (
                    <Box key={field.id} sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                        <FormControl sx={{ width: 400 }}>
                            <FormInput
                                control={control}
                                name={fieldName}
                                label={field.nome}
                                type="text"
                                variant="outlined"
                                 sx={{ 
                                    width: "100%",
                                    ...(field.tipo === 'regex' && regexError && !isRegexValid && {
                                        '& .MuiOutlinedInput-root': {
                                            '& fieldset': {
                                    borderColor: 'warning.main',
                                    borderWidth: '2px',
                                },
                                '&:hover fieldset': {
                                    borderColor: 'warning.main',
                                },
                                '&.Mui-focused fieldset': {
                                    borderColor: 'warning.main',
                                },
                            },
                        })
                    }}
                            />
                        </FormControl>
                        
                        {field.tipo === 'regex' && regexError && !isRegexValid && (
                            <Tooltip
                                title="Attenzione!"
                                arrow
                                placement="right"
                                description={"Il valore inserito non rispetta l'espressione di controllo prevista per il campo dinamico. "}
                            >
                                <ReportProblemIcon 
                                    sx={{
                                        color: 'warning.main',
                                        cursor: "pointer",
                                        marginTop: 3,
                                        marginLeft: 1
                                    }}
                                />
                            </Tooltip>
                        )}
                    </Box>
                );
                
            case 'checkbox':
                return (
                    <FormInput
                        key={field.id}
                        control={control}
                        name={fieldName}
                        value={fieldValue}
                        type="checkbox"
                        options={[
                            {
                                label: field.nome,
                                value: "1"
                            }
                        ]}
                    />
                );
                
            case 'select':
                const options = parseSelectOptions(field.opt || '');
                return (
                    <FormControl key={field.id} sx={{ width: 400 }}>
                        <FormInput
                            sx={{ width: "100%" }}
                            control={control}
                            name={fieldName}
                            label={field.nome}
                            type="select"
                            variant="outlined"
                            options={options}
                            
                        />
                    </FormControl>
                );
                
            case 'memo':
                return (
                    <Box key={field.id} sx={{ width: 400 }}>
                        <FormControl>
                            <InputLabel>{field.nome}</InputLabel>
                            <TextareaAutosize
                               spellCheck="false"
                                minRows={5}
                                name={`field_${field.id}`}
                                value={fieldValue}
                                onChange={(e) => {
                                    setValue(fieldName, e.target.value);
                                }}
                                  style={{
                                        width: 400,
                                        resize: "none"
                                    }}
                               
                            />
                        </FormControl>
                       
                    </Box>
                );
                
            case 'date':
                return (
                    <Box key={field.id} sx={{ 
                        width: 400, 
                        display: 'flex', 
                        gap: 1 
                    }}>
                        <div style={{ width: 330, flex: 1 }}>
                            <DatePicker
                                sx={{
                                    width: "100%",
                                }}
                                label={field.nome}
                                name={`field_${field.id}`}
                                value={dateStates[fieldName] || null}
                                
                                onChange={(value: Date | null) => {
                                    setValue(fieldName, value);
                                    setDateStates(prev => ({
                                        ...prev,
                                        [fieldName]: value
                                    }));
                                }}
                            />
                        </div>

                        {dateStates[fieldName] && (
                            <div style={{ 
                                paddingTop: '20px',
                                display: 'flex',
                                alignItems: 'center',
                                width: 40
                            }}>
                                <IconButton 
                                    edge="end" 
                                    color="primary"
                                    variant="outlined"
                                    aria-label="delete"  
                                    onClick={() => {
                                        setDateStates(prev => ({
                                            ...prev,
                                            [fieldName]: null
                                        }));
                                        setValue(fieldName, '');
                                    }} 
                                    size="medium"
                                    style={{
                                        width: 40,
                                        height: 40
                                    }}
                                >
                                    <CloseIcon />
                                </IconButton>
                            </div>
                        )}
                    </Box>
                );
                
            default:
                return (
                    <TextField
                        key={field.id}
                        label={field.nome}
                        name={`field_${field.id}`}
                        helperText={field.descrizione}
                        sx={{ width: 400 }}
                        {...register(fieldName, {
                            required: field.required === '1'
                        })}
                        value={fieldValue}
                        error={!!errors[fieldName]}
                    />
                );
        }
    };

    const handleSaveChanges = (event: any) => {
        event.preventDefault();
        handleSubmit(onSubmitUpdate)();
        setShowCreateForm({
            ...showCreateForm,
            campiDinamici: false,
        });
    };

    return (
         <Box
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                ml: 1
            }}
        >
            <Box display="flex">
                <ContentHeader icon={faIdCard} title={t("Campi Dinamici")} />
            </Box>

            <Box sx={{ display: "flex", flexDirection: "column", gap: 2, position: "relative", minHeight: isLoadingFields ? 200 : "auto" }}>
                {isLoadingFields ? (
                    <Spinner fullPage={false} />
                ) : (
                    dynamicFields.map((field) => renderDynamicField(field))
                )}
            </Box>

            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815,
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                campiDinamici: false,
                            })
                        }
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button
                        type="submit"
                        onClick={(event: any) => handleSaveChanges(event)}
                        variant="contained"
                    >
                        {t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}
