import { useState, useEffect } from "react";
import { Typo<PERSON>, Box, FormControl, Button } from "@vapor/react-material";
import { faIdCard } from "@fortawesome/pro-regular-svg-icons";
import FormInput from "../../../../custom-components/FormInput";
import { useTranslation } from "@1f/react-sdk";
import { UseFormReturn } from "react-hook-form";
import { DatePicker } from "../../../../components/ui-kit/DatePicker";
import moment from "moment";
import ContentHeader from "../../components/ContentHeader";

interface ICammeraDiCommercio {
    anagraficheData: any;
    method: UseFormReturn;
    isUpdate?: boolean;
    showCreateForm?: any;
    setShowCreateForm?: React.Dispatch<React.SetStateAction<any>>;
    onSubmitUpdate?: (data: any) => void;
    id?: any;
}

const parseDateString = (dateString: string) => {
    const [day, month, year] = dateString.split("/").map(Number);
    return new Date(year, month - 1, day);
};

export default function CammeraDiCommercioForm(props: ICammeraDiCommercio) {
    const {
        anagraficheData,
        method,
        isUpdate = false,
        showCreateForm,
        setShowCreateForm,
        onSubmitUpdate,
    }: any = props;
    const { t } = useTranslation();

    const [dataIscrizione, setDataIscrizione] = useState<Date | null>(null);

    const { control, watch, setValue, handleSubmit } = method;
    const { cities, valute, person } = anagraficheData;

    const finalValute = [
        {
            id: "0",
            nome: "-",
        },
        ...valute,
    ];

    const onDateChange = (_: string, value: Date) => {
        let formattedDate = moment(value).format("DD/MM/YYYY");
        setDataIscrizione(value);
        setValue("dataiscrizione", formattedDate.toString());
    };
    const data_Iscrizione = watch("dataiscrizione");
    const communeSelected = watch("comune");

    useEffect(() => {
        if (data_Iscrizione) {
            setDataIscrizione(parseDateString(data_Iscrizione));
        }
    }, [data_Iscrizione]);

    useEffect(() => {
        const selectedCity = cities?.find(
            (city: any) => city?.id === communeSelected
        );
        if (selectedCity) {
            setValue("provincia", selectedCity.provincia);
        } else {
            setValue("provincia", "");
        }
    }, [communeSelected, setValue]);

    const handleSaveChanges = (event: any) => {
        event.preventDefault();
        handleSubmit(onSubmitUpdate)();
        setShowCreateForm({
            ...showCreateForm,
            cameraDiCommercio: false,
        });
    };

    return (
        <Box
            component="form"
            sx={{
                display: "flex",
                flexDirection: "column",
                gap: 2,
                ml: 1
            }}
        >
            <Box display="flex">
                <ContentHeader
                    icon={faIdCard}
                    title={t("Camera di commercio")}
                />
            </Box>

            <Typography variant="subtitle" color="primary.textTitleColor">
                {t("Camera di Commercio")}
            </Typography>
            <Box sx={{ display: "flex", gap: 2 }}>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        control={control}
                        name={`comune`}
                        label={t("Comune")}
                        type="select"
                        variant="outlined"
                        options={cities?.map(({ id, nome }: any) => ({
                            value: id,
                            label: nome,
                        }))}
                    />
                </FormControl>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        disabled
                        control={control}
                        name={`provincia`}
                        label={t("Provincia")}
                        type="text"
                        variant="outlined"
                    />
                </FormControl>
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        control={control}
                        name={`sezione`}
                        label={t("Sezione")}
                        type="text"
                        variant="outlined"
                    />
                </FormControl>
                <div style={{ width: 400 }}>
                    <DatePicker
                        sx={{
                            width: "100%",
                        }}
                        label={t("Data Iscrizione")}
                        value={dataIscrizione}
                        onChange={(value: Date | null) => {
                            if (value) onDateChange("dataiscrizione", value);
                        }}
                    />
                </div>
            </Box>

            {person?.pctid !== "PFI" && (
                <Box sx={{ display: "flex", gap: 2 }}>
                    <FormControl sx={{ width: 400 }}>
                        <FormInput
                            sx={{
                                width: "100%",
                            }}
                            control={control}
                            name={`nrea`}
                            label={t("Numero REA")}
                            type="text"
                            variant="outlined"
                        />
                    </FormControl>
                </Box>
            )}

            <Typography variant="subtitle" color="primary.textTitleColor">
                {t("Capitale Sociale")}
            </Typography>

            <Box sx={{ display: "flex", gap: 2 }}>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        control={control}
                        name={`deliberato`}
                        label={t("Deliberato")}
                        type="number"
                        placeholder="0,00"
                        variant="outlined"
                    />
                </FormControl>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        control={control}
                        name={`sottoscritto`}
                        label={t("Sottoscritto")}
                        type="number"
                        placeholder="0,00"
                        variant="outlined"
                    />
                </FormControl>
            </Box>
            <Box sx={{ display: "flex", gap: 2 }}>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        control={control}
                        name={`versato`}
                        label={t("Versato")}
                        type="number"
                        placeholder="0,00"
                        variant="outlined"
                    />
                </FormControl>
                <FormControl sx={{ width: 400 }}>
                    <FormInput
                        sx={{
                            width: "100%",
                        }}
                        control={control}
                        name={`valuta`}
                        label={t("Valuta")}
                        type="select"
                        variant="outlined"
                        options={finalValute?.map(({ id, nome }: any) => ({
                            value: id,
                            label: nome,
                        }))}
                    />
                </FormControl>
            </Box>
            {isUpdate && (
                <Box
                    gap={2}
                    sx={{
                        display: "flex",
                        justifyContent: "flex-end",
                        width: 815,
                    }}
                >
                    <Button
                        type="button"
                        onClick={() =>
                            setShowCreateForm({
                                ...showCreateForm,
                                cameraDiCommercio: false,
                            })
                        }
                        variant="outlined"
                    >
                        {t("Annulla")}
                    </Button>
                    <Button
                        type="submit"
                        onClick={(event: any) => handleSaveChanges(event)}
                        variant="contained"
                    >
                        {t("Salva")}
                    </Button>
                </Box>
            )}
        </Box>
    );
}
