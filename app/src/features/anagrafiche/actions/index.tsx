import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Toolbar, VaporPage } from "@vapor/react-custom";
import { <PERSON><PERSON>, Stack, Grid } from "@vapor/react-material";
import PageTitle from "../../../custom-components/PageTitle";
import AnagraficheTypology from "./anagraficheTypology";
import useGetAnagraficheActionData from "./../hooks/useGetAnagraficheActionData";
import AnagraficheSidebar from "./anagraficheSidebar";
import UserData from "./userData";
import { useTranslation } from "@1f/react-sdk";
import { useAnagraficheFormHook } from "../hooks/useAnagraficheFormHook";
import useSaveAnagrafiche from "../hooks/useSaveAnagrafiche";
import { useCardData } from "../helpers/typologyData";
import useTags from "../hooks/useTags";
import ToastNotification from "../../../custom-components/ToastNotification";

export default function AnagraficheActionsIndex() {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { anagraficheData, isUpdate } = useGetAnagraficheActionData(); //data to populate the menus
    const cardData = useCardData(anagraficheData);

    const { saveTags } = useTags();

    const { method, handleSubmit, firstStepCompleted, setFirstStepCompleted, userType, setUserType } = useAnagraficheFormHook(anagraficheData, isUpdate);
    const { onSubmitCreate, mandatoryFields, optionalFields } = useSaveAnagrafiche();


    const { setError } = method;

    useEffect(() => {
        if (mandatoryFields && Object.keys(mandatoryFields).length > 0) {
            Object.keys(mandatoryFields).forEach((fieldName) => {
                setError(fieldName, {
                    type: "manual",
                    message: mandatoryFields[fieldName]
                });
            });
        }
    }, [mandatoryFields, setError]);

    const [currentSection, setCurrentSection] = useState<string>("1");
    const [parentTipologiaSelected, setParentTipologiaSelected] = useState<string>("");
    const [selectedTagsValue, setSelectedTagsValue] = useState<any[]>([]);
    const [showWarningNotification, setShowWarningNotification] = useState<boolean>(false);
    const { watch } = method;

    useEffect(() => {
        if (isUpdate) {
            setFirstStepCompleted(true);
        }
    }, [isUpdate]);

    const values = watch();

    const parentRef = useRef<HTMLDivElement>(null);
    const datiGeneraliRef = useRef<HTMLDivElement>(null);
    const recapitiRef = useRef<HTMLDivElement>(null);
    const contactsRefs = useRef<HTMLDivElement>(null);
    const groupsRefs = useRef<HTMLDivElement>(null);
    const noteprofilazioneRefs = useRef<HTMLDivElement>(null);
    const cameraDiCommercioRefs = useRef<HTMLDivElement>(null);
    const fiscaliBancheRefs = useRef<HTMLDivElement>(null);
    const antiriciclaggioRefs = useRef<HTMLDivElement>(null);
    const campiDinamiciRefs = useRef<HTMLDivElement>(null);
    const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
        if (parentRef.current && ref.current) {
            const parent = parentRef.current;
            const element = ref.current;
            const parentTop = parent?.getBoundingClientRect().top;
            const elementTop = element.getBoundingClientRect().top;
            const scrollTo = elementTop - parentTop + parent.scrollTop;
            setCurrentSection(ref.current.id);
            window.scrollTo({ top: scrollTo, behavior: "smooth" });
        }
    };

    const refs = {
        datiGeneraliRef,
        recapitiRef,
        contactsRefs,
        groupsRefs,
        noteprofilazioneRefs,
        fiscaliBancheRefs,
        cameraDiCommercioRefs,
        antiriciclaggioRefs,
        campiDinamiciRefs,
        parentRef
    };

    const handleAnagraficheSaveAction = async () => {
        let statusResponse: any;
        let anagraficaUuidResponse: any;

        await handleSubmit(async (data) => {
            const currentContacts = data.contacts || [];

            const names = currentContacts.map((contact: any) => (contact.nome || "").trim());

            const hasDuplicates = new Set(names).size < names.length;

            if (hasDuplicates) {
                setShowWarningNotification(true);
                return;
            }
            const { status, anagraficaUuid }: any = await onSubmitCreate(data);
            statusResponse = status;
            anagraficaUuidResponse = anagraficaUuid;
        })();

        if (statusResponse === 200) {
            await saveTags(anagraficaUuidResponse, selectedTagsValue);
            navigate("/anagrafiche");
        }
    };

    useEffect(() => {
        const data: any = cardData.find((item: any) => item.id === userType.parentSelected);
        !!data &&
            setUserType((prev: any) => ({
                ...prev,
                type: data.title,
                icon: data.icon,
                details: data.details,
                detailSelected: data.defaultIdOption || "0"
            }));
    }, [userType.parentSelected]);

    return (
        <VaporPage
            contentToolbar={
                <Toolbar
                    contentRight={
                        <Stack direction="row" gap={1}>
                            <Button onClick={() => navigate("/anagrafiche")} variant="outlined">
                                {firstStepCompleted ? t("Annulla") : t("Indietro")}
                            </Button>

                            <Button variant="contained" type={firstStepCompleted ? "submit" : "button"} disabled={!firstStepCompleted && userType.parentSelected === ""} onClick={firstStepCompleted ? () => handleAnagraficheSaveAction() : () => setFirstStepCompleted(true)}>
                                {firstStepCompleted ? t("Salva") : t("Avanti")}
                            </Button>
                        </Stack>
                    }
                />
            }
        >
            <PageTitle title={t("Nuova anagrafica")} pathToPrevPage="/anagrafiche" />
            {firstStepCompleted ? (
                <VaporPage.Section>
                    <Grid container spacing={2}>
                        <Grid
                            item
                            md={2}
                            style={{
                                borderRight: "hsl(200, 20%, 77%) solid 1px",
                                marginTop: "inherit",
                                marginLeft: "inherit",
                                backgroundColor: "#f7f7f7"
                            }}
                        >
                            <AnagraficheSidebar scrollToSection={scrollToSection} refs={refs} values={values} currentSection={currentSection} userType={userType} anagraficheData={anagraficheData} />
                        </Grid>
                        <Grid item md={9} sx={{ padding: "20px" }}>
                            <UserData 
                                anagraficheData={anagraficheData} 
                                refs={refs} 
                                method={method} 
                                t={t} 
                                setCurrentSection={setCurrentSection} 
                                userType={userType} 
                                setUserType={setUserType} 
                                setSelectedTagsValue={setSelectedTagsValue} 
                                mandatoryFields={mandatoryFields} 
                                optionalFields={optionalFields}
                                />
                        </Grid>
                    </Grid>
                </VaporPage.Section>
            ) : (
                <VaporPage.Section>
                    <AnagraficheTypology anagraficheParams={values} parentTipologiaSelected={parentTipologiaSelected} setParentTipologiaSelected={setParentTipologiaSelected} userType={userType} setUserType={setUserType} anagraficheData={anagraficheData} />
                </VaporPage.Section>
            )}
            <ToastNotification showNotification={showWarningNotification} setShowNotification={setShowWarningNotification} severity="warning" text={t("Non è possibile inserire un tipo di contatto già utilizzato")} />
        </VaporPage>
    );
}
