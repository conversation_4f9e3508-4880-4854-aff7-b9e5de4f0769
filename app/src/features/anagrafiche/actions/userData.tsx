import { useEffect, useState } from "react";
import DatiGeneraliForm from "./components/DatiGeneraliForm";
import RecapitiForm from "./components/RecapitiForm";
import NoteProfilzaioneForm from "./components/NoteProfilazioneForm";
import FisacliBancheForm from "./components/FiscaliBancheForm";
import AntiriciclaggioForm from "./components/AntiriciclaggioForm";
import CammeraDiCommercioForm from "./components/CammeraDiCommercioForm";
import { Divider } from "@vapor/react-material";
import { debounce } from "lodash";
import { CUSTOMER_TYPE } from "../constants/anagraficheType";
import CampiDinamiciForm from "./components/CampiDinamiciForm";

interface IUserData {
    anagraficheData: any;
    refs: any;
    method: any;
    t: any;
    setCurrentSection: React.Dispatch<React.SetStateAction<string>>;
    userType: any;
    setUserType: any;
    setSelectedTagsValue: any;
    mandatoryFields: any;
    optionalFields: any;
}

const UserData = (props: IUserData) => {
    const { anagraficheData, refs, method, t, setCurrentSection, userType, setUserType, setSelectedTagsValue, mandatoryFields , optionalFields} = props;
    const { datiGeneraliRef, recapitiRef, contactsRefs, noteprofilazioneRefs, cameraDiCommercioRefs, fiscaliBancheRefs, antiriciclaggioRefs,campiDinamiciRefs, groupsRefs, parentRef } = refs;
   
    const [bottomPadding, setBottomPadding] = useState<string>("0");

    const {
        formState: { errors }
    } = method;

 

    const debouncedHandleIntersection = debounce((entries: any, setCurrentSection: any) => {
        entries.forEach((entry: any) => {
            if (entry.isIntersecting) {
                setCurrentSection(entry.target.id);
            }
        });
    }, 100); // Adjust


    useEffect(() => {
        try {
            const observer = new IntersectionObserver((entries) => debouncedHandleIntersection(entries, setCurrentSection), {
                root: null,
                threshold: [0.1, 0.3, 0.5, 0.7, 1.0], // Adjust based on section sizes
                rootMargin: "-50px 0px -50% 0px"
            });

            const sections = [datiGeneraliRef, recapitiRef, contactsRefs, noteprofilazioneRefs, cameraDiCommercioRefs, fiscaliBancheRefs, antiriciclaggioRefs,campiDinamiciRefs, groupsRefs];
            sections.forEach((ref) => {
                if (ref.current) {
                    observer.observe(ref.current);
                }
            });

            return () => {
                sections.forEach((ref) => {
                    if (ref.current) {
                        observer.unobserve(ref.current);
                    }
                });
            };
        } catch (error: any) {
            console.log("is error in here!!!!", error);
        }
    }, [setCurrentSection, datiGeneraliRef, recapitiRef, contactsRefs, noteprofilazioneRefs, cameraDiCommercioRefs, fiscaliBancheRefs, antiriciclaggioRefs,campiDinamiciRefs, groupsRefs, parentRef]);

    //defining padding for the last section so it can be shown full and be selected in the sidebar
    useEffect(() => {
        if (parentRef.current) {
            const lastSection = campiDinamiciRefs.current;
            if (lastSection) {
                const paddingBottom = lastSection.offsetHeight; // Extra buffer
                setBottomPadding(paddingBottom); // Adjust paddingBottom
            }
        }
    }, [campiDinamiciRefs, parentRef]);

  

    const scrollToDatiGenerale = () => {
        if (parentRef.current && datiGeneraliRef.current) {
            const parent = parentRef.current;
            const element = datiGeneraliRef.current;
            const parentTop = parent.getBoundingClientRect().top;
            const elementTop = element.getBoundingClientRect().top;
            const scrollTo = elementTop - parentTop + parent.scrollTop - 50; // Add an offset

            window.scrollTo({ top: scrollTo, behavior: "smooth" }); // Smooth scroll
        }
    };

    useEffect(() => {
        if (errors.subjectName || errors.subjectSurname) {
            scrollToDatiGenerale();
        }
    }, [errors]);

    return (
        <div
            style={{
                overflowY: "auto",
                paddingRight: "3rem",
                scrollbarWidth: "none",
                minHeight: "100vh",
                paddingBottom: `${bottomPadding}px` // Apply calculated padding
            }}
            ref={parentRef}
        >
            <div id="1" ref={datiGeneraliRef}>
                <DatiGeneraliForm 
                    anagraficheData={anagraficheData} 
                    method={method} t={t} 
                    isSociety={userType.parentSelected === CUSTOMER_TYPE.Societa} 
                    isPersonaFisica={userType.parentSelected === CUSTOMER_TYPE.PersonNumber} 
                    userType={userType} setUserType={setUserType}  
                    mandatoryFields={mandatoryFields}
                    optionalFields={optionalFields}
                />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            <div id="2" ref={recapitiRef}>
                <RecapitiForm anagraficheData={anagraficheData} method={method} />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            {userType.parentSelected !== CUSTOMER_TYPE.PersonNumber && (
                <>
                    <div id="5" ref={cameraDiCommercioRefs}>
                        <CammeraDiCommercioForm anagraficheData={anagraficheData} method={method} />
                    </div>

                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%"
                        }}
                    />
                </>
            )}
            <div id="4" ref={noteprofilazioneRefs}>
                <NoteProfilzaioneForm anagraficheData={anagraficheData} method={method} setSelectedTagsValue={setSelectedTagsValue} />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            <div id="6" ref={fiscaliBancheRefs}>
                <FisacliBancheForm anagraficheData={anagraficheData} method={method} />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            <div id="7" ref={antiriciclaggioRefs}>
                <AntiriciclaggioForm anagraficheData={anagraficheData} method={method} />
            </div>
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%"
                }}
            />
            <div id="8" ref={campiDinamiciRefs}>
                <CampiDinamiciForm anagraficheData={anagraficheData} method={method} />
            </div>
        </div>
    );
};

export default UserData;
