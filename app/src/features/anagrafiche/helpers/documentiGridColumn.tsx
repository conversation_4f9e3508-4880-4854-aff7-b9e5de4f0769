import {
    IGridColumn,
    IGridSettings,
} from "../../../interfaces/general.interfaces";
import BaseGridList from "../../../models/BaseGridList";
import { mapOtherList } from "../../../utilities/common";
import { Box } from "@mui/material";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faEye } from "@fortawesome/free-regular-svg-icons";
import { faDownload } from "@fortawesome/pro-regular-svg-icons";

export const getDocumentiGrid = async (
    t: any,
    handlePrintDocumenti: any,
    handlePreviewDocument: any
) => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [t("Nome file"), t("Descrizione"), t("Data"), t("")],
            column_keys: ["nomefile", "titolodocumento", "datadoc", "Azioni"],
            column_widths: ["25%", "25%", "25%", "10%"],
            cell_templates: [null, null, null, null],
            sortable: [true, false, true, false],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapDocumentiFieldColumnNames(
        response,
        t,
        handlePrintDocumenti,
        handlePreviewDocument
    );
};

export const mapDocumentiFieldColumnNames = (
    response: any,
    _t: any,
    handlePrintDocumenti: any,
    handlePreviewDocument: any
) => {
    const {
        column_names,
        column_keys,
        sortable,
        column_widths,
    }: IGridSettings = mapOtherList(response);

    let columns = column_names.map((cln: string, index: number) => {
        if (column_widths !== undefined && column_widths[index] !== "0%") {
            let returnColumn: any = [];
            returnColumn.field = column_keys[index];
            returnColumn.headerName = cln;
            let calc = parseInt(column_widths[index]) / 100;
            returnColumn.flex = Math.round(calc * 1e2) / 1e2;
            returnColumn.sortable = sortable[index];
            if (column_keys[index] === "Azioni") {
                returnColumn.renderCell = (row: any) =>
                    documentiIconField({
                        row,
                        handlePrintDocumenti,
                        handlePreviewDocument,
                    });
            } else {
                returnColumn.valueGetter = (row: any) => {
                    return row["value"];
                };
            }
            return returnColumn;
        }
    });
    columns = columns.filter(function (element: any) {
        return element !== undefined;
    });
    return columns as unknown as IGridColumn[];
};

// Documenti Field
const documentiIconField = ({
    row,
    handlePrintDocumenti,
    handlePreviewDocument,
}: {
    row: any;
    handlePrintDocumenti: any;
    handlePreviewDocument: any;
}) => {
    return (
        <Box
            sx={{
                display: "flex",
                justifyContent: "flex-end",
                alignItems: "center",
                gap: 1,
                width: "100%"
            }}
        >
            <FontAwesomeIcon
                icon={faDownload}
                style={{ color: "hsl(200, 100%, 42%)", padding: "10px" }}
                onClick={(e) => {
                    e.stopPropagation();
                    handlePrintDocumenti(row?.row?.id, row?.row?.nomefile);
                }}
            />
            <FontAwesomeIcon
                icon={faEye}
                style={{ color: "hsl(200, 100%, 42%)", padding: "10px" }}
                onClick={(e) => {
                    e.stopPropagation();
                    handlePreviewDocument(row?.row?.id, row?.row?.nomefile);
                }}
            />
        </Box>
    );
};
