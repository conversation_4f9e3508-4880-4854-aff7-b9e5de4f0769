import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import Typography from "@mui/material/Typography";

// Define your props interface if using TypeScript (optional)
// interface ContactHeaderProps {
//   icon: any; // or a more specific type if available
//   title: string;
//   iconStyle?: React.CSSProperties;
//   typographyProps?: React.ComponentProps<typeof Typography>;
// }

const ContentHeader = ({
    icon,
    title,
    iconStyle = {},
    typographyProps = {},
    hasError = false,
}: any) => {
    return (
        <>
            <FontAwesomeIcon
                size="lg"
                icon={icon}
                style={{
                    color: "primary.main",
                    marginRight: "8px",
                    marginTop: "8px",
                    ...iconStyle,
                }}
            />
            <Typography
                variant="titleMedium"
                color="primary.main"
                gutterBottom
                {...typographyProps}
            >
                {title} {hasError && <span style={{ color: "red" }}>* it has required fields</span>}
            </Typography>

        </>
    );
};

export default ContentHeader;
