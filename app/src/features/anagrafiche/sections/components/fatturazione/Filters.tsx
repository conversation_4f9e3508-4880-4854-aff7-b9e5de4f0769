import { Box, Button, FormControl, Select, MenuItem, InputLabel , Grid} from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { CustomSelect } from "../../../../../custom-components/CustomSelect";
import { DatePicker } from "../../../../../components/ui-kit/DatePicker";
import { useState } from "react";
import moment from "moment";
import { processDateValue } from "../../../../../helpers/dateHelper";

const cancelQueryButtonStyle = {
    marginTop: "auto",
    marginLeft: "1rem",
};

export default function Filters(props: any) {
    const { query, setQuery, defaultQuery, lawyers,feeStatus } = props;
    const [date, setDate] = useState({
        startDateSearch: "",
        endDateSearch: "",
     });
    const { t } = useTranslation();

    const onChangeFilterInputs = (e: any) => {
        setQuery((items: any) => ({
            ...items,
            [e.target.name]: e.target.value,
        }));
    };
    const onDateChange = (name: string, value: Date) => {
        setDate((prevValue: any) => ({ ...prevValue, [name]: moment(value).format("DD/MM/YYYY") }));
        setQuery((prevQuery: any) => ({...prevQuery,[name]: moment(value).format("DD/MM/YYYY")}))
      
    };
    const handleReset = () => {
        setDate({
            startDateSearch: "",
            endDateSearch: "",
        });
        setQuery({ ...defaultQuery,   tipologia: "preavvisiDiParcella",peopleId: query.peopleId });
    };

    return (
        <>
        <Box component="form" display="flex" alignItems="end" gap={1}>
                <Grid container spacing={2}>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    >
                    <FormControl variant="outlined" fullWidth>
                        <InputLabel>{t("Tipologia")}</InputLabel>
                        <Select
                            labelId="select-label"
                            value={query.tipologia}
                            onChange={onChangeFilterInputs}
                            name="tipologia"
                        >
                            <MenuItem value={"preavvisiDiParcella"}>{t("Preavvisi di parcella")}</MenuItem>
                            <MenuItem value={"fatture"}>{t("Fatture")}</MenuItem>
                            <MenuItem value={"fattureElettroniche"}>{t("Fatture elettroniche")}</MenuItem>
                            <MenuItem value={"noteDiCreditoElettroniche"}>{t("Note di credito elettroniche")}</MenuItem>
                        </Select>
                    </FormControl>
                    </Grid>
                    <Grid
                        item
                        xs={12}
                            sm={3}
                            md={3}
                        
                    >
                    <FormControl variant="outlined" fullWidth disabled={query.tipologia !== "preavvisiDiParcella"}>
                    <InputLabel>{t("Situazione preavviso")}</InputLabel>
                    <Select
                        labelId="select-label"
                        value={query.idFeeNoticeStatusSearch || -1}
                        onChange={onChangeFilterInputs}
                        name="idFeeNoticeStatusSearch" 
                    >
                        <MenuItem value={-1}>-</MenuItem>
                        {feeStatus?.map((fee: any) => (
                            <MenuItem key={fee?.id} value={fee?.id}>{fee?.nome}</MenuItem>
                        ))}
                    </Select>
                    </FormControl>

                    </Grid>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}     
                    >
                        <DatePicker
                            label={t("Periodo dal")}
                            value={processDateValue(date.startDateSearch)}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("startDateSearch", date);
                            }}
                        />
                    </Grid>
                      <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    >    
                        <DatePicker
                            label={t("Periodo al")}
                            value={processDateValue(date.endDateSearch)}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("endDateSearch", date);
                            }}
                            minDate={date.startDateSearch ? new Date(date.startDateSearch) : undefined}
                        
                /> </Grid>
                </Grid>
                 
        </Box>
              <Box component="form" display="flex" alignItems="end" gap={1} sx={{pt: 2}}>
                 <Grid container spacing={2}>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    >
                        <FormControl variant="outlined" fullWidth>
                        <InputLabel>{t("Stato")}</InputLabel>
                        <Select
                            labelId="select-label"
                            value={query.statusSearch || -1}
                            onChange={onChangeFilterInputs}
                            name="statusSearch"
                        >
                            <MenuItem value={-1}>{t("Tutti gli stati")}</MenuItem>
                            <MenuItem value={1}>{t("Da incassare")}</MenuItem>
                            <MenuItem value={2}>{t("Incassate")}</MenuItem>
                        </Select>
                        </FormControl>
                    </Grid>
                     <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    > 
                    <FormControl variant="outlined" fullWidth>
                    <InputLabel id='select-label'>{t("Emittente")}</InputLabel>
                    <CustomSelect       
                        value={query.emittenteSearch}
                        onChange={onChangeFilterInputs}
                        name='emittenteSearch'
                        group='attivo'
                        dataSource={lawyers}
                        valueKey='id'
                        
                    >
                        <MenuItem value={-1}>{t("Tutti gli avvocati")}</MenuItem>
                        {lawyers?.map((rec: any) => (
                            <MenuItem key={rec?.id} value={rec?.id}>{rec?.nome}</MenuItem>
                        ))}
                    
                    </CustomSelect>
                </FormControl></Grid>
                 <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        
                    > 
                      <FormControl variant="outlined" fullWidth>
                <InputLabel>{t("Tipo acconti")}</InputLabel>
                <Select
                    labelId="select-label"
                    value={query.advanceSearch || 0}
                    onChange={onChangeFilterInputs}
                    name="advanceSearch"
                >
                    <MenuItem value={0}>{t("Tutti")}</MenuItem>
                    <MenuItem value={"on"}>{t("Solo acconti")}</MenuItem>
                    
                </Select>
            </FormControl>
            </Grid>
                    <Grid
                        item
                        xs={12}
                        sm={3}
                        md={3}
                        style={{display: "flex", justifyContent: "flex-start" }}
                        
                    >
                    <Button  style={cancelQueryButtonStyle} onClick={handleReset}>
            {t("Annulla ricerca")}
            </Button>
            </Grid>
                </Grid>

        </Box>
        </>
    );
}
