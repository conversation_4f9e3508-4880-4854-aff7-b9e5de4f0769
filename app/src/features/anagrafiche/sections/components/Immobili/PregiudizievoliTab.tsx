
import { useTranslation } from "@1f/react-sdk";
import { <PERSON>, Grid, Divider, Button } from "@vapor/react-material";
import { faCirclePlus, faTrashCan } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import FormInput from "../../../../../custom-components/FormInput";
import { useFieldArray, useFormContext } from "react-hook-form";

import { DatePicker } from "../../../../../components/ui-kit/DatePicker";

const PregiudizievoliTab = () => {
    const { t } = useTranslation();
    const { control, setValue } = useFormContext();
    const { fields, append, remove } = useFieldArray({
        control,
        name: "pregiud",
    });

    const onAppendClick = () => {
       append({
            tipo: "",
            importo: "",
            grado: "",
            data: "",
            tassaiscrizione: "",
            note: "",
        })
    }

    

    return (
        <Box
            sx={{
                "& .MuiTextField-root": {
                    m: 1,
                    width: 1,
                },
            }}
        >
            {fields.map((field: any, index: number) => {
                return (
                    <div key={field.id}>
                        <Grid container spacing={2}>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`pregiud.${index}.tipo`}
                                    type="text"
                                    label={t("Tipo")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`pregiud.${index}.importo`}
                                    type="text"
                                    label={t("Importo")}
                                    formatNumber={true}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`pregiud.${index}.grado`}
                                    type="text"
                                    label={t("Grado")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <DatePicker
                                    label={t("Data")}
                                    value={field.data_pregiud}
                                    onChange={(value: Date | null) =>
                                        setValue(
                                            `pregiud.${index}.data_pregiud`,
                                            value
                                        )
                                    }
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`pregiud.${index}.tassaiscrizione`}
                                    formatNumber={true}
                                    type="text"
                                    label={t("Tassa iscrizione")}
                                />
                            </Grid>
                            <Grid item xs={6} md={6}>
                                <FormInput
                                    fullWidth
                                    control={control}
                                    name={`pregiud.${index}.note`}
                                    type="text"
                                    label={t("Note")}
                                />
                            </Grid>
                            {index > 0 && (
                                <>
                                    <Grid
                                        item
                                        xs={12}
                                        md={12}
                                        sx={{
                                            display: "flex",
                                            justifyContent: "flex-end",
                                        }}
                                    >
                                        <Button
                                            color="error"
                                            startIcon={
                                                <FontAwesomeIcon
                                                    style={{ color: "error" }}
                                                    icon={faTrashCan}
                                                />
                                            }
                                            onClick={() => remove(index)}
                                        >
                                            {t("Elimina")}
                                        </Button>
                                    </Grid>
                                </>
                            )}
                        </Grid>
                        <Divider
                            className="MuiDivider-VaporLight"
                            sx={{ width: "102%", mt: 2 }}
                        />
                    </div>
                );
            })}

            <Box sx={{ marginBottom: 2 }}>
                <Button
                    startIcon={
                        <FontAwesomeIcon
                            style={{
                                color: "#008FD6",
                            }}
                            icon={faCirclePlus}
                        />
                    }
                    sx={{ marginTop: 2 }}
                     onClick={onAppendClick}
                    
                >
                    {t("Aggiungi pregiudizievoli")}
                </Button>
            </Box>
        </Box>
    );
};

export default PregiudizievoliTab;
