import { useState } from "react";
import { GridPaginationModel } from "@mui/x-data-grid-pro";
import { Typography, <PERSON>, Button } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import usePostCustom from "../../../../hooks/usePostCustom";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import { useDocumentiHook } from "../../hooks/useDocumentiHook";
import { useNavigate } from "react-router-dom";
import { faFileUser } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import DocumentiPreview from "./DocumentiPreview";
import BaseModal from "../../../../custom-components/BaseModal";
import AnagraficaFileUpload from "./AnagraficaFileUpload";

interface QueryParams {
    peopleUniqueid: string;
    viewType: string;
    page: number;
    pageSize: number;
    sortColumn: string;
    sortOrder: "asc" | "desc";
}

const Documenti = ({ uniqueid, id }: any) => {
    const navigate = useNavigate();
    const [query, setQuery] = useState<QueryParams>({
        peopleUniqueid: uniqueid,
        viewType: "",
        page: 0,
        pageSize: 7,
        sortColumn: "id",
        sortOrder: "asc"
    });
    const [openModal, setOpenModal] = useState<boolean>(false);
    const [loader, setLoader] = useState<boolean>(false);
    const [files, setFiles] = useState<any>([]);
    const [docDescription, setDocDescription] = useState<any>([]);
    const [docDate, setDocDate] = useState<any>([]);

    const { t } = useTranslation();
    const { initDocumenti, columns, list, loading, previewOpen, setPreviewOpen, selectedDocumentId, selectedFile } = useDocumentiHook(query);

    const documentiUploadRequest = usePostCustom(`default/anagrafiche/upload-file?id=${id}`);

    const onPageChangeCallback = (model: GridPaginationModel) => {
        setQuery((prev) => ({ ...prev, page: model.page, pageSize: model.pageSize }));
    };

    const onFileUpload = (filesItems: any) => {
        setFiles((files: any) => [...files, ...filesItems]);
    };
    const onRemove = (fileIndex: any) => {
        setFiles((files: any) => files.filter((_: any, i: any) => i !== fileIndex));
    };

    const handleButtonClick = () => {
        setOpenModal(true);
    };

    const handleUploadFile = async () => {
        try {
            if (!files?.length) return;
            setLoader(true);
            let i = 0;
            for (const file of files) {
                const formData = new FormData();
                formData.append("files[]", file);
                formData.append("doc_description", docDescription[i]);
                formData.append("doc_date", docDate[i]);

                await documentiUploadRequest.doFetch(true, formData);
                i++;
            }

            setLoader(false);
            setOpenModal(false);
            setDocDescription([]);
            setDocDate([]);
            setFiles([]);
            initDocumenti();
        } catch (error) {
            console.error("Error during upload:", error);
        }
    };

    const onClickCallback = (id: string) => {
        navigate(`/legacy/anagrafiche/update-file?id=${id}`);
    };

    const renderDataTable = () => (
        <CustomDataGrid
            name="peopleuploadedfiles"
            columns={columns}
            data={list?.currentPage}
            page={query.page}
            totalRows={list?.totalRows}
            pageSize={query.pageSize}
            loading={loading}
            query={query}
            setQuery={setQuery}
            onClickCallback={onClickCallback}
            onPageChangeCallback={onPageChangeCallback}
            onClickKey="id"
        />
    );

    const handleDecline = () => {
        setOpenModal(false);
        setDocDescription([]);
        setDocDate([]);
        setFiles([]);
    };
    return (
        <Box>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between"
                }}
            >
                <Box sx={{ display: "flex", gap: 1 }}>
                    <FontAwesomeIcon fontSize={"24px"} icon={faFileUser} />
                    <Typography variant="titleMedium" color="primary.main" gutterBottom sx={{ marginLeft: "15px" }}>
                        {t("Lista file")}
                    </Typography>
                </Box>

                <Box sx={{ display: "flex", gap: 1 }}>
                    <Button variant="contained" size="small" onClick={handleButtonClick}>
                        {t("Carica file")}
                    </Button>
                </Box>
            </Box>
            <Box sx={{ pt: 10 }}>{renderDataTable()}</Box>
            <Box>
                <DocumentiPreview open={previewOpen} setOpen={setPreviewOpen} id={selectedDocumentId} fileName={selectedFile} />
            </Box>

            {openModal && (
                <BaseModal
                    open={openModal}
                    handleDecline={handleDecline}
                    decline={"Annulla"}
                    agree="Salva"
                    handleAgree={handleUploadFile}
                    content={
                        <AnagraficaFileUpload
                            setDocDescription={setDocDescription}
                            setDocDate={setDocDate}
                            docDescription={docDescription}
                            docDate={docDate}
                            files={files}
                            setFiles={setFiles}
                            onRemove={onRemove}
                            onFileUpload={onFileUpload}
                        />
                    }
                    loader={loader}
                    title={"CARICA NUOVO FILE"}
                    isFooter={true}
                />
            )}
        </Box>
    );
};

export default Documenti;
