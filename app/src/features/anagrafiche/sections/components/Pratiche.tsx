import { useState } from "react";
import { Box, Button, Menu, MenuItem, Typography } from "@vapor/v3-components";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBook, faChevronDown } from "@fortawesome/pro-regular-svg-icons";
import { CustomDataGrid } from "../../../../custom-components/CustomDataGrid";
import Spinner from "../../../../custom-components/Spinner";
import useCustomerFiles from "../../hooks/useCustomerFiles";
import { useNavigate } from "react-router-dom";
import {
    GridCallbackDetails,
    GridPaginationModel,
    GridRowSelectionModel,
} from "@mui/x-data-grid-pro";
import { useTranslation } from "@1f/react-sdk";

interface IProps {
    customer: any;
}

export default function Pratiche(props: IProps) {
    const { customer } = props;
    const { customersFileQuery, setCustomerFileQuery, loading, list } =
        useCustomerFiles();
    const navigate = useNavigate();
    const { t } = useTranslation();
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [disableDocumentoButton, setDisableDocumentoButton] =
        useState<boolean>(true);
    const open = Boolean(anchorEl);

    const onPageChangeCallback = (
        model: GridPaginationModel,
        _: GridCallbackDetails<any>
    ) => {
        setCustomerFileQuery({
            ...customersFileQuery,
            page: model.page,
            pageSize: model.pageSize,
        });
    };

    const onClickCallback = (uniqueid: any) => {
        navigate(`/archive/summary?uid=${uniqueid}`);
    };

    const navigateToPratica = () => {
        navigate("/legacy/archive/archive");
    };

    const navigateToNewPratica = () => {
        navigate("/legacy/archive/archive");
    };

    const navigateToFeesItems = () => {
        navigate(
            `/legacy/people-multifiles-fee/index?people=${customer.id}&referencesType=6`
        );
    };

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleRowSelection = (
        rowSelectionModel: GridRowSelectionModel,
        _details: GridCallbackDetails<any>
    ) => {
        setDisableDocumentoButton(rowSelectionModel.length > 0 ? false : true);
    };

    const renderDataTable = () => {
        if (loading) {
            return <Spinner fullPage={false} />;
        }
        return (
            <CustomDataGrid
                name="peopleArchive"
                columns={list.columns}
                data={list?.rows || []}
                page={list.page}
                totalRows={list.totalRows}
                pageSize={list.pageSize}
                selectableRows={true}
                onPageChangeCallback={onPageChangeCallback}
                onRowSelectionModelChange={handleRowSelection}
                onClickCallback={onClickCallback}
                query={customersFileQuery}
                setQuery={setCustomerFileQuery}
                onClickKey="uniqueid"
                onClickCheckboxKey="uniqueid"
            />
        );
    };

    return (
        <Box>
            <Box
                sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                <Box sx={{ display: "flex", gap: 1 }}>
                    <FontAwesomeIcon fontSize={"24px"} icon={faBook} />
                    <Typography
                        variant="titleMedium"
                        color="primary.main"
                        gutterBottom
                        sx={{ marginLeft: "15px" }}
                    >
                        {t("Pratiche")}
                    </Typography>
                </Box>

                <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                        variant="outlined"
                        color="primary"
                        size="small"
                        sx={{ textTransform: "none" }}
                        onClick={navigateToPratica}
                    >
                        {t("Vai a Pratiche")}
                    </Button>
                    <div>
                        <Button
                            aria-haspopup="true"
                            variant="contained"
                            color="primary"
                            size="small"
                            onClick={handleClick}
                            endIcon={
                                <FontAwesomeIcon
                                    icon={faChevronDown}
                                    size="sm"
                                />
                            }
                        >
                            {t("Azioni")}
                        </Button>
                        <Menu
                            MenuListProps={{
                                "aria-labelledby": "basic-button",
                            }}
                            anchorEl={anchorEl}
                            open={open}
                            id="basic-menu"
                            onClose={handleClose}
                        >
                            <MenuItem onClick={navigateToNewPratica}>
                                {t("Nuova pratica")}
                            </MenuItem>
                            <MenuItem
                                disabled={disableDocumentoButton}
                                onClick={navigateToFeesItems}
                            >
                                {t("Nuovo documento multipratica")}
                            </MenuItem>
                        </Menu>
                    </div>
                </Box>
            </Box>
            <Box sx={{ pt: 10 }}>{renderDataTable()}</Box>
        </Box>
    );
}
