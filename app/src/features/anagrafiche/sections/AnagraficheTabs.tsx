import {
    Box,
    Button,
    Checkbox,
    FormControl,
    FormControlLabel,
    InputLabel,
    MenuItem,
    Select,
    TextField,
} from "@vapor/react-material";
import React from "react";
import { Tab, Tabs } from "@vapor/react-extended";
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import { useTranslation } from "@1f/react-sdk";
import { a11yProps, CustomTabPanel } from "../../../helpers/customTabPanel";
import { RELATIONS_OPTIONS } from "../constants/constants";

export const AnagraficheTabs = (props: any) => {
    const { t } = useTranslation();
    const [value, setValue] = React.useState(0);

    const handleChange = (_event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    const {
        params: defaultParams,
        onChangeFunctions,
        categorie,
        assigneeList,
    } = props;

    const {
        onChangeCheckbox,
        onChangeInput,
        onClickReset,
        onSubmit,
        onDateChange,
    } = onChangeFunctions;

    return (
        <>
            <Box>
                <Tabs
                    value={value}
                    onChange={handleChange}
                    size="extraSmall"
                    variant="standard"
                >
                    <Tab label="Ricerca" {...a11yProps(0)} />
                    <Tab label="Ricerca per indirizi" {...a11yProps(1)} />
                </Tabs>
            </Box>
            <CustomTabPanel value={value} index={0}>
                <Box display="flex" alignItems="end" gap={2}>
                    <TextField
                        label="Ricerca:"
                        variant="outlined"
                        value={defaultParams.searchField}
                        placeholder="Ricerca"
                        name="searchField"
                        sx={{ width: 1 / 4 }}
                        onChange={onChangeInput}
                    />
                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <InputLabel id="select-label">
                            {t("Tutte le Relazioni")}
                        </InputLabel>
                        <Select
                            labelId="select-label"
                            value={defaultParams.searchRelation}
                            label={t("Tutte le Relazioni")}
                            onChange={onChangeInput}
                            name="searchRelation"
                        >
                            <MenuItem value="-1">
                                {t("Tutte le Relazioni")}
                            </MenuItem>
                            {RELATIONS_OPTIONS.map(
                                (relation: any, index: number) => (
                                    <MenuItem
                                        key={index}
                                        value={relation.value}
                                    >
                                        {relation.label}
                                    </MenuItem>
                                )
                            )}
                        </Select>
                    </FormControl>
                    <FormControl variant="outlined" sx={{ width: 1 / 4 }}>
                        <InputLabel id="select-label">
                            {t("Qualsiasi associazione")}
                        </InputLabel>
                        <Select
                            labelId="select-label"
                            value={defaultParams.searchAssociazione}
                            label={t("Tutte le Relazioni")}
                            onChange={onChangeInput}
                            name="searchAssociazione"
                        >
                            <MenuItem value={-1}>
                                {t("Tutte le categorie")}
                            </MenuItem>
                            {assigneeList?.map((category: any) => (
                                <MenuItem key={category.id} value={category.id}>
                                    {category.nomeutente}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <TextField
                        label={t("Tags:")}
                        variant="outlined"
                        value={defaultParams.searchTags}
                        placeholder="Tags"
                        name="searchTags"
                        sx={{ width: 1 / 4 }}
                        onChange={onChangeInput}
                    />
                </Box>
                <Box
                    display="flex"
                    alignItems="end"
                    gap={2}
                    style={{ marginTop: "7px" }}
                >
                    <FormControlLabel
                        sx={{ width: 1 / 7 }}
                        control={
                            <Checkbox
                                onChange={onChangeCheckbox}
                                color="primary"
                                name="searchTagsLike"
                                checked={!!defaultParams.searchTagsLike}
                            />
                        }
                        label={t("Cerca per 'contiene'")}
                    />
                    <div style={{ width: "25%" }}>
                        <DatePicker
                            label={t("Start Date")}
                            value={defaultParams.startAnagraficheSearchValue}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("startAnagraficheSearchValue", date);
                            }}
                        />
                    </div>
                    <div style={{ width: "25%" }}>
                        <DatePicker
                            label="End Date"
                            value={defaultParams.endAnagraficheSearchValue}
                            onChange={(date: Date | null) => {
                                if (date) onDateChange("endAnagraficheSearchValue", date);
                            }}
                        />
                    </div>
                    <FormControl variant="outlined" sx={{ width: 1 / 5 }}>
                        <InputLabel id="select-label">
                            {t("Tutte le categorie")}
                        </InputLabel>
                        <Select
                            labelId="select-label"
                            value={defaultParams.categoryId}
                            label={t("Tutte le categorie")}
                            onChange={onChangeInput}
                            name="categoryId"
                        >
                            <MenuItem value={-1}>
                                {t("Tutte le categorie")}
                            </MenuItem>
                            {categorie?.map((category: any) => (
                                <MenuItem key={category.id} value={category.id}>
                                    {category.nome}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                    <FormControlLabel
                        sx={{ width: 1 / 7 }}
                        control={
                            <Checkbox
                                onChange={onChangeCheckbox}
                                color="primary"
                                name="showAdvanceDynamic"
                                checked={!!defaultParams.showAdvanceDynamic}
                            />
                        }
                        label={t("Mostra Campi")}
                    />
                    <Button
                        variant="contained"
                        sx={{ width: 1 / 10 }}
                        color="primary"
                        type="submit"
                        onClick={onSubmit}
                    >
                        {t("Cerca")}
                    </Button>
                    <Button
                        variant="contained"
                        color="primary"
                        sx={{ width: 1 / 10 }}
                        onClick={onClickReset}
                    >
                        {t("Mostra tutti")}
                    </Button>
                </Box>
            </CustomTabPanel>
            <CustomTabPanel value={value} index={1}>
                <Box display="flex" alignItems="end" gap={2}>
                    <TextField
                        label={t("Nazione:")}
                        variant="outlined"
                        value={defaultParams.searchNation}
                        placeholder={t("Cerca nazione")}
                        name="searchNation"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />
                    <TextField
                        label={t("Città:")}
                        variant="outlined"
                        value={defaultParams.searchCity}
                        placeholder={t("Cerca città")}
                        name="searchCity"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Via:")}
                        variant="outlined"
                        value={defaultParams.searchWay}
                        placeholder={t("Cerca via")}
                        name="searchWay"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Cap:")}
                        variant="outlined"
                        value={defaultParams.searchCap}
                        placeholder={t("Cerca cap")}
                        name="searchCap"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Provincia:")}
                        variant="outlined"
                        value={defaultParams.searchProvince}
                        placeholder={t("Cerca provincia")}
                        name="searchProvince"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />

                    <TextField
                        label={t("Regione:")}
                        variant="outlined"
                        value={defaultParams.searchRegion}
                        placeholder={t("Cerca region")}
                        name="searchRegion"
                        sx={{ width: 1 / 3 }}
                        onChange={onChangeInput}
                    />
                    <Button
                        variant="contained"
                        color="primary"
                        type="submit"
                        onClick={onSubmit}
                    >
                        {t("Cerca")}
                    </Button>

                    <Button
                        variant="contained"
                        color="primary"
                        onClick={onClickReset}
                    >
                        {t("Mostra tutti")}
                    </Button>
                </Box>
            </CustomTabPanel>
        </>
    );
};
