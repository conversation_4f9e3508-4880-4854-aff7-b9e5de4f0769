import { useEffect } from "react";
import { Divider } from "@vapor/react-material";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import DatiGeneraliDetail from "./components/DatiGeneraliDetail";
import RecapitiDetail from "./components/RecapitiDetail";
import ContattiCollegatiDetail from "./components/ContatiCollegatiDetail";
import NoteProfilazioneDetail from "./components/NoteProfilazioneDetail";
import FiscaliBancheDetail from "./components/FiscaliBancheDetail";
import AntiriciclaggioDetail from "./components/AntiriciclaggioDetail";
import CameraDiCommercioDetail from "./components/CameraDiCommercioDetail";
// import GruppiDetail from "./components/GruppiDetail";
import DatiGeneraliForm from "../actions/components/DatiGeneraliForm";
import FisacliBancheForm from "../actions/components/FiscaliBancheForm";
import NoteProfilzaioneForm from "../actions/components/NoteProfilazioneForm";
import RecapitiForm from "../actions/components/RecapitiForm";
import CammeraDiCommercioForm from "../actions/components/CammeraDiCommercioForm";
// import GruppiForm from "../actions/components/GruppiForm";
import ContattiCollegatiForm from "../actions/components/ContatiCollegatiForm";
import AntiriciclaggioForm from "../actions/components/AntiriciclaggioForm";
import Spinner from "../../../custom-components/Spinner";
import useGetAnagraficheActionData from "./../hooks/useGetAnagraficheActionData";
import { useTranslation } from "@1f/react-sdk";
import useSaveAnagrafiche from "../hooks/useSaveAnagrafiche";
import { debounce } from "lodash"; // Ensure you have lodash installed
import { CUSTOMER_TYPE } from "../constants/anagraficheType";
import useGetRelationships from "../hooks/useGetRelationships";
import { useChangedKey } from "../hooks/useChangedKey";
import CampiDinamiciForm from "../actions/components/CampiDinamiciForm";
import CampiDinamiciDetail from "./components/CampiDinamiciDetail";

const UserData = ({
    refs,
    setCurrentSection,
    userType,
    setUserType,
    method,
    showCreateForm,
    setShowCreateForm,
    setBottomPadding,
}: any) => {
    const { isInitialized, anagrafiche } = useAnagraficheProvider();
    const { anagraficheData, isUpdate, uniqueId, setAngraficheData } =
        useGetAnagraficheActionData(); //data to populate the menus
    const { readContactData, setReadContactData } =
        useGetRelationships(showCreateForm); // passing showCreateForm to fetch the contacts fast as the anagrafiche contact
    const { onSubmitCreate, mandatoryFields } = useSaveAnagrafiche(uniqueId);


    const { t } = useTranslation();
    const changedKey = useChangedKey(showCreateForm);

    const { setError, formState: { errors } } = method;

      useEffect(() => {
        if (mandatoryFields && Object.keys(mandatoryFields).length > 0) {
            Object.keys(mandatoryFields).forEach((fieldName) => {
                setError(fieldName, {
                    type: "manual",
                    message: mandatoryFields[fieldName]
                });
            });
        }
    }, [mandatoryFields, setError]);

    const debouncedHandleIntersection = debounce(
        (entries: any, setCurrentSection: any) => {
            entries.forEach((entry: any) => {
                if (entry.isIntersecting) {
                    setCurrentSection(entry.target.id);
                }
            });
        },
        100
    ); // Adjust

    const { tags } = anagrafiche;

    const {
        datiGeneraliRef,
        recapitiRef,
        contactsRefs,
        noteprofilazioneRefs,
        cameraDiCommercioRefs,
        fiscaliBancheRefs,
        antiriciclaggioRefs,
        campiDinamiciRefs,
        groupsRefs,
        parentRef,
    } = refs;

    useEffect(() => {
        try {
            const observer = new IntersectionObserver(
                (entries) =>
                    debouncedHandleIntersection(entries, setCurrentSection),
                {
                    root: null,
                    threshold: [0.1, 0.3, 0.5, 0.7, 1.0], // Adjust based on section sizes
                    rootMargin: "-50px 0px -50% 0px",
                }
            );

            const sections = [
                datiGeneraliRef,
                recapitiRef,
                contactsRefs,
                noteprofilazioneRefs,
                cameraDiCommercioRefs,
                fiscaliBancheRefs,
                antiriciclaggioRefs,
                campiDinamiciRefs,
                groupsRefs,
            ];
            sections.forEach((ref) => {
                if (ref.current) {
                    observer.observe(ref.current);
                }
            });

            return () => {
                sections.forEach((ref) => {
                    if (ref.current) {
                        observer.unobserve(ref.current);
                    }
                });
            };
        } catch (error: any) {
            console.log("is error in here!!!!", error);
        }
    }, [
        setCurrentSection,
        datiGeneraliRef,
        recapitiRef,
        contactsRefs,
        noteprofilazioneRefs,
        cameraDiCommercioRefs,
        fiscaliBancheRefs,
        antiriciclaggioRefs,
        campiDinamiciRefs,
        groupsRefs,
        parentRef,
    ]);

    //defining padding for the last section so it can be shown full and be selected in the sidebar
    useEffect(() => {
        if (parentRef.current) {
            const lastSection = campiDinamiciRefs.current;
            if (lastSection) {
                const paddingBottom = lastSection.offsetHeight; // Extra buffer
                setBottomPadding(paddingBottom);
            }
        }
    }, [campiDinamiciRefs, parentRef]);

    useEffect(() => {
        const sectionRefs: Record<string, React.RefObject<HTMLDivElement>> = {
            datiGenerali: datiGeneraliRef,
            recapiti: recapitiRef,
            contactiCollegati: contactsRefs,
            noteProfilazione: noteprofilazioneRefs,
            cameraDiCommercio: cameraDiCommercioRefs,
            fiscaliBanche: fiscaliBancheRefs,
            antiriciclaggio: antiriciclaggioRefs,
            campiDinamici: campiDinamiciRefs,
            gruppi: groupsRefs,
        };

        if (changedKey && sectionRefs[changedKey]?.current) {
            const currentElement = sectionRefs[changedKey]?.current;
            if (currentElement) {
                currentElement.scrollIntoView({
                    behavior: "smooth",
                    block: "center", // Scrolls the element to the vertical center of the viewport. if removed it will scroll the element to top of it.
                });
            }
        }
    }, [changedKey, parentRef]);

    if (!isInitialized) {
        return <Spinner />;
    }

    return (
        <>
            {showCreateForm.datiGenerali ? (
                <div id="1" ref={datiGeneraliRef}>
                    <DatiGeneraliForm
                        t={t}
                        method={method}
                        anagraficheData={anagraficheData}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        //onSubmitUpdate={onSubmitCreate}
                        mandatoryFields={mandatoryFields}
                        isSociety={
                            userType.parentSelected === CUSTOMER_TYPE.Societa
                        }
                        isPersonaFisica={
                            userType.parentSelected ===
                            CUSTOMER_TYPE.PersonNumber
                        }
                        userType={userType}
                        setUserType={setUserType}

                    />
                </div>
            ) : (
                <div id="1" ref={datiGeneraliRef}>
                    <DatiGeneraliDetail
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        isPersonaFisica={
                            userType.parentSelected ===
                            CUSTOMER_TYPE.PersonNumber
                        }
                        hasError={!!errors?.partitaiva || !!errors?.codicefiscale}
                    />
                </div>
            )}
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%",
                }}
            />
            {showCreateForm.recapiti ? (
                <div id="2" ref={recapitiRef}>
                    <RecapitiForm
                        anagraficheData={anagraficheData}
                        method={method}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        onSubmitUpdate={onSubmitCreate}
                    />
                </div>
            ) : (
                <div id="2" ref={recapitiRef}>
                    <RecapitiDetail
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                    />
                </div>
            )}
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%",
                }}
            />
            {userType.parentSelected !== CUSTOMER_TYPE.Person &&
                (showCreateForm.contactiCollegati ? (
                    <div id="3" ref={contactsRefs}>
                        <ContattiCollegatiForm
                            anagraficheData={anagraficheData}
                            setAngraficheData={setAngraficheData}
                            t={t}
                            showCreateForm={showCreateForm}
                            contactData={readContactData}
                            setReadContactData={setReadContactData}
                            setShowCreateForm={setShowCreateForm}
                        />{" "}
                    </div>
                ) : (
                    <div id="3" ref={contactsRefs}>
                        <ContattiCollegatiDetail
                            showCreateForm={showCreateForm}
                            setShowCreateForm={setShowCreateForm}
                            contactData={readContactData}
                            isInitialized={isInitialized}
                        />{" "}
                    </div>
                ))}

            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%",
                }}
            />
            {/* *** The backend for the gruppi section is not yet developed. SECTION IN PENDING *** */}
            {/* {userType.parentSelected !== CUSTOMER_TYPE.Person && (
                <>
                    {showCreateForm.gruppi ? (
                        <div id="9" ref={groupsRefs}>
                            <GruppiForm
                                anagraficheData={anagraficheData}
                                isUpdate={isUpdate}
                                method={method}
                                t={t}
                                showCreateForm={showCreateForm}
                                setShowCreateForm={setShowCreateForm}
                            />
                        </div>
                    ) : (
                        <div id="9" ref={groupsRefs}>
                            <GruppiDetail
                                showCreateForm={showCreateForm}
                                setShowCreateForm={setShowCreateForm}
                            />
                        </div>
                    )}
                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%",
                        }}
                    />
                </>
            )} */}
            {userType.parentSelected !== CUSTOMER_TYPE.PersonNumber && (
                <>
                    {showCreateForm.cameraDiCommercio ? (
                        <div id="5" ref={cameraDiCommercioRefs}>
                            <CammeraDiCommercioForm
                                anagraficheData={anagraficheData}
                                method={method}
                                isUpdate={isUpdate}
                                showCreateForm={showCreateForm}
                                setShowCreateForm={setShowCreateForm}
                                onSubmitUpdate={onSubmitCreate}
                            />
                        </div>
                    ) : (
                        <div id="5" ref={cameraDiCommercioRefs}>
                            <CameraDiCommercioDetail
                                showCreateForm={showCreateForm}
                                setShowCreateForm={setShowCreateForm}
                            />
                        </div>
                    )}
                    <Divider
                        light
                        style={{
                            marginBottom: "25px",
                            marginTop: "25px",
                            width: "105%",
                        }}
                    />
                </>
            )}

            {showCreateForm.noteProfilazione ? (
                <div id="4" ref={noteprofilazioneRefs}>
                    <NoteProfilzaioneForm
                        anagraficheData={anagraficheData}
                        method={method}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        activeTags={tags}
                        onSubmitUpdate={onSubmitCreate}
                    />
                </div>
            ) : (
                <div id="4" ref={noteprofilazioneRefs}>
                    <NoteProfilazioneDetail
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                    />
                </div>
            )}

            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%",
                }}
            />
            {showCreateForm.fiscaliBanche ? (
                <div id="6" ref={fiscaliBancheRefs}>
                    <FisacliBancheForm
                        anagraficheData={anagraficheData}
                        method={method}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        onSubmitUpdate={onSubmitCreate}
                    />
                </div>
            ) : (
                <div id="6" ref={fiscaliBancheRefs}>
                    <FiscaliBancheDetail
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        hasError={!!errors?.codiceb2b}
                    />
                </div>
            )}
            <Divider
                light
                style={{
                    marginBottom: "25px",
                    marginTop: "25px",
                    width: "105%",
                }}
            />
            {showCreateForm.antiriciclaggio ? (
                <div id="7" ref={antiriciclaggioRefs}>
                    <AntiriciclaggioForm
                        method={method}
                        anagraficheData={anagraficheData}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        onSubmitUpdate={onSubmitCreate}
                    />
                </div>
            ) : (
                <div id="7" ref={antiriciclaggioRefs}>
                    <AntiriciclaggioDetail
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                    />
                </div>
            )}

             {showCreateForm.campiDinamici ? (
                <div id="8" ref={campiDinamiciRefs}>
                    <CampiDinamiciForm
                        method={method}
                        anagraficheData={anagraficheData}
                        isUpdate={isUpdate}
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                        onSubmitUpdate={onSubmitCreate}
                    />
                </div>
            ) : (
                <div id="8" ref={campiDinamiciRefs}>
                    <CampiDinamiciDetail
                        showCreateForm={showCreateForm}
                        setShowCreateForm={setShowCreateForm}
                    />
                </div>
            )}
        </>
    );
};

export default UserData;
