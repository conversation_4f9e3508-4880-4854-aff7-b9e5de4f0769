import { Box, Typography, Divider } from "@vapor/react-material";
import { useTranslation } from "@1f/react-sdk";
import { faBuilding } from "@fortawesome/pro-regular-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import PersonOutlineIcon from "@mui/icons-material/PersonOutline";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import DetailList from "../../../custom-components/DetailList";
import DetailSidebar from "../../../custom-components/SidebarDetail";
import Spinner from "../../../custom-components/Spinner";
import moment from "moment";
import {
    faFileSignature,
    faHouse,
    faArrowRight,
    faBook,
    faFileUser,
    faFileInvoiceDollar,
} from "@fortawesome/pro-regular-svg-icons";
import { CUSTOMER_TYPE } from "../constants/anagraficheType";
import { useCardData } from "../helpers/typologyData";
import { useConfigs } from "../../../store/ConfigStore";
import useGetAnagraficheActionData from "../hooks/useGetAnagraficheActionData";

const getInitials = (customerName: string): string => {
    return customerName
        ?.split(" ")
        ?.map((part) => part?.charAt(0)?.toUpperCase())
        ?.join("");
};

const AnagraficheSidebar = ({
    refs,
    scrollToSection,
    currentSection,
    userType,
}: any) => {
    const { t } = useTranslation();
    const { anagraficheData } = useGetAnagraficheActionData(); //data to populate the menus
    const cardData = useCardData(anagraficheData);
    const { configs }: any = useConfigs();
    const { isInitialized, anagrafiche } = useAnagraficheProvider();
    const { customer } = anagrafiche;

    const getIconTitle = () => {
        if (userType.parentSelected === CUSTOMER_TYPE.Societa) {
            return (
                <FontAwesomeIcon
                    icon={faBuilding}
                    style={{ color: "hsl(200, 100%, 23%)", marginRight: "7px" }}
                />
            );
        }

        return (
            <PersonOutlineIcon
                style={{ color: "hsl(200, 100%, 23%)", marginRight: "7px" }}
            />
        );
    };

    const datiList = [
        { id: "1", label: t("Dati Generali"), ref: refs.datiGeneraliRef },
        { id: "2", label: t("Recapiti"), ref: refs.recapitiRef },
        { id: "3", label: t("Contatti collegati"), ref: refs.contactsRefs },
        {
            id: "4",
            label: t("Note e profilazione"),
            ref: refs.noteprofilazioneRefs,
        },

        { id: "6", label: t("Fiscali e Banche"), ref: refs.fiscaliBancheRefs },
        { id: "7", label: t("Antiriciclagio"), ref: refs.antiriciclaggioRefs },
        { id: "8", label: t("Campi Dinamici"), ref: refs.campiDinamiciRefs },
    ];
    if (userType.parentSelected !== CUSTOMER_TYPE.PersonNumber) {
        datiList.splice(3, 0, {
            id: "5",
            label: t("Camera di Commercio"),
            ref: refs.cameraDiCommercioRefs,
        });
    }

    const actionList = [
        {
            id: "10",
            label: t("Pratiche"),
            startIcon: faBook,
            endIcon: faArrowRight,
            ref: refs.praticheRefs,
        },
        configs?.data.modules?.contracts_enabled && {
            id: "11",
            label: t("Contratti"),
            startIcon: faFileSignature,
            endIcon: faArrowRight,
            ref: refs.contrattiRefs,
        },
        {
            id: "12",
            label: t("Lista file"),
            startIcon: faFileUser,
            endIcon: faArrowRight,
            ref: refs.documentiRefs,
        },
        {
            id: "13",
            label: t("Immobili"),
            startIcon: faHouse,
            endIcon: faArrowRight,
            ref: refs.immobiliRefs,
        },
        {
            id: "14",
            label: t("Fatture"),
            startIcon: faFileInvoiceDollar,
            endIcon: faArrowRight,
            ref: refs.fatturazioneRefs,
        },
    ].filter(Boolean);

    const childrenTypologyData = cardData.reduce((acc: any, tipologia: any) => {
        if (tipologia.title === userType.type) {
            return tipologia.details.find(
                (item: any) => item.id === userType.detailSelected
            );
        }
        return acc;
    }, null);

    if (!isInitialized) {
        return <Spinner />;
    }

    function capitalizeWords(str: string) {
        if (str) {
            return str
                .toLowerCase()
                .split(" ")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ");
        }
    }

    return (
        <DetailSidebar
            badgeTitle={capitalizeWords(userType?.type)}
            childrenBadgeTitle={capitalizeWords(childrenTypologyData?.name)}
            iconTitle={getIconTitle()}
            title={customer?.nome}
            idLabel={t("ID")}
            id={customer.id}
            subtitle={customer.partitaiva}
            subtitleLabel={t("P.IVA")}
        >
            <Divider light style={{ width: "95%" }} />
            <DetailList
                title={t("Dati")}
                list={datiList}
                scrollToSection={scrollToSection}
                currentSection={currentSection}
            />
            <Divider light style={{ width: "95%" }} />
            <DetailList
                title={t("Lista")}
                list={actionList}
                scrollToSection={scrollToSection}
                currentSection={currentSection}
            />
            <Divider light style={{ width: "95%" }} />
            <Box sx={{ mt: 2 }}>
                {customer.modificatoda && customer.modificatoil && (
                    <Typography
                        variant="bodySmall"
                        gutterBottom
                        component="div"
                    >
                        {t("Ultima Modifica")}:{" "}
                        {getInitials(customer.modificatoda)}{" "}
                        {moment(customer.modificatoil).format(
                            "DD/MM/YYYY HH:mm"
                        )}
                    </Typography>
                )}
            </Box>
        </DetailSidebar>
    );
};

export default AnagraficheSidebar;
