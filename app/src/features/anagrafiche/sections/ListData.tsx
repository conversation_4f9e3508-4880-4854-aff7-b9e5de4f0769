import React, { useEffect } from "react";
import <PERSON>ratiche from "./components/Pratiche";
import { useConfigs } from "../../../store/ConfigStore";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import Documenti from "./components/Documenti";
import Immobili from "./components/Immobili/Immobili";
import Fatturazione from "./components/fatturazione/Fatturazione"

interface IProps {
    refs: any;
    currentSection: string;
    setCurrentSection: React.Dispatch<React.SetStateAction<any>>;
}

export default function ListData(props: IProps) {
    const { refs, currentSection, setCurrentSection } = props;
    const { anagrafiche } = useAnagraficheProvider();
    const { customer } = anagrafiche;
    const { configs }: any = useConfigs();

    useEffect(() => {
        if (refs.parentRef.current) {
            setCurrentSection(currentSection);
        }
    }, [
        refs.parentRef,
        refs.praticheRefs,
        refs.contrattiRefs,
        refs.documentiRefs,
        refs.fatturazioneRefs,
        refs.immobiliRefs,
        currentSection,
        setCurrentSection,
    ]);

    return (
        <>
            <div id="10" ref={refs.praticheRefs}>
                {currentSection === "10" && <Pratiche customer={customer}/>}
            </div>
            <div id="11" ref={refs.contrattiRefs}>
                {currentSection === "11" &&
                    configs?.data.modules?.contracts_enabled && (
                        <h1>Contratti</h1>
                    )}
            </div>
            <div id="12" ref={refs.documentiRefs}>
                {currentSection === "12" && (
                    <Documenti
                        uniqueid={customer?.uniqueid}
                        id={customer?.id}
                    />
                )}
            </div>
            <div id="13" ref={refs.immobiliRefs}>
                {currentSection === "13" && (
                    <Immobili />
                )}
            </div>
            <div id="14" ref={refs.fatturazioneRefs}>
                {currentSection === "14" && (
                    <Fatturazione anagrafiche={anagrafiche} id={customer?.id} />
                )}
            </div>
        </>
    );
}
