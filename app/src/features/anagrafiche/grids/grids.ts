import BaseGridList from "../../../models/BaseGridList";
import { IGridColumn } from "../../../interfaces/general.interfaces";
import { mapOtherColumnNames } from "../../../utilities/common";

export const getPraticheGrid = async (t: any): Promise<IGridColumn[]> => {
    const response = BaseGridList.fromJson({
        gridsSettings: {
            column_names: [
                t("Codice"),
                t("Nome pratica"),
                t("Cliente"),
                t("Oggetto"),
                t("R.G."),
                t("Responsabile"),
                t("Stato"),
            ],
            column_keys: [
                "codicepratica",
                "codicearchivio",
                "cliente",
                "oggetto",
                "rg",
                "relazione",
                "stato",
            ],
            column_widths: [
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
                "10%",
            ],
            sortable: [true, true, true, false, false],
            header_templates: "",
            column_totals: null,
        },
    });
    return mapOtherColumnNames(response);
};
