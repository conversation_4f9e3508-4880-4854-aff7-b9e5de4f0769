import { useForm } from "react-hook-form";
import { useEffect, useState } from "react";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import useGetAnagraficheActionData from "./useGetAnagraficheActionData";
import { DEFAULT_ANAGRAFICHE_PARAMS } from "../constants/defaultAnagraficheParams";
import { CUSTOMER_TYPE } from "../constants/anagraficheType";

const validationSchema1 = yup.object().shape({
    subjectSurname: yup.string().required("Cognome obbligatorio"),
    subjectName: yup.string().required("Nome obbligatorio"),
    
});

const validationSchema2 = yup.object().shape({
    nome: yup.string().required("Denominazione obbligatoria"),
});

interface ShowCreateFormType {
    datiGenerali: boolean;
    recapiti: boolean;
    indirizzo: boolean;
    contactiCollegati: boolean;
    noteProfilazione: boolean;
    cameraDiCommercio: boolean;
    fiscaliBanche: boolean;
    antiriciclaggio: boolean;
    gruppi: boolean;
};


export const useAnagraficheFormHook = (peopleData?: any, isUpdateBool?: boolean) => {
    const { anagraficheData, isUpdate } = (!peopleData || typeof isUpdateBool === "undefined")
        ? useGetAnagraficheActionData()
        : { anagraficheData: peopleData, isUpdate: isUpdateBool };
    const { province, contacts } = anagraficheData;
    const [firstStepCompleted, setFirstStepCompleted] =
        useState<boolean>(false);
    const [showCreateForm, setShowCreateForm] = useState<ShowCreateFormType>({
        datiGenerali: false,
        recapiti: false,
        indirizzo: false,
        contactiCollegati: false,
        noteProfilazione: false,
        cameraDiCommercio: false,
        fiscaliBanche: false,
        antiriciclaggio: false,
        gruppi: false,
    });
    const [userType, setUserType] = useState<{
        type: string;
        icon: null | any;
        details: any;
        detailSelected: string;
        parentSelected: string;
    }>({
        icon: null,
        type: "",
        details: [],
        detailSelected: "",
        parentSelected: "",
    });

    const form = useForm({
        resolver:
            firstStepCompleted || showCreateForm.datiGenerali
                ? yupResolver<any>(
                    userType.type !== CUSTOMER_TYPE.Person
                        ? validationSchema2
                        : validationSchema1
                )
                : undefined,
        defaultValues: DEFAULT_ANAGRAFICHE_PARAMS,
    });

    const {
        handleSubmit,
        control,
        register,
        setValue,
        watch,
        setError,
        formState: { errors },
    } = form;

    const { additional_addresses, additional_phones } = anagraficheData;

    const newData = anagraficheData?.person
        ? Object.keys(anagraficheData.person).length > 0
        : 0;

    useEffect(() => {
        if (
            isUpdate &&
            anagraficheData?.person &&
            Object.keys(anagraficheData.person).length > 0
        ) {
            setPersonValues();
        }
    }, [isUpdate, newData]);

    const setPersonValues = () => {
        Object.keys(anagraficheData?.person).forEach((key: string) => {
            setValue(key, anagraficheData.person[key]);
            if (key === "aui") {
                setAuiValues(anagraficheData.person[key]);
            }
        });
        setNoteProfilazioneValues(anagraficheData.person);
        setRecapitiContacts();
        setRecapitiPhones();
    };

    const setRecapitiPhones = () => {
        const arrayData = Object.entries(additional_phones).map(
            ([key, value]: any[]) => {
                const contactData: any = contacts.find(
                    (item: any) => item.nome === value.nome_campo
                );
                return {
                    contact_id: key,
                    nome_campo: contactData?.id,
                    valore: value.valore,
                    nome: contactData?.nome,
                };
            }
        );
        setValue("contacts", arrayData);
    };

    const setRecapitiContacts = () => {
        const arrayData = Object.entries(additional_addresses).map(
            ([key, value]: any[]) => {
                // const regioneId: any = cities.find(
                //     (item: any) =>
                //         item.nome === value?.valore?.regione?.toUpperCase()
                // );

                const provinceId: any = province?.find(
                    (item: any) => item?.iniziali === value?.provincia
                );

                return {
                    address_id: key,
                    nome_campo: value.nome_campo,
                    provincia: provinceId?.id,
                    via: value.valore.via,
                    cap: value.valore.cap,
                    citta: value.valore.citta,
                    nazione: value.valore.nazione,
                    regione: value.valore.regione,
                };
            }
        );
        setValue("addresses", arrayData);
    };

    const setAuiValues = (auiData: any) => {
        if (auiData) {
            Object.keys(auiData).forEach((auiKey: string) => {
                setValue(auiKey, auiData[auiKey]);
                if (auiKey === "data_rilascio") {
                    setValue(auiKey, auiData[auiKey]);
                }
            });
        }
    };

    const setNoteProfilazioneValues = (noteData: any) => {
        Object.keys(noteData).forEach((key) => {
            if (key === "codice_esterno") {
                setValue("externalcode", anagraficheData.person.codice_esterno);
            }
        });
    };

    return {
        handleSubmit,
        control,
        register,
        setValue,
        watch,
        setError,
        errors,
        method: form,
        showCreateForm,
        setShowCreateForm,
        firstStepCompleted,
        setFirstStepCompleted,
        userType,
        setUserType,
    };
};
