import usePostCustom from "../../../hooks/usePostCustom";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import { useState } from "react";
export default function useSaveAnagrafiche(id?: string | undefined | null) {
    const { fetchAnagrafiche } = useAnagraficheProvider();

    const [mandatoryFields, setMandatoryFields] = useState<any>({});
    const [optionalFields, setOptionalFields] = useState<any>({});
    const url: string = id
        ? `anagrafiche/update?id=${id}`
        : "anagrafiche/update";

    const submitRequest = usePostCustom(url);

    const checkDataRequest = usePostCustom("/anagrafiche/checkData?noTemplateVars=true");


    const checkData = async (data: any) => {
        const formData = new FormData();
        
        // Add mandatory fields
        if (data.categoria) {
            formData.append('mandatory[categoria]', data.categoria);
        }
        if (data.codicefiscale !== undefined) {
            formData.append('mandatory[codicefiscale]', data.codicefiscale || '');
        }
        if (data.codiceb2b !== undefined) {
            formData.append('mandatory[codiceb2b]', data.codiceb2b || '');
        }
        if (data.partitaiva !== undefined) {
            formData.append('mandatory[partitaiva]', data.partitaiva || '');
        }
        
        // Add optional fields
        if (data.codicefiscale !== undefined) {
            formData.append('optional[codicefiscale]', data.codicefiscale || '');
        }
        if (data.id !== undefined) {
            formData.append('optional[id]', data.id || '');
        }
        if (data.codicedestinatario !== undefined) {
            formData.append('optional[codicedestinatario]', data.codicedestinatario || '');
        }
        if (data.partitaiva !== undefined) {
            formData.append('optional[partitaiva]', data.partitaiva || '');
        }
        
        const response: any = await checkDataRequest.doFetch(true, formData);
        setOptionalFields(response?.data?.optional);
        return response.data;
    }

    const onSubmitCreate = async (data: any) => {
        if(!id){
            const checkDataResponse: any = await checkData(data);
            const {dyphone_7, ...restValidData} = checkDataResponse?.mandatory || {};
            setMandatoryFields(restValidData);
            if(checkDataResponse?.mandatory && Object.keys(checkDataResponse.mandatory).length > 0 ) {
                return {
                    status: "error",
                    message: "Mandatory fields are missing",
                    mandatoryFields: checkDataResponse.mandatory,
                    optionalFields: checkDataResponse.optional
                }
            }
        }
        const transformAddresses = (addresses: any[]) => {
            const result: any = {};
            addresses.forEach((address, index) => {
                Object.keys(address).forEach((key: string) => {
                    if (address.address_id !== null) {
                        result[`dyaddress_${address.address_id}_${key}`] =
                            address[key] || "";
                    } else {
                        result[`dyaddress_${index}_${key}`] =
                            address[key] || "";
                    }
                });
            });
            return result;
        };

        const transformContacts = (contacts: any[]) => {
            const result: any = {};
            contacts.forEach((contact) => {
                Object.keys(contact).forEach((key: string) => {
                    if (key === "nome_campo") {
                        result[`dyphone_${contact.nome_campo}`] =
                            contact["valore"] || "";
                    } else {
                        result["contact"] = contact["nome"] || "";
                    }
                });
            });
            return result;
        };

        if (Array.isArray(data.addresses)) {
            const transformedAddresses = transformAddresses(data.addresses);
            data = { ...data, ...transformedAddresses };
            delete data.addresses;
        }
        if (Array.isArray(data.contacts)) {
            const transformedContacts = transformContacts(data.contacts);
            data = { ...data, ...transformedContacts };
            delete data.contacts;
        }
        data.title = data.titolo;
        if (data.subjectName && data.subjectSurname) {
            data.nome = `${data.subjectName} ${data.subjectSurname}`;
        }
        if (data.formula_saluti) {
            data.formula = data.formula_saluti;
        }

        const response: any = await submitRequest.doFetch(true, data);
        if (response) {
            fetchAnagrafiche(); // update data after submit
        }

        return {
            status: response?.status,
            anagraficaUuid: response?.data?.anagraficaUuid,
        };
    };

    return { onSubmitCreate, mandatoryFields, optionalFields, checkData };
}
