import { useEffect, useState } from "react";
import useGetCustom from "../../../hooks/useGetCustom";
import { useAnagraficheProvider } from "../providers/AnagraficheProvider";
import { getPraticheGrid } from "../grids/grids";
import { useTranslation } from "@1f/react-sdk";
import { IList } from "../../../interfaces/general.interfaces";

const DEFAULT_QUERY = {
    peopleUniqueid: "",
    page: 0,
    pageSize: 7,
    sortColumn: "codicepratica",
    sortOrder: "asc",
};

export default function useCustomerFiles() {
    const { t } = useTranslation();
    const listCustomerFilesRequest = useGetCustom(
        "customersfiles/list?noTemplateVars=true"
    );
    const { anagrafiche } = useAnagraficheProvider();
    const { customer } = anagrafiche;

    const [customersFileQuery, setCustomerFileQuery] = useState({
        ...DEFAULT_QUERY,
    });

    const [list, setList] = useState<IList<any>>({
        rows: [],
        columns: [],
        totalRows: 0,
        pageIndex: 0,
        pageSize: 10,
    });

    const fetchData = async () => {
      const updatedQuery = {
        ...customersFileQuery,
        peopleUniqueid: customer?.uniqueid || ""
      }
        const [columns, response]: any = await Promise.all([
            getPraticheGrid(t),
            listCustomerFilesRequest.doFetch(true, updatedQuery),
        ]);

        const { currentPage, totalRows } = response.data;
        console.log('currentPage', currentPage)

        setList({
            ...list,
            rows: currentPage || [],
            columns: columns,
            totalRows: parseInt(totalRows),
            page: customersFileQuery?.page,
            pageSize: customersFileQuery?.pageSize,
        });
    };

    useEffect(() => {
        fetchData();
    }, [customersFileQuery]);

    return {
        customersFileQuery,
        setCustomerFileQuery,
        loading: listCustomerFilesRequest.loading,
        list
    };
}
