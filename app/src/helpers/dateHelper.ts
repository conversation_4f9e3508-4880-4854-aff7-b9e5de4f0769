/**
 * Helper utility for date processing and conversion
 * Used to standardize date handling across DatePicker components
 */

/**
 * Processes various date formats and converts them to Date objects
 * @param val - The value to process (Date, string, null, undefined)
 * @returns Date object or null
 */
export const processDateValue = (val: any): Date | null => {
    if (!val) return null;
    
    // If already a Date object, return as is if valid
    if (val instanceof Date) {
        return isNaN(val.getTime()) ? null : val;
    }
    
    // If it's a string, try to parse it
    if (typeof val === 'string') {
        // Try parsing DD/MM/YYYY format first (common in the app)
        if (val.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
            const [day, month, year] = val.split('/').map(Number);
            const date = new Date(year, month - 1, day);
            return isNaN(date.getTime()) ? null : date;
        }
        
        // Try ISO format or other standard formats
        const parsed = new Date(val);
        return isNaN(parsed.getTime()) ? null : parsed;
    }
    
    return null;
};

/**
 * Formats a Date object to DD/MM/YYYY string format
 * @param date - Date object to format
 * @returns Formatted string or empty string if invalid
 */
export const formatDateToString = (date: Date | null): string => {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        return '';
    }
    
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
};
