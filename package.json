{"name": "app", "version": "0.0.8", "private": true, "type": "module", "dependencies": {"@1f/react-sdk": "0.0.0-20250120090223", "@ckeditor/ckeditor5-build-classic": "^44.3.0", "@ckeditor/ckeditor5-react": "^9.5.0", "@cyntler/react-doc-viewer": "^1.17.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/core": "^6.1.14", "@fullcalendar/daygrid": "^6.1.14", "@fullcalendar/interaction": "^6.1.14", "@fullcalendar/list": "^6.1.14", "@fullcalendar/react": "^6.1.14", "@fullcalendar/timegrid": "^6.1.14", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^5.17.1", "@mui/x-license-pro": "^6.10.2", "@mui/x-tree-view": "^7.28.1", "@types/lodash": "^4.17.0", "@vapor/react-x-data-grid": "^0.2.0", "@vapor/react-x-date-pickers": "^0.6.0", "chart.js": "^4.4.4", "chartjs-adapter-date-fns": "^3.0.0", "classnames": "^2.5.1", "dayjs": "^1.11.11", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "18.2.0", "react-chartjs-2": "^5.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-hook-form": "^7.50.1", "rete": "^2.0.5", "rete-area-plugin": "^2.0.5", "rete-connection-plugin": "^2.0.5", "rete-context-menu-plugin": "^2.0.4", "rete-history-plugin": "^2.0.2", "rete-react-plugin": "^2.0.7", "rete-render-utils": "^2.0.3", "sass": "^1.77.8", "yup": "^1.3.3"}, "devDependencies": {"@1f/vite-plugin": "0.0.0-20250120090223", "@types/react": "18.2.79", "@types/react-dom": "18.2.25", "@vitejs/plugin-react-swc": "^3.7.0", "rimraf": "^6.0.1", "typescript": "^5.5.4", "vite": "^5.3.4", "vite-plugin-svgr": "^4.2.0"}, "scripts": {"dev": "vite", "tsc": "tsc", "build": "tsc && vite build", "build-no-tsc": "vite build", "preview": "vite preview", "update:deps": "npx npm-check-updates --dep dev,prod,optional,peer --root --format group -i"}, "overrides": {"@vapor/v3-components": "^0.3.0", "@mui/styled-engine": "^6.1.8", "@mui/system": "^6.1.8", "@mui/material": "^6.1.8"}}