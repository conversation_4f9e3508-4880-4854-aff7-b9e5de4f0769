# DatePicker Usage Patterns

## Regola fondamentale
Il DatePicker del nostro ui-kit è un wrapper semplice attorno al componente Vapor DatePicker. NON contiene logica di processamento automatico delle date. Ogni componente che lo utilizza deve gestire correttamente i valori in input e output.

## Pattern corretti di utilizzo

### Pattern 1: Query con stringhe DD/MM/YYYY (più comune nei dashboard)
```tsx
// Quando il state contiene stringhe nel formato DD/MM/YYYY
const [query, setQuery] = useState({
    startDateSearch: "01/01/2024", // stringa DD/MM/YYYY
    endDateSearch: "31/12/2024"    // stringa DD/MM/YYYY
});

// Nel render:
<DatePicker
    label="Dal"
    value={query.startDateSearch ? moment(query.startDateSearch, "DD/MM/YYYY").toDate() : null}
    onChange={(date: Date | null) => {
        if (date) {
            setQuery(prev => ({
                ...prev,
                startDateSearch: moment(date).format("DD/MM/YYYY")
            }));
        }
    }}
/>
```

### Pattern 2: State con Date objects
```tsx
// Quando il state contiene già Date objects
const [query, setQuery] = useState({
    startDate: new Date(), // Date object
    endDate: new Date()    // Date object
});

// Nel render:
<DatePicker
    label="Dal"
    value={query.startDate}
    onChange={(date: Date | null) => {
        setQuery(prev => ({
            ...prev,
            startDate: date
        }));
    }}
/>
```

### Pattern 3: Form con react-hook-form
```tsx
// Con react-hook-form, i valori possono essere Date objects
<DatePicker
    label="Data di nascita"
    value={values.datanascita ? values.datanascita : null}
    onChange={(value: Date | null) => {
        setValue("datanascita", value);
    }}
/>
```

### Pattern 4: Callback passato come prop
```tsx
// Quando la gestione è delegata al componente genitore
<DatePicker
    label="Dal"
    name="startDate"
    value={query.startDate}
    onChange={onDateChange} // funzione passata dal genitore
/>
```

## ❌ Pattern SBAGLIATI da evitare

### Non fare mai questo:
```tsx
// SBAGLIATO: processamento automatico nel DatePicker stesso
export const DatePicker = (props) => {
    const processedValue = processDateValue(props.value); // ❌ NO!
    return <VDatePicker value={processedValue} ... />
}
```

### Non fare nemmeno questo:
```tsx
// SBAGLIATO: passare stringhe DD/MM/YYYY direttamente
<DatePicker
    value={query.startDateSearch} // ❌ se è una stringa DD/MM/YYYY
    onChange={...}
/>
```

### ⚠️ PROBLEMA CRITICO: Callback con parametri incompatibili
```tsx
// SBAGLIATO: quando onDateChange si aspetta (name: string, value: Date)
<DatePicker
    name="startDate"
    value={query.startDate}
    onChange={onDateChange} // ❌ onDateChange riceve solo (date: Date | null)
/>

// CORRETTO: wrapper per gestire la conversione
<DatePicker
    name="startDate"
    value={query.startDate ? moment(query.startDate, "DD/MM/YYYY").toDate() : null}
    onChange={(date: Date | null) => {
        if (date) {
            onDateChange('startDate', date); // ✅ Passa entrambi i parametri
        }
    }}
/>
```

## 📁 File corretti attualmente

### Dashboard (con conversione da stringhe DD/MM/YYYY):
- ✅ `/features/dashboard/dashboardFatture/Filters.tsx`
- ✅ `/features/dashboard/dashboardPratiche/Filter.tsx`
- ✅ `/features/dashboard/dashboardTimesheet/Filters.tsx`
- ✅ `/features/dashboard/dashboardIndex/Filter.tsx` (già corretto)

### Archive (con callback che richiede name e value):
- ✅ `/features/archive/tabs/Tab1.tsx` (corretto - usa callback wrapper)
- ✅ `/features/archive/tabs/Tab8.tsx` (corretto - usa callback wrapper)

### File che potrebbero essere già corretti (da verificare):
- `/features/dashboard/dashboardClienti/Filters.tsx` (usa Date objects)
- Molti file in `/features/anagrafiche/` (usano react-hook-form)
- File in `/features/archive/` (usano callback functions)

## 🚨 Action Items

1. **Analizzare tutti i restanti file** che usano DatePicker
2. **Applicare il pattern corretto** basato sul tipo di dato che gestiscono
3. **Testare funzionalità** per assicurarsi che i valori arrivino correttamente alle API
4. **Documentazione** per i futuri sviluppatori

## Import corretto
```tsx
import { DatePicker } from "../../../components/ui-kit/DatePicker";
import moment from "moment"; // se necessario per conversioni
```
